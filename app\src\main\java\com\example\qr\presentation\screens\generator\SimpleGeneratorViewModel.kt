package com.example.qr.presentation.screens.generator

import android.content.Context
import android.graphics.Bitmap
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.data.repository.PersistentQRRepository
import com.example.qr.data.repository.SettingsRepository
import com.example.qr.utils.QRCodeAnalyzer
import com.example.qr.utils.QRCodeGenerator
import com.example.qr.utils.QRCodeCustomization
import com.example.qr.utils.QRGenerationService
import com.example.qr.utils.QRGenerationResult
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Date

class SimpleGeneratorViewModel(private val context: Context) : ViewModel() {

    private val _uiState = MutableStateFlow(GeneratorUiState())
    val uiState: StateFlow<GeneratorUiState> = _uiState.asStateFlow()

    private val settingsRepository = SettingsRepository(context)
    private val qrRepository = PersistentQRRepository(context)

    fun generateQRCode(
        type: QRCodeType,
        content: String,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ) {
        if (content.isBlank()) {
            _uiState.value = GeneratorUiState(
                error = context.getString(R.string.error_content_empty)
            )
            return
        }

        _uiState.value = GeneratorUiState(isLoading = true, error = null)

        viewModelScope.launch {
            try {
                // Generate the actual content based on type
                val qrContent = when (type) {
                    QRCodeType.TEXT -> content
                    QRCodeType.URL -> if (content.startsWith("http")) content else "https://$content"
                    QRCodeType.EMAIL -> if (content.startsWith("mailto:")) content else "mailto:$content"
                    QRCodeType.PHONE -> if (content.startsWith("tel:")) content else "tel:$content"
                    QRCodeType.SMS -> if (content.startsWith("sms:")) content else "sms:$content"
                    else -> content
                }

                // Use the enhanced QR generation service
                val result = QRGenerationService.generateCompleteQRCode(
                    context = context,
                    type = type,
                    content = qrContent,
                    customization = customization,
                    overrideFormat = overrideFormat
                )

                when (result) {
                    is QRGenerationResult.Success -> {
                        // Save to repository if auto-save is enabled and not already saved to device
                        val savedQRCode = if (result.savedToDevice) {
                            val id = qrRepository.addQRCode(result.qrCodeData)
                            result.qrCodeData.copy(id = id)
                        } else {
                            // Check if auto-save to history is enabled
                            val autoSaveEnabled = settingsRepository.autoSaveGenerated.first()
                            if (autoSaveEnabled) {
                                val id = qrRepository.addQRCode(result.qrCodeData)
                                result.qrCodeData.copy(id = id)
                            } else {
                                result.qrCodeData
                            }
                        }

                        _uiState.value = GeneratorUiState(
                            generatedQRCode = savedQRCode,
                            generatedBitmap = result.bitmap,
                            showResult = true,
                            isLoading = false,
                            autoSaved = result.savedToDevice,
                            generatedFormat = result.format,
                            formatFeedback = if (result.savedToDevice) {
                                QRGenerationService.getFormatFeedbackMessage(
                                    result.format,
                                    result.fileName,
                                    result.filePath
                                )
                            } else null
                        )
                    }
                    is QRGenerationResult.Error -> {
                        _uiState.value = GeneratorUiState(
                            error = result.message,
                            isLoading = false
                        )
                    }
                }
            } catch (e: Exception) {
                _uiState.value = GeneratorUiState(
                    error = "Error generating QR code: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    fun generateWiFiQRCode(
        ssid: String,
        password: String,
        security: String,
        hidden: Boolean,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ) {
        val wifiContent = QRCodeGenerator.generateWiFiQRCode(
            ssid = ssid,
            password = password,
            security = when (security) {
                "WEP" -> com.example.qr.utils.WiFiSecurity.WEP
                "WPA" -> com.example.qr.utils.WiFiSecurity.WPA
                "WPA2" -> com.example.qr.utils.WiFiSecurity.WPA2
                else -> com.example.qr.utils.WiFiSecurity.WPA
            },
            hidden = hidden
        )

        generateQRCode(QRCodeType.WIFI, wifiContent, customization, overrideFormat)
    }

    fun generateEmailQRCode(
        email: String,
        subject: String,
        body: String,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ) {
        val emailContent = QRCodeGenerator.generateEmailQRCode(
            email = email,
            subject = subject,
            body = body
        )

        generateQRCode(QRCodeType.EMAIL, emailContent, customization, overrideFormat)
    }

    fun generateSMSQRCode(
        phone: String,
        message: String,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ) {
        val smsContent = QRCodeGenerator.generateSMSQRCode(
            phone = phone,
            message = message
        )

        generateQRCode(QRCodeType.SMS, smsContent, customization, overrideFormat)
    }

    fun generateContactQRCode(
        firstName: String,
        lastName: String,
        phone: String,
        email: String,
        organization: String,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ) {
        val contactContent = QRCodeGenerator.generateContactQRCode(
            firstName = firstName,
            lastName = lastName,
            phone = phone,
            email = email,
            organization = organization
        )

        generateQRCode(QRCodeType.CONTACT, contactContent, customization, overrideFormat)
    }

    fun generateLocationQRCode(
        latitude: Double,
        longitude: Double,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ) {
        val locationContent = QRCodeGenerator.generateLocationQRCode(
            latitude = latitude,
            longitude = longitude
        )

        generateQRCode(QRCodeType.LOCATION, locationContent, customization, overrideFormat)
    }

    fun toggleFavorite(qrCode: QRCodeData) {
        viewModelScope.launch {
            try {
                qrRepository.toggleFavorite(qrCode.id)
                val updatedQRCode = qrCode.copy(isFavorite = !qrCode.isFavorite)
                _uiState.value = GeneratorUiState(
                    generatedQRCode = updatedQRCode,
                    generatedBitmap = _uiState.value.generatedBitmap,
                    showResult = _uiState.value.showResult,
                    isLoading = _uiState.value.isLoading,
                    error = _uiState.value.error
                )
            } catch (e: Exception) {
                _uiState.value = GeneratorUiState(
                    generatedQRCode = _uiState.value.generatedQRCode,
                    generatedBitmap = _uiState.value.generatedBitmap,
                    showResult = _uiState.value.showResult,
                    isLoading = _uiState.value.isLoading,
                    error = "Failed to update favorite: ${e.message}"
                )
            }
        }
    }

    fun dismissResult() {
        _uiState.value = GeneratorUiState(
            showResult = false,
            generatedQRCode = null,
            generatedBitmap = null
        )
    }

    fun clearError() {
        _uiState.value = GeneratorUiState(
            generatedQRCode = _uiState.value.generatedQRCode,
            generatedBitmap = _uiState.value.generatedBitmap,
            showResult = _uiState.value.showResult,
            isLoading = _uiState.value.isLoading,
            error = null
        )
    }
}

data class GeneratorUiState(
    val isLoading: Boolean = false,
    val generatedQRCode: QRCodeData? = null,
    val generatedBitmap: Bitmap? = null,
    val showResult: Boolean = false,
    val error: String? = null,
    val autoSaved: Boolean = false,
    val generatedFormat: String? = null,
    val formatFeedback: String? = null
)
