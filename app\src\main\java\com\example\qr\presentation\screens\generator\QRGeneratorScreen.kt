package com.example.qr.presentation.screens.generator

import android.graphics.Bitmap
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.automirrored.filled.Message
import androidx.compose.material.icons.automirrored.filled.TextSnippet
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.utils.QRCodeCustomization
import com.example.qr.presentation.navigation.Screen
import com.example.qr.ui.theme.*
import com.example.qr.ui.components.GradientBackground
import com.example.qr.ui.components.ReactiveLanguageContent
import com.example.qr.ui.components.SparkActionCard
import com.example.qr.ui.components.QRResultBottomSheet
import com.example.qr.utils.ShareUtils
import com.example.qr.ui.theme.QRSparkTextStyles
import com.example.qr.utils.SafeAreaUtils

// Data classes and helper functions
data class QRTypeInfo(
    val type: QRCodeType,
    val title: String,
    val description: String,
    val icon: ImageVector
)

// Static version for non-Composable contexts
fun getQRCodeTypesStatic(): List<QRTypeInfo> = listOf(
    QRTypeInfo(QRCodeType.TEXT, "Text", "Plain text content", Icons.AutoMirrored.Filled.TextSnippet),
    QRTypeInfo(QRCodeType.URL, "Website", "Web URL or link", Icons.Default.Language),
    QRTypeInfo(QRCodeType.WIFI, "WiFi", "WiFi network credentials", Icons.Default.NetworkWifi),
    QRTypeInfo(QRCodeType.EMAIL, "Email", "Email address", Icons.Default.Email),
    QRTypeInfo(QRCodeType.PHONE, "Phone", "Phone number", Icons.Default.Phone),
    QRTypeInfo(QRCodeType.SMS, "SMS", "Text message", Icons.AutoMirrored.Filled.Message),
    QRTypeInfo(QRCodeType.CONTACT, "Contact", "Contact information", Icons.Default.Person),
    QRTypeInfo(QRCodeType.LOCATION, "Location", "Geographic coordinates", Icons.Default.LocationOn)
)

@Composable
fun getQRCodeTypes(): List<QRTypeInfo> = listOf(
    QRTypeInfo(QRCodeType.TEXT, stringResource(R.string.generator_text_title), stringResource(R.string.generator_text_description), Icons.AutoMirrored.Filled.TextSnippet),
    QRTypeInfo(QRCodeType.URL, stringResource(R.string.generator_url_title), stringResource(R.string.generator_url_description), Icons.Default.Language),
    QRTypeInfo(QRCodeType.WIFI, stringResource(R.string.generator_wifi_title), stringResource(R.string.generator_wifi_description), Icons.Default.NetworkWifi),
    QRTypeInfo(QRCodeType.EMAIL, stringResource(R.string.generator_email_title), stringResource(R.string.generator_email_description), Icons.Default.Email),
    QRTypeInfo(QRCodeType.PHONE, stringResource(R.string.generator_phone_title), stringResource(R.string.generator_phone_description), Icons.Default.Phone),
    QRTypeInfo(QRCodeType.SMS, stringResource(R.string.generator_sms_title), stringResource(R.string.generator_sms_description), Icons.AutoMirrored.Filled.Message),
    QRTypeInfo(QRCodeType.CONTACT, stringResource(R.string.generator_contact_title), stringResource(R.string.generator_contact_description), Icons.Default.Person),
    QRTypeInfo(QRCodeType.LOCATION, stringResource(R.string.generator_location_title), stringResource(R.string.generator_location_description), Icons.Default.LocationOn)
)

fun getQRTypeInfoStatic(type: QRCodeType): QRTypeInfo =
    getQRCodeTypesStatic().first { it.type == type }

@Composable
fun getQRTypeInfo(type: QRCodeType): QRTypeInfo =
    getQRCodeTypes().first { it.type == type }

@Composable
fun getQRTypeColor(type: QRCodeType): Color = when (type) {
    QRCodeType.TEXT -> GenerateColor
    QRCodeType.URL -> ScanColor
    QRCodeType.WIFI -> SparkGreen
    QRCodeType.EMAIL -> CollectionColor
    QRCodeType.PHONE -> SuccessGreen
    QRCodeType.SMS -> SparkPink
    QRCodeType.CONTACT -> InfoBlue
    QRCodeType.LOCATION -> WarningOrange
    else -> MaterialTheme.colorScheme.onSurfaceVariant
}

fun handleGeneration(
    type: QRCodeType,
    content: String,
    params: Map<String, Any>,
    viewModel: SimpleGeneratorViewModel
) {
    // Extract format override if provided
    val overrideFormat = params["overrideFormat"] as? String

    when (type) {
        QRCodeType.TEXT -> viewModel.generateQRCode(type, content, overrideFormat = overrideFormat)
        QRCodeType.URL -> viewModel.generateQRCode(type, content, overrideFormat = overrideFormat)
        QRCodeType.PHONE -> viewModel.generateQRCode(type, content, overrideFormat = overrideFormat)

        QRCodeType.WIFI -> {
            viewModel.generateWiFiQRCode(
                ssid = content,
                password = params["password"] as? String ?: "",
                security = params["security"] as? String ?: "WPA",
                hidden = params["hidden"] as? Boolean ?: false,
                overrideFormat = overrideFormat
            )
        }

        QRCodeType.EMAIL -> {
            viewModel.generateEmailQRCode(
                email = content,
                subject = params["subject"] as? String ?: "",
                body = params["body"] as? String ?: "",
                overrideFormat = overrideFormat
            )
        }

        QRCodeType.SMS -> {
            viewModel.generateSMSQRCode(
                phone = content,
                message = params["message"] as? String ?: "",
                overrideFormat = overrideFormat
            )
        }

        QRCodeType.CONTACT -> {
            viewModel.generateContactQRCode(
                firstName = content,
                lastName = params["lastName"] as? String ?: "",
                phone = params["phone"] as? String ?: "",
                email = params["email"] as? String ?: "",
                organization = params["organization"] as? String ?: "",
                overrideFormat = overrideFormat
            )
        }

        QRCodeType.LOCATION -> {
            val longitude = params["longitude"] as? String ?: "0.0"
            viewModel.generateLocationQRCode(
                latitude = content.toDoubleOrNull() ?: 0.0,
                longitude = longitude.toDoubleOrNull() ?: 0.0,
                overrideFormat = overrideFormat
            )
        }

        else -> viewModel.generateQRCode(type, content, overrideFormat = overrideFormat)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRGeneratorScreen(
    navController: NavController
) {
    ReactiveLanguageContent {
        QRGeneratorScreenContent(navController)
    }
}

@Composable
private fun QRGeneratorScreenContent(
    navController: NavController
) {
    val context = LocalContext.current
    val viewModel: SimpleGeneratorViewModel = viewModel { SimpleGeneratorViewModel(context) }
    var selectedType by remember { mutableStateOf<QRCodeType?>(null) }
    val uiState by viewModel.uiState.collectAsState()

    // Show error snackbar
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            // Show snackbar or toast
            viewModel.clearError()
        }
    }

    GradientBackground(applyScreenSafeArea = false) {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.displayCutout),
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            stringResource(R.string.generator_title),
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.SemiBold
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = {
                            navController.navigate(Screen.Home.route) {
                                popUpTo(Screen.Home.route) { inclusive = true }
                            }
                        }) {
                            Icon(
                                Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent
                    ),
                    windowInsets = WindowInsets.statusBars
                )
            },
            containerColor = Color.Transparent,
            contentWindowInsets = WindowInsets.safeDrawing
        ) { paddingValues ->
        if (selectedType == null) {
            QRTypeSelectionScreen(
                modifier = Modifier.padding(paddingValues),
                onTypeSelected = { selectedType = it }
            )
        } else {
            QRGeneratorForm(
                type = selectedType!!,
                modifier = Modifier.padding(paddingValues),
                onBack = { selectedType = null },
                onGenerate = { content, params ->
                    handleGeneration(selectedType!!, content, params, viewModel)
                },
                isLoading = uiState.isLoading
            )
        }
        }

        // Show QR code result bottom sheet
        if (uiState.showResult && uiState.generatedQRCode != null && uiState.generatedBitmap != null) {
            QRResultBottomSheet(
                qrCode = uiState.generatedQRCode!!,
                bitmap = uiState.generatedBitmap,
                autoSaved = uiState.autoSaved,
                onDismiss = { viewModel.dismissResult() },
                onToggleFavorite = { viewModel.toggleFavorite(uiState.generatedQRCode!!) },
                generatedFormat = uiState.generatedFormat,
                formatFeedback = uiState.formatFeedback
            )
        }
    }
}

@Composable
fun QRTypeSelectionScreen(
    modifier: Modifier = Modifier,
    onTypeSelected: (QRCodeType) -> Unit
) {
    val qrCodeTypes = getQRCodeTypes() // Call Composable function here

    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .windowInsetsPadding(WindowInsets.navigationBars)
            .windowInsetsPadding(WindowInsets.ime)
            .padding(SafeAreaUtils.responsiveContentPadding()),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = SafeAreaUtils.responsiveContentPadding()
    ) {
        item {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = stringResource(R.string.generator_select_type),
                    style = QRSparkTextStyles.heroTitle,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(R.string.generator_select_type_desc),
                    style = QRSparkTextStyles.heroSubtitle,
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(32.dp))
            }
        }

        items(qrCodeTypes) { typeInfo ->
            SparkQRTypeCard(
                typeInfo = typeInfo,
                onClick = { onTypeSelected(typeInfo.type) }
            )
        }
    }
}

@Composable
fun SparkQRTypeCard(
    typeInfo: QRTypeInfo,
    onClick: () -> Unit
) {
    SparkActionCard(
        title = typeInfo.title,
        subtitle = typeInfo.description,
        icon = {
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = getQRTypeColor(typeInfo.type).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = typeInfo.icon,
                    contentDescription = typeInfo.title,
                    tint = getQRTypeColor(typeInfo.type),
                    modifier = Modifier.size(24.dp)
                )
            }
        },
        onClick = onClick,
        trailingIcon = {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Select",
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(24.dp)
            )
        }
    )
}

@Composable
fun QRGeneratorForm(
    type: QRCodeType,
    modifier: Modifier = Modifier,
    onBack: () -> Unit,
    onGenerate: (String, Map<String, Any>) -> Unit,
    isLoading: Boolean = false
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(24.dp)
    ) {
        // Spark-styled header
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(bottom = 32.dp)
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
            Column {
                Text(
                    text = "Generate ${getQRTypeInfo(type).title}",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = getQRTypeInfo(type).description,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = getQRTypeColor(type)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = stringResource(R.string.generator_generating),
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        } else {
            when (type) {
                QRCodeType.TEXT -> SparkTextQRForm(type = type, onGenerate = onGenerate)
                QRCodeType.URL -> URLQRForm(onGenerate = onGenerate)
                QRCodeType.WIFI -> WiFiQRForm(onGenerate = onGenerate)
                QRCodeType.EMAIL -> EmailQRForm(onGenerate = onGenerate)
                QRCodeType.PHONE -> PhoneQRForm(onGenerate = onGenerate)
                QRCodeType.SMS -> SMSQRForm(onGenerate = onGenerate)
                QRCodeType.CONTACT -> ContactQRForm(onGenerate = onGenerate)
                QRCodeType.LOCATION -> LocationQRForm(onGenerate = onGenerate)
                else -> SparkTextQRForm(type = type, onGenerate = onGenerate)
            }
        }
    }
}

@Composable
fun SparkTextQRForm(
    type: QRCodeType,
    onGenerate: (String, Map<String, Any>) -> Unit
) {
    var text by remember { mutableStateOf("") }

    Column(verticalArrangement = Arrangement.spacedBy(24.dp)) {
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = stringResource(R.string.generator_text_content),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.padding(bottom = 12.dp)
                )

                OutlinedTextField(
                    value = text,
                    onValueChange = { text = it },
                    label = { Text(stringResource(R.string.generator_text_label)) },
                    placeholder = { Text(stringResource(R.string.generator_text_placeholder)) },
                    modifier = Modifier.fillMaxWidth(),
                    minLines = 4,
                    maxLines = 8,
                    shape = RoundedCornerShape(12.dp),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = getQRTypeColor(type),
                        focusedLabelColor = getQRTypeColor(type)
                    )
                )
            }
        }

        Button(
            onClick = { onGenerate(text, emptyMap()) },
            enabled = text.isNotBlank(),
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = getQRTypeColor(type)
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Icon(
                imageVector = Icons.Default.QrCode,
                contentDescription = null,
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                stringResource(R.string.generator_generate_button),
                style = QRSparkTextStyles.buttonLarge
            )
        }
    }
}

