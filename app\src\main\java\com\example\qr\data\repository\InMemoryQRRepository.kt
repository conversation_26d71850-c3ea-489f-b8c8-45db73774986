package com.example.qr.data.repository

import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Date

object InMemoryQRRepository {
    private val _qrCodes = MutableStateFlow<List<QRCodeData>>(emptyList())
    val qrCodes: StateFlow<List<QRCodeData>> = _qrCodes.asStateFlow()
    
    private var nextId = 1L
    
    fun addQRCode(qrCode: QRCodeData): Long {
        val id = nextId++
        val qrCodeWithId = qrCode.copy(id = id)
        _qrCodes.value = _qrCodes.value + qrCodeWithId
        return id
    }
    
    fun updateQRCode(qrCode: QRCodeData) {
        _qrCodes.value = _qrCodes.value.map { 
            if (it.id == qrCode.id) qrCode else it 
        }
    }
    
    fun deleteQRCode(qrCode: QRCodeData) {
        _qrCodes.value = _qrCodes.value.filter { it.id != qrCode.id }
    }
    
    fun deleteQRCodeById(id: Long) {
        _qrCodes.value = _qrCodes.value.filter { it.id != id }
    }
    
    fun toggleFavorite(id: Long) {
        _qrCodes.value = _qrCodes.value.map { qrCode ->
            if (qrCode.id == id) {
                qrCode.copy(isFavorite = !qrCode.isFavorite)
            } else {
                qrCode
            }
        }
    }
    
    fun getAllQRCodes(): List<QRCodeData> = _qrCodes.value
    
    fun getScannedQRCodes(): List<QRCodeData> = _qrCodes.value.filter { !it.isGenerated }
    
    fun getGeneratedQRCodes(): List<QRCodeData> = _qrCodes.value.filter { it.isGenerated }
    
    fun getFavoriteQRCodes(): List<QRCodeData> = _qrCodes.value.filter { it.isFavorite }
    
    fun searchQRCodes(query: String): List<QRCodeData> {
        return if (query.isBlank()) {
            _qrCodes.value
        } else {
            _qrCodes.value.filter { qrCode ->
                qrCode.content.contains(query, ignoreCase = true) ||
                qrCode.displayName.contains(query, ignoreCase = true)
            }
        }
    }
    
    fun clearAll() {
        _qrCodes.value = emptyList()
        nextId = 1L
    }
    
    fun deleteNonFavorites() {
        _qrCodes.value = _qrCodes.value.filter { it.isFavorite }
    }
    
    // Initialize with some sample data
    fun initializeSampleData() {
        if (_qrCodes.value.isEmpty()) {
            val sampleData = listOf(
                QRCodeData(
                    content = "https://www.google.com",
                    type = QRCodeType.URL,
                    format = "URL",
                    displayName = "Google",
                    createdAt = Date(),
                    isGenerated = false,
                    isFavorite = true
                ),
                QRCodeData(
                    content = "Hello World!",
                    type = QRCodeType.TEXT,
                    format = "TEXT",
                    displayName = "Hello World!",
                    createdAt = Date(System.currentTimeMillis() - 86400000),
                    isGenerated = true,
                    isFavorite = false
                )
            )
            
            sampleData.forEach { addQRCode(it) }
        }
    }
}
