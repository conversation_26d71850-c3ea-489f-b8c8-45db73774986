package com.example.qr.utils

import android.graphics.Bitmap
import android.graphics.Color
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.WriterException
import com.google.zxing.common.BitMatrix
import com.google.zxing.qrcode.QRCodeWriter
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel

object QRCodeGenerator {
    
    fun generateQRCode(
        content: String,
        width: Int = 512,
        height: Int = 512,
        foregroundColor: Int = Color.BLACK,
        backgroundColor: Int = Color.WHITE,
        errorCorrectionLevel: ErrorCorrectionLevel = ErrorCorrectionLevel.M
    ): Bitmap? {
        return try {
            val writer = QRCodeWriter()
            val hints = hashMapOf<EncodeHintType, Any>().apply {
                put(EncodeHintType.ERROR_CORRECTION, errorCorrectionLevel)
                put(EncodeHintType.CHARACTER_SET, "UTF-8")
                put(EncodeHintType.MARGIN, 1)
            }
            
            val bitMatrix: BitMatrix = writer.encode(content, BarcodeFormat.QR_CODE, width, height, hints)
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
            
            for (x in 0 until width) {
                for (y in 0 until height) {
                    bitmap.setPixel(x, y, if (bitMatrix[x, y]) foregroundColor else backgroundColor)
                }
            }
            
            bitmap
        } catch (e: WriterException) {
            e.printStackTrace()
            null
        }
    }
    
    fun generateWiFiQRCode(
        ssid: String,
        password: String,
        security: WiFiSecurity = WiFiSecurity.WPA,
        hidden: Boolean = false
    ): String {
        return "WIFI:T:${security.value};S:$ssid;P:$password;H:${if (hidden) "true" else "false"};;"
    }
    
    fun generateContactQRCode(
        firstName: String,
        lastName: String = "",
        phone: String = "",
        email: String = "",
        organization: String = "",
        url: String = ""
    ): String {
        return buildString {
            appendLine("BEGIN:VCARD")
            appendLine("VERSION:3.0")
            appendLine("FN:$firstName $lastName".trim())
            if (firstName.isNotEmpty()) appendLine("N:$lastName;$firstName;;;")
            if (phone.isNotEmpty()) appendLine("TEL:$phone")
            if (email.isNotEmpty()) appendLine("EMAIL:$email")
            if (organization.isNotEmpty()) appendLine("ORG:$organization")
            if (url.isNotEmpty()) appendLine("URL:$url")
            appendLine("END:VCARD")
        }
    }
    
    fun generateEmailQRCode(
        email: String,
        subject: String = "",
        body: String = ""
    ): String {
        return buildString {
            append("mailto:$email")
            val params = mutableListOf<String>()
            if (subject.isNotEmpty()) params.add("subject=$subject")
            if (body.isNotEmpty()) params.add("body=$body")
            if (params.isNotEmpty()) {
                append("?${params.joinToString("&")}")
            }
        }
    }
    
    fun generateSMSQRCode(
        phone: String,
        message: String = ""
    ): String {
        return if (message.isNotEmpty()) {
            "sms:$phone?body=$message"
        } else {
            "sms:$phone"
        }
    }
    
    fun generateLocationQRCode(
        latitude: Double,
        longitude: Double,
        altitude: Double? = null
    ): String {
        return if (altitude != null) {
            "geo:$latitude,$longitude,$altitude"
        } else {
            "geo:$latitude,$longitude"
        }
    }
}

enum class WiFiSecurity(val value: String) {
    NONE("nopass"),
    WEP("WEP"),
    WPA("WPA"),
    WPA2("WPA2")
}

data class QRCodeCustomization(
    val foregroundColor: Int = Color.BLACK,
    val backgroundColor: Int = Color.WHITE,
    val size: Int? = null, // No default size - will use user's preference
    val errorCorrectionLevel: ErrorCorrectionLevel = ErrorCorrectionLevel.M,
    val logoPath: String? = null,
    val logoSize: Float = 0.2f // Percentage of QR code size
)
