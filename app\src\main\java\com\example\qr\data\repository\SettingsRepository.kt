package com.example.qr.data.repository

import android.content.Context
import com.example.qr.data.preferences.AppSettings
import com.example.qr.data.preferences.SettingsDataStore
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SettingsRepository @Inject constructor(
    private val context: Context
) {
    private val settingsDataStore = SettingsDataStore(context)

    // Expose all settings as flows
    val allSettings: Flow<AppSettings> = settingsDataStore.allSettings
    val autoSaveScanned: Flow<Boolean> = settingsDataStore.autoSaveScanned
    val vibrateOnScan: Flow<Boolean> = settingsDataStore.vibrateOnScan
    val playSoundOnScan: Flow<Boolean> = settingsDataStore.playSoundOnScan
    val autoOpenLinks: Flow<Boolean> = settingsDataStore.autoOpenLinks
    val autoSaveGenerated: Flow<Boolean> = settingsDataStore.autoSaveGenerated
    val defaultQRSize: Flow<String> = settingsDataStore.defaultQRSize
    val defaultQRFormat: Flow<String> = settingsDataStore.defaultQRFormat
    val themeMode: Flow<String> = settingsDataStore.themeMode
    val showTutorials: Flow<Boolean> = settingsDataStore.showTutorials
    val enableAnimations: Flow<Boolean> = settingsDataStore.enableAnimations
    val analyticsEnabled: Flow<Boolean> = settingsDataStore.analyticsEnabled
    val crashReportingEnabled: Flow<Boolean> = settingsDataStore.crashReportingEnabled
    val selectedLanguage: Flow<String> = settingsDataStore.selectedLanguage
    val isFirstTimeLanguageSelection: Flow<Boolean> = settingsDataStore.isFirstTimeLanguageSelection

    // Update methods
    suspend fun setAutoSaveScanned(enabled: Boolean) = settingsDataStore.setAutoSaveScanned(enabled)
    suspend fun setVibrateOnScan(enabled: Boolean) = settingsDataStore.setVibrateOnScan(enabled)
    suspend fun setPlaySoundOnScan(enabled: Boolean) = settingsDataStore.setPlaySoundOnScan(enabled)
    suspend fun setAutoOpenLinks(enabled: Boolean) = settingsDataStore.setAutoOpenLinks(enabled)
    suspend fun setAutoSaveGenerated(enabled: Boolean) = settingsDataStore.setAutoSaveGenerated(enabled)
    suspend fun setDefaultQRSize(size: String) = settingsDataStore.setDefaultQRSize(size)
    suspend fun setDefaultQRFormat(format: String) = settingsDataStore.setDefaultQRFormat(format)
    suspend fun setThemeMode(mode: String) = settingsDataStore.setThemeMode(mode)
    suspend fun setShowTutorials(enabled: Boolean) = settingsDataStore.setShowTutorials(enabled)
    suspend fun setEnableAnimations(enabled: Boolean) = settingsDataStore.setEnableAnimations(enabled)
    suspend fun setAnalyticsEnabled(enabled: Boolean) = settingsDataStore.setAnalyticsEnabled(enabled)
    suspend fun setCrashReportingEnabled(enabled: Boolean) = settingsDataStore.setCrashReportingEnabled(enabled)
    suspend fun setSelectedLanguage(languageCode: String) = settingsDataStore.setSelectedLanguage(languageCode)
    suspend fun setFirstTimeLanguageSelection(isFirstTime: Boolean) = settingsDataStore.setFirstTimeLanguageSelection(isFirstTime)

    // Utility methods for getting current values synchronously when needed
    fun getQRSizePixels(size: String): Int {
        return when (size) {
            "Small" -> 256
            "Medium" -> 512
            "Large" -> 768
            "Extra Large" -> 1024
            else -> 512 // Default to Medium
        }
    }

    fun getQRFormatExtension(format: String): String {
        return when (format) {
            "PNG" -> ".png"
            "JPEG" -> ".jpg"
            "PDF" -> ".pdf"
            else -> ".png" // Default to PNG
        }
    }

    fun getQRFormatMimeType(format: String): String {
        return when (format) {
            "PNG" -> "image/png"
            "JPEG" -> "image/jpeg"
            "PDF" -> "application/pdf"
            else -> "image/png" // Default to PNG
        }
    }
}

// Extension functions for easy access
suspend fun SettingsRepository.getCurrentSettings(): AppSettings {
    var currentSettings: AppSettings? = null
    allSettings.collect { settings ->
        currentSettings = settings
        return@collect
    }
    return currentSettings ?: AppSettings()
}
