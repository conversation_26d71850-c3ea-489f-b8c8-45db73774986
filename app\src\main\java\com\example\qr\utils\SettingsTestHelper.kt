package com.example.qr.utils

import android.content.Context
import android.util.Log
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking

/**
 * Helper class to test and verify settings functionality
 */
object SettingsTestHelper {
    
    private const val TAG = "SettingsTestHelper"
    
    /**
     * Test all settings functionality
     */
    fun testAllSettings(context: Context) {
        val repository = SettingsRepository(context)
        
        runBlocking {
            try {
                Log.d(TAG, "=== Settings Integration Test ===")
                
                // Test reading current settings
                val currentSettings = repository.allSettings.first()
                Log.d(TAG, "Current settings loaded: $currentSettings")
                
                // Test scanning settings
                testScanningSettings(repository)
                
                // Test generation settings
                testGenerationSettings(repository)
                
                // Test UI settings
                testUISettings(repository)
                
                Log.d(TAG, "=== All Settings Tests Completed Successfully ===")
                
            } catch (e: Exception) {
                Log.e(TAG, "Settings test failed", e)
            }
        }
    }
    
    private suspend fun testScanningSettings(repository: SettingsRepository) {
        Log.d(TAG, "Testing scanning settings...")
        
        // Test auto-save scanned
        val originalAutoSave = repository.autoSaveScanned.first()
        repository.setAutoSaveScanned(!originalAutoSave)
        val newAutoSave = repository.autoSaveScanned.first()
        Log.d(TAG, "Auto-save scanned: $originalAutoSave -> $newAutoSave")
        repository.setAutoSaveScanned(originalAutoSave) // Restore
        
        // Test vibration
        val originalVibrate = repository.vibrateOnScan.first()
        repository.setVibrateOnScan(!originalVibrate)
        val newVibrate = repository.vibrateOnScan.first()
        Log.d(TAG, "Vibrate on scan: $originalVibrate -> $newVibrate")
        repository.setVibrateOnScan(originalVibrate) // Restore
        
        // Test sound
        val originalSound = repository.playSoundOnScan.first()
        repository.setPlaySoundOnScan(!originalSound)
        val newSound = repository.playSoundOnScan.first()
        Log.d(TAG, "Play sound on scan: $originalSound -> $newSound")
        repository.setPlaySoundOnScan(originalSound) // Restore
        
        // Test auto-open links
        val originalAutoOpen = repository.autoOpenLinks.first()
        repository.setAutoOpenLinks(!originalAutoOpen)
        val newAutoOpen = repository.autoOpenLinks.first()
        Log.d(TAG, "Auto-open links: $originalAutoOpen -> $newAutoOpen")
        repository.setAutoOpenLinks(originalAutoOpen) // Restore
        
        Log.d(TAG, "Scanning settings test completed")
    }
    
    private suspend fun testGenerationSettings(repository: SettingsRepository) {
        Log.d(TAG, "Testing generation settings...")
        
        // Test auto-save generated
        val originalAutoSaveGen = repository.autoSaveGenerated.first()
        repository.setAutoSaveGenerated(!originalAutoSaveGen)
        val newAutoSaveGen = repository.autoSaveGenerated.first()
        Log.d(TAG, "Auto-save generated: $originalAutoSaveGen -> $newAutoSaveGen")
        repository.setAutoSaveGenerated(originalAutoSaveGen) // Restore
        
        // Test default QR size
        val originalSize = repository.defaultQRSize.first()
        val testSize = if (originalSize == "Medium") "Large" else "Medium"
        repository.setDefaultQRSize(testSize)
        val newSize = repository.defaultQRSize.first()
        val sizePixels = repository.getQRSizePixels(newSize)
        Log.d(TAG, "Default QR size: $originalSize -> $newSize ($sizePixels pixels)")
        repository.setDefaultQRSize(originalSize) // Restore
        
        // Test default QR format
        val originalFormat = repository.defaultQRFormat.first()
        val testFormat = if (originalFormat == "PNG") "JPEG" else "PNG"
        repository.setDefaultQRFormat(testFormat)
        val newFormat = repository.defaultQRFormat.first()
        val formatExt = repository.getQRFormatExtension(newFormat)
        val formatMime = repository.getQRFormatMimeType(newFormat)
        Log.d(TAG, "Default QR format: $originalFormat -> $newFormat ($formatExt, $formatMime)")
        repository.setDefaultQRFormat(originalFormat) // Restore
        
        Log.d(TAG, "Generation settings test completed")
    }
    
    private suspend fun testUISettings(repository: SettingsRepository) {
        Log.d(TAG, "Testing UI settings...")
        
        // Test show tutorials
        val originalTutorials = repository.showTutorials.first()
        repository.setShowTutorials(!originalTutorials)
        val newTutorials = repository.showTutorials.first()
        Log.d(TAG, "Show tutorials: $originalTutorials -> $newTutorials")
        repository.setShowTutorials(originalTutorials) // Restore
        
        // Test enable animations
        val originalAnimations = repository.enableAnimations.first()
        repository.setEnableAnimations(!originalAnimations)
        val newAnimations = repository.enableAnimations.first()
        Log.d(TAG, "Enable animations: $originalAnimations -> $newAnimations")
        repository.setEnableAnimations(originalAnimations) // Restore
        
        // Test theme mode
        val originalTheme = repository.themeMode.first()
        val testTheme = when (originalTheme) {
            "Light" -> "Dark"
            "Dark" -> "System"
            else -> "Light"
        }
        repository.setThemeMode(testTheme)
        val newTheme = repository.themeMode.first()
        Log.d(TAG, "Theme mode: $originalTheme -> $newTheme")
        repository.setThemeMode(originalTheme) // Restore
        
        Log.d(TAG, "UI settings test completed")
    }
    
    /**
     * Log current settings state
     */
    fun logCurrentSettings(context: Context) {
        val repository = SettingsRepository(context)
        
        runBlocking {
            try {
                val settings = repository.allSettings.first()
                Log.d(TAG, "=== Current Settings State ===")
                Log.d(TAG, "Auto-save scanned: ${settings.autoSaveScanned}")
                Log.d(TAG, "Vibrate on scan: ${settings.vibrateOnScan}")
                Log.d(TAG, "Play sound on scan: ${settings.playSoundOnScan}")
                Log.d(TAG, "Auto-open links: ${settings.autoOpenLinks}")
                Log.d(TAG, "Auto-save generated: ${settings.autoSaveGenerated}")
                Log.d(TAG, "Default QR size: ${settings.defaultQRSize}")
                Log.d(TAG, "Default QR format: ${settings.defaultQRFormat}")
                Log.d(TAG, "Theme mode: ${settings.themeMode}")
                Log.d(TAG, "Show tutorials: ${settings.showTutorials}")
                Log.d(TAG, "Enable animations: ${settings.enableAnimations}")
                Log.d(TAG, "Analytics enabled: ${settings.analyticsEnabled}")
                Log.d(TAG, "Crash reporting enabled: ${settings.crashReportingEnabled}")
                Log.d(TAG, "=== End Settings State ===")
            } catch (e: Exception) {
                Log.e(TAG, "Error logging current settings", e)
            }
        }
    }
    
    /**
     * Verify settings persistence across app restarts
     */
    fun verifySettingsPersistence(context: Context) {
        val repository = SettingsRepository(context)
        
        runBlocking {
            try {
                Log.d(TAG, "=== Testing Settings Persistence ===")
                
                // Set unique test values
                repository.setAutoSaveScanned(false)
                repository.setVibrateOnScan(false)
                repository.setDefaultQRSize("Large")
                repository.setDefaultQRFormat("JPEG")
                
                // Wait a moment for persistence
                kotlinx.coroutines.delay(100)
                
                // Read back values
                val autoSave = repository.autoSaveScanned.first()
                val vibrate = repository.vibrateOnScan.first()
                val size = repository.defaultQRSize.first()
                val format = repository.defaultQRFormat.first()
                
                Log.d(TAG, "Persistence test results:")
                Log.d(TAG, "Auto-save scanned: $autoSave (expected: false)")
                Log.d(TAG, "Vibrate on scan: $vibrate (expected: false)")
                Log.d(TAG, "Default QR size: $size (expected: Large)")
                Log.d(TAG, "Default QR format: $format (expected: JPEG)")
                
                // Restore defaults
                repository.setAutoSaveScanned(true)
                repository.setVibrateOnScan(true)
                repository.setDefaultQRSize("Medium")
                repository.setDefaultQRFormat("PNG")
                
                Log.d(TAG, "=== Settings Persistence Test Completed ===")
                
            } catch (e: Exception) {
                Log.e(TAG, "Settings persistence test failed", e)
            }
        }
    }
}
