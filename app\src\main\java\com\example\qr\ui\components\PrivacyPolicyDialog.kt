package com.example.qr.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.qr.R
import com.example.qr.ui.theme.QRSparkTextStyles
import com.example.qr.ui.theme.SparkPink

@Composable
fun PrivacyPolicyDialog(
    onAccept: () -> Unit,
    onDecline: () -> Unit,
    isFirstTime: Boolean = true,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val scrollState = rememberScrollState()
    
    Dialog(
        onDismissRequest = { if (!isFirstTime) onDecline() },
        properties = DialogProperties(
            dismissOnBackPress = !isFirstTime,
            dismissOnClickOutside = !isFirstTime,
            usePlatformDefaultWidth = false
        )
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .fillMaxHeight(0.9f)
                .padding(16.dp),
            shape = RoundedCornerShape(24.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Security,
                        contentDescription = null,
                        tint = SparkPink,
                        modifier = Modifier.size(32.dp)
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = if (isFirstTime) 
                            stringResource(R.string.privacy_welcome_title)
                        else 
                            stringResource(R.string.privacy_policy_title),
                        style = QRSparkTextStyles.heroTitle.copy(fontSize = 22.sp),
                        color = MaterialTheme.colorScheme.onSurface,
                        fontWeight = FontWeight.Bold
                    )
                }
                
                Spacer(modifier = Modifier.height(16.dp))
                
                if (isFirstTime) {
                    Text(
                        text = stringResource(R.string.privacy_welcome_subtitle),
                        style = QRSparkTextStyles.cardSubtitle,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                }
                
                // Privacy Policy Content
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    shape = RoundedCornerShape(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                            .verticalScroll(scrollState)
                    ) {
                        PrivacyPolicyContent()
                    }
                }
                
                Spacer(modifier = Modifier.height(20.dp))
                
                // Action Buttons
                if (isFirstTime) {
                    FirstTimePrivacyButtons(
                        onAccept = onAccept,
                        onDecline = onDecline
                    )
                } else {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = onDecline
                        ) {
                            Text(
                                text = stringResource(R.string.action_close),
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun FirstTimePrivacyButtons(
    onAccept: () -> Unit,
    onDecline: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        // Accept Button
        Button(
            onClick = onAccept,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(
                containerColor = SparkPink,
                contentColor = Color.White
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Text(
                text = stringResource(R.string.privacy_accept_button),
                style = QRSparkTextStyles.buttonMedium,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(vertical = 4.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // Decline Button
        OutlinedButton(
            onClick = onDecline,
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.colorScheme.onSurfaceVariant
            ),
            shape = RoundedCornerShape(16.dp)
        ) {
            Text(
                text = stringResource(R.string.privacy_decline_button),
                style = QRSparkTextStyles.buttonMedium,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(vertical = 4.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = stringResource(R.string.privacy_decline_explanation),
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
private fun PrivacyPolicyContent() {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        PrivacySection(
            title = stringResource(R.string.privacy_overview_title),
            content = stringResource(R.string.privacy_overview_content)
        )
        
        PrivacySection(
            title = stringResource(R.string.privacy_permissions_title),
            content = stringResource(R.string.privacy_permissions_content)
        )
        
        PrivacySection(
            title = stringResource(R.string.privacy_data_storage_title),
            content = stringResource(R.string.privacy_data_storage_content)
        )
        
        PrivacySection(
            title = stringResource(R.string.privacy_optional_features_title),
            content = stringResource(R.string.privacy_optional_features_content)
        )
        
        PrivacySection(
            title = stringResource(R.string.privacy_data_security_title),
            content = stringResource(R.string.privacy_data_security_content)
        )
        
        PrivacySection(
            title = stringResource(R.string.privacy_user_rights_title),
            content = stringResource(R.string.privacy_user_rights_content)
        )
        
        PrivacySection(
            title = stringResource(R.string.privacy_contact_title),
            content = stringResource(R.string.privacy_contact_content)
        )
    }
}

@Composable
private fun PrivacySection(
    title: String,
    content: String
) {
    Column {
        Text(
            text = title,
            style = QRSparkTextStyles.cardTitle.copy(fontSize = 16.sp),
            color = MaterialTheme.colorScheme.onSurface,
            fontWeight = FontWeight.SemiBold
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = content,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            lineHeight = 20.sp
        )
    }
}
