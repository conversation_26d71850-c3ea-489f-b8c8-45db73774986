# QR Spark Privacy Policy

**Last Updated**: January 2025  
**Version**: 1.0

## Overview

QR Spark ("we," "our," or "the app") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, store, and protect your information when you use our QR code scanning and generation application.

## Information We Collect

### Information We DO NOT Collect by Default
- Personal identifying information (name, email, phone number)
- Location data or GPS coordinates
- Contact lists or address books
- Device identifiers or advertising IDs
- Browsing history or web activity
- Usage analytics or behavioral data

### Information We MAY Collect (Only with Your Explicit Consent)
- **Anonymous Crash Reports**: If you enable crash reporting, we collect anonymous technical data about app crashes to improve stability
- **Anonymous Usage Analytics**: If you enable analytics, we collect anonymous usage statistics to understand feature usage and improve the app

### Local Data Storage
- **QR Code Data**: QR codes you scan or generate are stored locally on your device
- **App Settings**: Your preferences and settings are stored locally
- **History and Favorites**: Your QR code history and favorites are stored locally
- **No Cloud Storage**: We do not store any of your data on external servers

## How We Use Your Information

### Local Data Usage
- **QR Code Storage**: To maintain your scan history and generated QR codes
- **App Functionality**: To remember your preferences and provide personalized experience
- **Offline Access**: All core features work without internet connection

### Optional Data Usage (Opt-in Only)
- **Crash Reports**: To identify and fix technical issues
- **Analytics**: To understand which features are most useful and improve the app

## Permissions Explained

### Required Permissions
- **Camera (android.permission.CAMERA)**: Essential for scanning QR codes using your device's camera
- **Internet (android.permission.INTERNET)**: Used for:
  - Opening URLs from scanned QR codes
  - Sharing QR codes via messaging apps
  - Optional analytics and crash reporting (if enabled)

### Storage Permissions
- **Read External Storage**: To access images for QR code scanning from gallery
- **Write External Storage (Android 9 and below)**: To save generated QR codes to your device
- **Modern Storage Access (Android 10+)**: Uses scoped storage for secure file access

### Additional Permissions
- **Vibrate (android.permission.VIBRATE)**: Provides haptic feedback when scanning QR codes (can be disabled)
- **Network State**: Checks internet connectivity for sharing features

## Data Security

### Local Security
- All data is encrypted using Android's built-in security features
- QR code data never leaves your device unless you explicitly share it
- App settings are stored securely using Android DataStore

### Network Security
- When enabled, analytics and crash reports are transmitted over encrypted HTTPS connections
- No personal or identifying information is included in any network transmissions

## Data Retention

### Local Data
- **QR Code History**: Stored until you delete individual items or clear all data
- **App Settings**: Stored until you reset settings or uninstall the app
- **User Control**: You can delete any or all local data at any time through the app

### Optional Remote Data
- **Crash Reports**: Automatically deleted after 90 days
- **Analytics Data**: Automatically deleted after 26 months
- **No Personal Data**: No personally identifiable information is ever stored remotely

## Your Rights and Controls

### Data Control
- **View Data**: Access all your stored QR codes and settings within the app
- **Delete Data**: Remove individual QR codes or clear all data
- **Export Data**: Share or backup your QR codes
- **Settings Control**: Modify all app preferences

### Privacy Controls
- **Opt-out**: Disable analytics and crash reporting at any time
- **Permission Management**: Control app permissions through Android settings
- **Data Deletion**: Request complete data deletion by contacting us

## Third-Party Services

### Services We Use
- **Google ML Kit**: For QR code detection (processes data locally on device)
- **Android CameraX**: For camera functionality (no data transmitted)

### Services We Don't Use
- We do not use advertising networks
- We do not use social media tracking
- We do not use third-party analytics by default
- We do not sell data to third parties

## Children's Privacy

QR Spark is safe for users of all ages. We do not:
- Knowingly collect personal information from children under 13
- Target advertising to children
- Require personal information for app functionality
- Share any user data with third parties

## International Users

### Data Processing
- All data processing occurs locally on your device
- Optional analytics are processed in accordance with applicable privacy laws
- We comply with GDPR, CCPA, and other international privacy regulations

### Your Rights
- **Access**: Request information about data we process
- **Rectification**: Correct any inaccurate data
- **Erasure**: Request deletion of your data
- **Portability**: Export your data in a readable format

## Changes to This Policy

### Notification
- We will notify users of significant privacy policy changes through the app
- Updated policies will be posted with a new "Last Updated" date
- Continued use of the app constitutes acceptance of updated policies

### Version Control
- Each policy version is numbered and dated
- Previous versions are available upon request

## Contact Information

### Privacy Inquiries
- **Email**: <EMAIL>
- **Subject Line**: "QR Spark Privacy Inquiry"
- **Response Time**: We respond to privacy inquiries within 72 hours

### Data Requests
- **Data Access**: Request a copy of your data
- **Data Deletion**: Request complete data removal
- **Privacy Concerns**: Report any privacy-related issues

## Legal Compliance

This privacy policy complies with:
- Google Play Store Developer Policy
- General Data Protection Regulation (GDPR)
- California Consumer Privacy Act (CCPA)
- Children's Online Privacy Protection Act (COPPA)
- Other applicable international privacy laws

---

**QR Spark - Your Privacy, Our Priority**

*This privacy policy is designed to be transparent, comprehensive, and user-friendly. We believe in giving you complete control over your data while providing the best possible QR code experience.*
