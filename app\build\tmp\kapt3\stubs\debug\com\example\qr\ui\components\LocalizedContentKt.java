package com.example.qr.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0011\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\u001a!\u0010\u0000\u001a\u00020\u00012\u0017\u0010\u0002\u001a\u0013\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u0003\u00a2\u0006\u0002\b\u0005H\u0007\u001a\u0012\u0010\u0006\u001a\u00020\u00072\b\b\u0001\u0010\b\u001a\u00020\tH\u0007\u001a+\u0010\u0006\u001a\u00020\u00072\b\b\u0001\u0010\n\u001a\u00020\t2\u0012\u0010\u000b\u001a\n\u0012\u0006\b\u0001\u0012\u00020\r0\f\"\u00020\rH\u0007\u00a2\u0006\u0002\u0010\u000e\u001a\u001b\u0010\u000f\u001a\u00020\u00012\u0011\u0010\u0002\u001a\r\u0012\u0004\u0012\u00020\u00010\u0010\u00a2\u0006\u0002\b\u0005H\u0007\u001a+\u0010\u0011\u001a\u00020\u00072\b\b\u0001\u0010\u0012\u001a\u00020\t2\u0012\u0010\u000b\u001a\n\u0012\u0006\b\u0001\u0012\u00020\r0\f\"\u00020\rH\u0007\u00a2\u0006\u0002\u0010\u000e\u001a\b\u0010\u0013\u001a\u00020\u0014H\u0007\u001a\u0014\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0014\u0012\u0004\u0012\u00020\u00010\u0003H\u0007\u00a8\u0006\u0016"}, d2 = {"LocalizedContent", "", "content", "Lkotlin/Function1;", "Landroid/content/Context;", "Landroidx/compose/runtime/Composable;", "LocalizedString", "", "resId", "", "stringRes", "formatArgs", "", "", "(I[Ljava/lang/Object;)Ljava/lang/String;", "ReactiveLanguageContent", "Lkotlin/Function0;", "reactiveStringResource", "id", "rememberCurrentLanguage", "Lcom/example/qr/data/model/Language;", "rememberLanguageChanger", "app_debug"})
public final class LocalizedContentKt {
    
    /**
     * A composable that provides localized context and automatically recomposes
     * when the language changes, enabling immediate language switching without activity restart.
     */
    @androidx.compose.runtime.Composable()
    public static final void LocalizedContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super android.content.Context, kotlin.Unit> content) {
    }
    
    /**
     * Hook to get the current language state for immediate access
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final com.example.qr.data.model.Language rememberCurrentLanguage() {
        return null;
    }
    
    /**
     * Hook to trigger immediate language change
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final kotlin.jvm.functions.Function1<com.example.qr.data.model.Language, kotlin.Unit> rememberLanguageChanger() {
        return null;
    }
    
    /**
     * A composable that provides localized strings and automatically updates when language changes
     * This replaces the standard stringResource() to make it reactive to language changes
     * Optimized to reduce memory allocations and improve performance
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String LocalizedString(@androidx.annotation.StringRes()
    int stringRes, @org.jetbrains.annotations.NotNull()
    java.lang.Object... formatArgs) {
        return null;
    }
    
    /**
     * Reactive version of stringResource that updates when language changes
     * Use this instead of the standard stringResource() for immediate language switching
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String reactiveStringResource(@androidx.annotation.StringRes()
    int id, @org.jetbrains.annotations.NotNull()
    java.lang.Object... formatArgs) {
        return null;
    }
    
    /**
     * A composable that wraps content and ensures it recomposes when language changes
     * This should be used to wrap screens that need immediate language updates
     * Optimized to reduce excessive recompositions
     */
    @androidx.compose.runtime.Composable()
    public static final void ReactiveLanguageContent(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> content) {
    }
    
    /**
     * A composable that provides localized strings that update immediately when language changes
     * This ensures strings are always in the current language
     * Optimized for better performance
     */
    @androidx.compose.runtime.Composable()
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String LocalizedString(@androidx.annotation.StringRes()
    int resId) {
        return null;
    }
}