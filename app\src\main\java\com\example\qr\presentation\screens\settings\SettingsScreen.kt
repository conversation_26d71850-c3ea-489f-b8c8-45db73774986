package com.example.qr.presentation.screens.settings

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.*
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.layout.safeDrawing
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.displayCutout
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.OpenInNew
import androidx.compose.material.icons.automirrored.filled.VolumeUp
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.qr.R
import com.example.qr.presentation.navigation.Screen
import com.example.qr.ui.theme.*
import com.example.qr.ui.components.GradientBackground
import com.example.qr.ui.components.EnhancedToggleSwitch
import com.example.qr.ui.components.SparkActionCard
import com.example.qr.ui.components.LanguageSelectionDialog
import com.example.qr.utils.ShareUtils
import com.example.qr.ui.theme.QRSparkTextStyles
import com.example.qr.data.model.Language
import com.example.qr.utils.LanguageManager
import com.example.qr.ui.components.LocalizedContent
import com.example.qr.ui.components.ReactiveLanguageContent
import com.example.qr.ui.components.reactiveStringResource
import com.example.qr.ui.components.InfoDialog
import com.example.qr.ui.components.SettingsInfoDialog
import com.example.qr.ui.components.SettingInfoType
import com.example.qr.utils.AnimationManager
import android.util.Log

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    viewModel: SettingsViewModel = viewModel()
) {
    ReactiveLanguageContent {
        SettingsScreenContent(navController, viewModel)
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SettingsScreenContent(
    navController: NavController,
    viewModel: SettingsViewModel
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val settings by viewModel.settings.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()

    // Watch for language changes to trigger immediate recomposition
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    // State for language change feedback
    var showLanguageChangeSuccess by remember { mutableStateOf(false) }

    // Info dialog states with debounce protection
    var showInfoDialog by remember { mutableStateOf<SettingInfoType?>(null) }
    var lastDialogOpenTime by remember { mutableStateOf(0L) }

    // Helper function to open info dialog with debounce
    val openInfoDialog = { infoType: SettingInfoType ->
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastDialogOpenTime > 500) { // 500ms debounce
            showInfoDialog = infoType
            lastDialogOpenTime = currentTime
        }
    }

    // Track if this is the initial load
    var isInitialLoad by remember { mutableStateOf(true) }

    // Track language changes for success feedback (but don't force aggressive recomposition)
    LaunchedEffect(currentLanguage) {
        Log.d("SettingsScreen", "Language changed to: ${currentLanguage.nativeName}")

        // Show brief success indicator for language changes (skip initial load)
        if (!isInitialLoad) {
            showLanguageChangeSuccess = true
            kotlinx.coroutines.delay(2000) // Show for 2 seconds
            showLanguageChangeSuccess = false
        } else {
            isInitialLoad = false
        }
    }

    GradientBackground {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.displayCutout),
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            reactiveStringResource(R.string.settings_title),
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.SemiBold
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = {
                            navController.navigate(Screen.Home.route) {
                                popUpTo(Screen.Home.route) { inclusive = true }
                            }
                        }) {
                            Icon(
                                Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = reactiveStringResource(R.string.back),
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent
                    ),
                    windowInsets = WindowInsets.statusBars
                )
            },
            containerColor = Color.Transparent,
            contentWindowInsets = WindowInsets.safeDrawing
        ) { paddingValues ->
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .windowInsetsPadding(WindowInsets.navigationBars)
                    .windowInsetsPadding(WindowInsets.ime)
                    .padding(horizontal = 16.dp)
                    .padding(top = 8.dp, bottom = 16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                contentPadding = PaddingValues(
                    start = 8.dp,
                    end = 8.dp,
                    bottom = 24.dp
                )
            ) {
                item {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = reactiveStringResource(R.string.settings_app_settings_title),
                            style = QRSparkTextStyles.heroTitle,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = reactiveStringResource(R.string.settings_app_settings_subtitle),
                            style = QRSparkTextStyles.heroSubtitle,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Spacer(modifier = Modifier.height(32.dp))
                    }
                }

                item {
                    SettingsSection(title = reactiveStringResource(R.string.settings_scanning), showDivider = false) {
                        SettingsItem(
                            title = reactiveStringResource(R.string.settings_auto_save_scanned),
                            subtitle = reactiveStringResource(R.string.settings_auto_save_scanned_desc),
                            icon = Icons.Default.Save,
                            iconColor = ScanColor,
                            showInfoIcon = true,
                            onInfoClick = { openInfoDialog(SettingInfoType.AUTO_SAVE_SCANNED) },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.autoSaveScanned,
                                    onCheckedChange = { viewModel.setAutoSaveScanned(it) },
                                    enabled = !isLoading,
                                    iconColor = ScanColor
                                )
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_vibrate_on_scan),
                            subtitle = stringResource(R.string.settings_vibrate_on_scan_desc),
                            icon = Icons.Default.Vibration,
                            iconColor = SparkGreen,
                            showInfoIcon = true,
                            onInfoClick = {
                                showInfoDialog = SettingInfoType.VIBRATE_ON_SCAN
                            },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.vibrateOnScan,
                                    onCheckedChange = { viewModel.setVibrateOnScan(it) },
                                    enabled = !isLoading,
                                    iconColor = SparkGreen
                                )
                            }
                        )

                        SettingsItem(
                            title = reactiveStringResource(R.string.settings_play_sound_on_scan),
                            subtitle = reactiveStringResource(R.string.settings_play_sound_on_scan_desc),
                            icon = Icons.AutoMirrored.Filled.VolumeUp,
                            iconColor = InfoBlue,
                            showInfoIcon = true,
                            onInfoClick = {
                                showInfoDialog = SettingInfoType.PLAY_SOUND_ON_SCAN
                            },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.playSoundOnScan,
                                    onCheckedChange = { viewModel.setPlaySoundOnScan(it) },
                                    enabled = !isLoading,
                                    iconColor = InfoBlue
                                )
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_auto_open_links),
                            subtitle = stringResource(R.string.settings_auto_open_links_desc),
                            icon = Icons.AutoMirrored.Filled.OpenInNew,
                            iconColor = WarningOrange,
                            showInfoIcon = true,
                            onInfoClick = {
                                showInfoDialog = SettingInfoType.AUTO_OPEN_LINKS
                            },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.autoOpenLinks,
                                    onCheckedChange = { viewModel.setAutoOpenLinks(it) },
                                    enabled = !isLoading,
                                    iconColor = WarningOrange
                                )
                            }
                        )
                    }
                }

                item {
                    SettingsSection(title = stringResource(R.string.settings_generation)) {
                        SettingsItem(
                            title = stringResource(R.string.settings_default_qr_size),
                            subtitle = stringResource(R.string.settings_default_qr_size_desc),
                            icon = Icons.Default.AspectRatio,
                            iconColor = GenerateColor,
                            trailing = {
                                var showSizeDialog by remember { mutableStateOf(false) }

                                TextButton(
                                    onClick = { showSizeDialog = true },
                                    enabled = !isLoading
                                ) {
                                    Text(
                                        settings.defaultQRSize,
                                        color = GenerateColor,
                                        fontWeight = FontWeight.Medium
                                    )
                                }

                                if (showSizeDialog) {
                                    SizeSelectionDialog(
                                        currentSize = settings.defaultQRSize,
                                        onSizeSelected = { size ->
                                            viewModel.setDefaultQRSize(size)
                                            showSizeDialog = false
                                        },
                                        onDismiss = { showSizeDialog = false }
                                    )
                                }
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_default_qr_format),
                            subtitle = stringResource(R.string.settings_default_qr_format_desc),
                            icon = Icons.Default.Image,
                            iconColor = SparkPink,
                            trailing = {
                                var showFormatDialog by remember { mutableStateOf(false) }

                                TextButton(
                                    onClick = { showFormatDialog = true },
                                    enabled = !isLoading
                                ) {
                                    Text(
                                        settings.defaultQRFormat,
                                        color = SparkPink,
                                        fontWeight = FontWeight.Medium
                                    )
                                }

                                if (showFormatDialog) {
                                    FormatSelectionDialog(
                                        currentFormat = settings.defaultQRFormat,
                                        onFormatSelected = { format ->
                                            viewModel.setDefaultQRFormat(format)
                                            showFormatDialog = false
                                        },
                                        onDismiss = { showFormatDialog = false }
                                    )
                                }
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_auto_save_generated),
                            subtitle = stringResource(R.string.settings_auto_save_generated_desc),
                            icon = Icons.Default.Download,
                            iconColor = SuccessGreen,
                            showInfoIcon = true,
                            onInfoClick = {
                                showInfoDialog = SettingInfoType.AUTO_SAVE_GENERATED
                            },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.autoSaveGenerated,
                                    onCheckedChange = { viewModel.setAutoSaveGenerated(it) },
                                    enabled = !isLoading,
                                    iconColor = SuccessGreen
                                )
                            }
                        )
                    }
                }

                item {
                    SettingsSection(title = stringResource(R.string.settings_appearance)) {
                        SettingsItem(
                            title = stringResource(R.string.settings_theme),
                            subtitle = stringResource(R.string.settings_theme_desc),
                            icon = Icons.Default.Palette,
                            iconColor = SparkPink,
                            trailing = {
                                var showThemeDialog by remember { mutableStateOf(false) }

                                TextButton(
                                    onClick = { showThemeDialog = true },
                                    enabled = !isLoading
                                ) {
                                    Text(
                                        settings.themeMode,
                                        color = SparkPink,
                                        fontWeight = FontWeight.Medium
                                    )
                                }

                                if (showThemeDialog) {
                                    ThemeSelectionDialog(
                                        currentTheme = settings.themeMode,
                                        onThemeSelected = { theme ->
                                            viewModel.setThemeMode(theme)
                                            showThemeDialog = false
                                        },
                                        onDismiss = { showThemeDialog = false }
                                    )
                                }
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_show_tutorials),
                            subtitle = stringResource(R.string.settings_show_tutorials_desc),
                            icon = Icons.Default.School,
                            iconColor = InfoBlue,
                            showInfoIcon = true,
                            onInfoClick = {
                                showInfoDialog = SettingInfoType.SHOW_TUTORIALS
                            },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.showTutorials,
                                    onCheckedChange = { viewModel.setShowTutorials(it) },
                                    enabled = !isLoading,
                                    iconColor = InfoBlue
                                )
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_enable_animations),
                            subtitle = stringResource(R.string.settings_enable_animations_desc),
                            icon = Icons.Default.PlayArrow,
                            iconColor = SparkGreen,
                            showInfoIcon = true,
                            onInfoClick = {
                                showInfoDialog = SettingInfoType.ENABLE_ANIMATIONS
                            },
                            trailing = {
                                EnhancedToggleSwitch(
                                    checked = settings.enableAnimations,
                                    onCheckedChange = { viewModel.setEnableAnimations(it) },
                                    enabled = !isLoading,
                                    iconColor = SparkGreen
                                )
                            }
                        )

                        // Language Setting
                        SettingsItem(
                            title = reactiveStringResource(R.string.settings_language),
                            subtitle = reactiveStringResource(R.string.settings_language_desc),
                            icon = Icons.Default.Language,
                            iconColor = InfoBlue,
                            trailing = {
                                var showLanguageDialog by remember { mutableStateOf(false) }

                                // Use reactive current language for immediate updates
                                val displayLanguage = remember(currentLanguage, settings.selectedLanguage) {
                                    Language.getLanguageByCode(settings.selectedLanguage) ?: currentLanguage
                                }

                                TextButton(
                                    onClick = { showLanguageDialog = true },
                                    enabled = !isLoading
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Text(
                                            text = displayLanguage.flag,
                                            fontSize = 16.sp
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                                            displayLanguage.nativeName,
                                            color = InfoBlue,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }

                                if (showLanguageDialog) {
                                    LanguageSelectionDialog(
                                        currentLanguage = displayLanguage,
                                        onLanguageSelected = { language ->
                                            Log.d("SettingsScreen", "Language selected: ${language.nativeName} (${language.code})")

                                            // Save language preference first
                                            viewModel.setSelectedLanguage(language.code)

                                            // Apply language immediately for instant UI update
                                            LanguageManager.updateLanguageImmediately(context, language)

                                            // Close dialog immediately
                                            showLanguageDialog = false

                                            Log.d("SettingsScreen", "Language change applied, Settings screen should update immediately")
                                        },
                                        onDismiss = { showLanguageDialog = false }
                                    )
                                }
                            }
                        )
                    }
                }

                item {
                    SettingsSection(title = stringResource(R.string.settings_about)) {
                        SettingsItem(
                            title = stringResource(R.string.settings_app_version),
                            subtitle = stringResource(R.string.settings_app_version_desc),
                            icon = Icons.Default.Info,
                            iconColor = InfoBlue,
                            onClick = {
                                // TODO: Copy version to clipboard
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_share_app),
                            subtitle = stringResource(R.string.settings_share_app_desc),
                            icon = Icons.Default.Share,
                            iconColor = GenerateColor,
                            onClick = { ShareUtils.shareApp(context) },
                            trailing = {
                                Icon(
                                    Icons.Default.ChevronRight,
                                    contentDescription = stringResource(R.string.share),
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        )

                        SettingsItem(
                            title = stringResource(R.string.settings_rate_app),
                            subtitle = stringResource(R.string.settings_rate_app_desc),
                            icon = Icons.Default.Star,
                            iconColor = SparkPink,
                            onClick = { ShareUtils.shareFeedback(context) },
                            trailing = {
                                Icon(
                                    Icons.Default.ChevronRight,
                                    contentDescription = stringResource(R.string.settings_rate_app),
                                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                                )
                            }
                        )
                    }
                }

                item {
                    var showResetDialog by remember { mutableStateOf(false) }

                    // Use consistent SettingsSection design for Reset Settings
                    SettingsSection(title = stringResource(R.string.settings_reset_dialog_title)) {
                        SettingsItem(
                            title = stringResource(R.string.settings_reset_all),
                            subtitle = stringResource(R.string.settings_reset_all_desc),
                            icon = Icons.Default.RestartAlt,
                            iconColor = MaterialTheme.colorScheme.error,
                            onClick = { showResetDialog = true },
                            trailing = {
                                Icon(
                                    Icons.Default.ChevronRight,
                                    contentDescription = stringResource(R.string.settings_reset_all),
                                    tint = MaterialTheme.colorScheme.error.copy(alpha = 0.7f)
                                )
                            }
                        )
                    }

                    if (showResetDialog) {
                        ResetSettingsDialog(
                            onConfirm = {
                                viewModel.resetAllSettings()
                                showResetDialog = false
                            },
                            onDismiss = { showResetDialog = false }
                        )
                    }
                }
            }
        }

        // Language change success indicator with safe area handling
        if (showLanguageChangeSuccess) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .windowInsetsPadding(WindowInsets.navigationBars)
                    .windowInsetsPadding(WindowInsets.ime),
                contentAlignment = Alignment.BottomCenter
            ) {
                Card(
                    modifier = Modifier
                        .padding(horizontal = 20.dp, vertical = 24.dp)
                        .animateContentSize(),
                    colors = CardDefaults.cardColors(
                        containerColor = SuccessGreen.copy(alpha = 0.95f)
                    ),
                    shape = RoundedCornerShape(20.dp),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = 12.dp,
                        pressedElevation = 16.dp
                    )
                ) {
                    Row(
                        modifier = Modifier.padding(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.CheckCircle,
                            contentDescription = null,
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "${reactiveStringResource(R.string.settings_language)} ${reactiveStringResource(R.string.status_done)}",
                            color = Color.White,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }

        // Info dialog display
        showInfoDialog?.let { infoType ->
            SettingsInfoDialog(
                settingType = infoType,
                onDismiss = { showInfoDialog = null },
                onAction = {
                    // Special action for sound setting - test sound
                    if (infoType == SettingInfoType.PLAY_SOUND_ON_SCAN) {
                        viewModel.testSound()
                    }
                }
            )
        }
    }
}

@Composable
private fun SettingsSection(
    title: String,
    showDivider: Boolean = true,
    icon: ImageVector = Icons.Default.Settings,
    iconTint: Color = MaterialTheme.colorScheme.primary.copy(alpha = 0.7f),
    content: @Composable () -> Unit
) {
    Column {
        // Soft spacing between sections instead of harsh dividers
        if (showDivider) {
            Spacer(modifier = Modifier.height(32.dp))
        }

        // Section header with modern styling
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                style = QRSparkTextStyles.cardTitle.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 18.sp
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.weight(1f)
            )

            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconTint,
                modifier = Modifier.size(22.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Modern card with elevated drop shadow and responsive design
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            shape = RoundedCornerShape(24.dp),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp,
                pressedElevation = 12.dp,
                hoveredElevation = 10.dp
            ),
            border = null // Remove any border
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 4.dp, horizontal = 4.dp)
            ) {
                content()
            }
        }
    }
}

@Composable
private fun SettingsItem(
    title: String,
    subtitle: String,
    icon: ImageVector,
    iconColor: Color,
    trailing: (@Composable () -> Unit)? = null,
    onClick: (() -> Unit)? = null,
    showInfoIcon: Boolean = false,
    onInfoClick: (() -> Unit)? = null
) {
    // Modern settings item without individual cards - integrates into section card with responsive design
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .then(
                if (onClick != null) {
                    Modifier.clickable { onClick() }
                } else Modifier
            )
            .padding(horizontal = 12.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Modern icon container with gradient background
        Box(
            modifier = Modifier
                .size(52.dp)
                .background(
                    brush = androidx.compose.ui.graphics.Brush.radialGradient(
                        colors = listOf(
                            iconColor.copy(alpha = 0.15f),
                            iconColor.copy(alpha = 0.08f)
                        ),
                        radius = 40f
                    ),
                    shape = RoundedCornerShape(16.dp)
                )
                .padding(2.dp),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                tint = iconColor,
                modifier = Modifier.size(26.dp)
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        // Text content with proper alignment
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = title,
                    style = QRSparkTextStyles.settingsTitle,
                    color = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier.weight(1f)
                )

                if (showInfoIcon) {
                    IconButton(
                        onClick = { onInfoClick?.invoke() },
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = stringResource(R.string.content_description_more_info),
                            tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }
            }

            Text(
                text = subtitle,
                style = QRSparkTextStyles.settingsDescription,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // Trailing content with proper alignment
        if (trailing != null) {
            Spacer(modifier = Modifier.width(12.dp))
            Box(
                contentAlignment = Alignment.Center
            ) {
                trailing()
            }
        }
    }
}

@Composable
private fun SizeSelectionDialog(
    currentSize: String,
    onSizeSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                stringResource(R.string.settings_size_dialog_title),
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                // Use internal English values for storage but display localized strings
                val sizeOptions = listOf(
                    "Small" to stringResource(R.string.size_small),
                    "Medium" to stringResource(R.string.size_medium),
                    "Large" to stringResource(R.string.size_large),
                    "Extra Large" to stringResource(R.string.size_extra_large)
                )

                sizeOptions.forEach { (internalValue, displayName) ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onSizeSelected(internalValue) }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentSize == internalValue,
                            onClick = { onSizeSelected(internalValue) },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = GenerateColor
                            )
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = displayName,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = when (internalValue) {
                                    "Small" -> "256 × 256 px"
                                    "Medium" -> "512 × 512 px"
                                    "Large" -> "768 × 768 px"
                                    "Extra Large" -> "1024 × 1024 px"
                                    else -> ""
                                },
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        },
        shape = RoundedCornerShape(16.dp)
    )
}

@Composable
private fun FormatSelectionDialog(
    currentFormat: String,
    onFormatSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                stringResource(R.string.settings_format_dialog_title),
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Column {
                listOf("PNG", "JPEG", "PDF").forEach { format ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onFormatSelected(format) }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentFormat == format,
                            onClick = { onFormatSelected(format) },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = SparkPink
                            )
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = format,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = when (format) {
                                    "PNG" -> stringResource(R.string.format_png_description)
                                    "JPEG" -> stringResource(R.string.format_jpeg_description)
                                    "PDF" -> stringResource(R.string.format_pdf_description)
                                    else -> ""
                                },
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        },
        shape = RoundedCornerShape(16.dp)
    )
}



@Composable
private fun InfoTooltipDialog(
    title: String,
    description: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Info,
                    contentDescription = null,
                    tint = InfoBlue,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = title,
                    fontWeight = FontWeight.Bold
                )
            }
        },
        text = {
            Text(
                text = description,
                fontSize = 14.sp,
                lineHeight = 20.sp
            )
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.ok))
            }
        },
        shape = RoundedCornerShape(20.dp)
    )
}

@Composable
private fun ResetSettingsDialog(
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Warning,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = stringResource(R.string.settings_reset_dialog_title),
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.error
                )
            }
        },
        text = {
            Column {
                Text(
                    text = stringResource(R.string.settings_reset_dialog_message),
                    fontSize = 14.sp,
                    lineHeight = 20.sp
                )

                Spacer(modifier = Modifier.height(12.dp))

                val resetItems = listOf(
                    stringResource(R.string.settings_reset_items_1),
                    stringResource(R.string.settings_reset_items_2),
                    stringResource(R.string.settings_reset_items_3),
                    stringResource(R.string.settings_reset_items_4)
                )

                resetItems.forEach { item ->
                    Text(
                        text = item,
                        fontSize = 13.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(vertical = 2.dp)
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))

                Text(
                    text = stringResource(R.string.settings_reset_dialog_warning),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.error
                )
            }
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text(stringResource(R.string.settings_reset_confirm))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        },
        shape = RoundedCornerShape(20.dp)
    )
}

@Composable
private fun ThemeSelectionDialog(
    currentTheme: String,
    onThemeSelected: (String) -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Palette,
                    contentDescription = null,
                    tint = SparkPink,
                    modifier = Modifier.size(24.dp)
                )
                Spacer(modifier = Modifier.width(12.dp))
                Text(
                    text = stringResource(R.string.settings_theme_dialog_title),
                    fontWeight = FontWeight.Bold
                )
            }
        },
        text = {
            Column {
                listOf("Light", "Dark", "System").forEach { theme ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onThemeSelected(theme) }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentTheme == theme,
                            onClick = { onThemeSelected(theme) },
                            colors = RadioButtonDefaults.colors(
                                selectedColor = SparkPink
                            )
                        )
                        Spacer(modifier = Modifier.width(12.dp))
                        Column {
                            Text(
                                text = when (theme) {
                                    "Light" -> stringResource(R.string.theme_light)
                                    "Dark" -> stringResource(R.string.theme_dark)
                                    "System" -> stringResource(R.string.theme_system)
                                    else -> theme
                                },
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = when (theme) {
                                    "Light" -> stringResource(R.string.theme_light_description)
                                    "Dark" -> stringResource(R.string.theme_dark_description)
                                    "System" -> stringResource(R.string.theme_system_description)
                                    else -> ""
                                },
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        },
        shape = RoundedCornerShape(16.dp)
    )
}