package com.example.qr.ui.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.example.qr.ui.theme.*
import com.example.qr.utils.LanguageManager
import com.example.qr.utils.TutorialManager
import com.example.qr.utils.TutorialContent
import com.example.qr.utils.TutorialStep
import com.example.qr.utils.AnimationManager
import android.content.Context

/**
 * TutorialOverlay - Shows tutorial steps with smooth animations
 * Respects both tutorial and animation settings
 */
@Composable
fun TutorialOverlay(
    steps: List<TutorialStep>,
    onComplete: () -> Unit,
    onSkip: () -> Unit,
    modifier: Modifier = Modifier
) {
    ReactiveLanguageContent {
        TutorialOverlayContent(
            steps = steps,
            onComplete = onComplete,
            onSkip = onSkip,
            modifier = modifier
        )
    }
}

@Composable
private fun TutorialOverlayContent(
    steps: List<TutorialStep>,
    onComplete: () -> Unit,
    onSkip: () -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var currentStep by remember { mutableIntStateOf(0) }
    var isVisible by remember { mutableStateOf(true) }
    
    // Check if tutorials should be shown
    val shouldShow = TutorialManager.shouldShowTutorials()
    
    if (!shouldShow || !isVisible || steps.isEmpty()) {
        return
    }
    
    // Enhanced animations for tutorial appearance and step transitions
    val slideOffset by AnimationManager.animateFloatAsState(
        targetValue = if (isVisible) 0f else 1000f,
        animationSpec = AnimationManager.getTweenSpec<Float>(500),
        label = "tutorial_slide"
    )

    val alpha by AnimationManager.animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = AnimationManager.getTweenSpec<Float>(400),
        label = "tutorial_alpha"
    )

    // Content transition animation for step changes
    val contentAlpha by animateFloatAsState(
        targetValue = 1f,
        animationSpec = tween(durationMillis = 300, delayMillis = 100),
        label = "content_alpha"
    )

    val contentScale by animateFloatAsState(
        targetValue = 1f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "content_scale"
    )
    
    Dialog(
        onDismissRequest = { /* Prevent dismissal */ },
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false
        )
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(
                    androidx.compose.ui.graphics.Brush.radialGradient(
                        colors = listOf(
                            Color.Black.copy(alpha = 0.8f * alpha),
                            Color.Black.copy(alpha = 0.6f * alpha)
                        ),
                        radius = 1000f
                    )
                )
                .clickable { /* Prevent clicks */ },
            contentAlignment = Alignment.Center
        ) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
                    .offset(y = slideOffset.dp),
                shape = RoundedCornerShape(28.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.98f)
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 24.dp
                )
            ) {
                Column(
                    modifier = Modifier
                        .padding(24.dp)
                        .graphicsLayer {
                            this.alpha = contentAlpha
                            scaleX = contentScale
                            scaleY = contentScale
                        },
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // Header with enhanced progress bar and close button
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.Top
                    ) {
                        // Enhanced progress indicator
                        EnhancedTutorialProgressBar(
                            currentStep = currentStep,
                            totalSteps = steps.size,
                            modifier = Modifier.weight(1f)
                        )

                        Spacer(modifier = Modifier.width(16.dp))

                        // Enhanced close button
                        IconButton(
                            onClick = {
                                isVisible = false
                                onSkip()
                            },
                            modifier = Modifier
                                .background(
                                    color = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.1f),
                                    shape = RoundedCornerShape(12.dp)
                                )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Skip tutorial",
                                tint = MaterialTheme.colorScheme.error,
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    // Tutorial content with modern typography hierarchy
                    val step = steps[currentStep]

                    // Reactive tutorial content that updates with language changes
                    ReactiveTutorialContent(
                        step = step,
                        currentStep = currentStep
                    )
                    
                    Spacer(modifier = Modifier.height(24.dp))
                    
                    // Standardized navigation buttons with string resources
                    LocalizedTutorialNavigationButtons(
                        currentStep = currentStep,
                        totalSteps = steps.size,
                        onPrevious = {
                            if (currentStep > 0) {
                                currentStep--
                            }
                        },
                        onNext = {
                            if (currentStep < steps.size - 1) {
                                currentStep++
                            }
                        },
                        onFinish = {
                            isVisible = false
                            onComplete()
                        },
                        customActionText = step.action
                    )
                }
            }
        }
    }
}

/**
 * Reactive tutorial content that updates when language changes
 */
@Composable
private fun ReactiveTutorialContent(
    step: TutorialStep,
    currentStep: Int
) {
    val context = LocalContext.current

    // Watch for language changes to update tutorial content
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    // Get reactive tutorial content based on current step and language
    val reactiveTutorialData = remember(currentStep, currentLanguage) {
        getReactiveTutorialData(context, currentStep)
    }

    // Title with strong hierarchy and QR Spark branding
    Text(
        text = reactiveTutorialData?.title ?: step.title,
        style = MaterialTheme.typography.headlineSmall.copy(
            fontSize = 26.sp,
            fontWeight = FontWeight.ExtraBold,
            lineHeight = 32.sp,
            letterSpacing = (-0.5).sp
        ),
        color = ScanColor,
        textAlign = TextAlign.Center,
        fontFamily = SafeFontFamily
    )

    Spacer(modifier = Modifier.height(20.dp))

    // Description with improved readability and contrast
    Text(
        text = reactiveTutorialData?.description ?: step.description,
        style = MaterialTheme.typography.bodyLarge.copy(
            fontSize = 17.sp,
            fontWeight = FontWeight.Normal,
            lineHeight = 26.sp,
            letterSpacing = 0.1.sp
        ),
        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.85f),
        textAlign = TextAlign.Center,
        fontFamily = SafeFontFamily
    )
}

/**
 * Data class for reactive tutorial content
 */
private data class ReactiveTutorialData(
    val title: String,
    val description: String,
    val action: String
)

/**
 * Get reactive tutorial data that updates with language changes
 */
private fun getReactiveTutorialData(context: Context, currentStep: Int): ReactiveTutorialData? {
    // This function will be called whenever language changes
    // Return null to use the original step data as fallback
    return null
}

/**
 * Simple tutorial tooltip for highlighting specific UI elements
 */
@Composable
fun TutorialTooltip(
    text: String,
    isVisible: Boolean,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    val shouldShow = TutorialManager.shouldShowTutorials()
    
    if (!shouldShow || !isVisible) return
    
    val alpha by AnimationManager.animateFloatAsState(
        targetValue = if (isVisible) 1f else 0f,
        animationSpec = AnimationManager.getTweenSpec<Float>(200),
        label = "tooltip_alpha"
    )
    
    val scale by AnimationManager.animateFloatAsState(
        targetValue = if (isVisible) 1f else 0.8f,
        animationSpec = AnimationManager.getSpringSpec<Float>(),
        label = "tooltip_scale"
    )
    
    if (alpha > 0f) {
        Card(
            modifier = modifier
                .wrapContentSize()
                .graphicsLayer {
                    this.alpha = alpha
                    scaleX = scale
                    scaleY = scale
                }
                .clickable { onDismiss() },
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = ScanColor.copy(alpha = 0.95f)
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 12.dp
            )
        ) {
            Text(
                text = text,
                modifier = Modifier.padding(16.dp),
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                ),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontFamily = SafeFontFamily
            )
        }
    }
}

/**
 * Tutorial trigger composable that shows tutorials when conditions are met
 */
@Composable
fun TutorialTrigger(
    tutorialKey: String,
    steps: List<TutorialStep>,
    onComplete: (() -> Unit)? = null
) {
    val context = LocalContext.current
    var showTutorial by remember { mutableStateOf(false) }

    // Watch for language changes to regenerate tutorial content
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    // Regenerate tutorial steps when language changes
    val localizedSteps = remember(currentLanguage) {
        getLocalizedTutorialSteps(context, tutorialKey)
    }

    // Check if this tutorial should be shown
    val shouldShow = TutorialManager.shouldShowTutorial(tutorialKey)

    LaunchedEffect(shouldShow) {
        if (shouldShow) {
            showTutorial = true
        }
    }

    if (showTutorial) {
        TutorialOverlay(
            steps = localizedSteps ?: steps,
            onComplete = {
                TutorialManager.markTutorialAsShown(context, tutorialKey)
                showTutorial = false
                onComplete?.invoke()
            },
            onSkip = {
                TutorialManager.markTutorialAsShown(context, tutorialKey)
                showTutorial = false
            }
        )
    }
}

/**
 * Get localized tutorial steps based on tutorial key and current language
 */
private fun getLocalizedTutorialSteps(context: Context, tutorialKey: String): List<TutorialStep>? {
    return when (tutorialKey) {
        TutorialManager.TutorialKeys.HOME_SCREEN -> TutorialContent.getHomeTutorials(context)
        TutorialManager.TutorialKeys.SCANNER_SCREEN -> TutorialContent.getScannerTutorials(context)
        TutorialManager.TutorialKeys.GENERATOR_SCREEN -> TutorialContent.getGeneratorTutorials(context)
        TutorialManager.TutorialKeys.HISTORY_SCREEN -> TutorialContent.getHistoryTutorials(context)
        else -> null
    }
}
