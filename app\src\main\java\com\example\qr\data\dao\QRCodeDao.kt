package com.example.qr.data.dao

import androidx.room.*
import com.example.qr.data.entity.QRCodeEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface QRCodeDao {
    
    // Get all QR codes as Flow for reactive updates
    @Query("SELECT * FROM qr_codes ORDER BY createdAt DESC")
    fun getAllQRCodes(): Flow<List<QRCodeEntity>>
    
    // Get all QR codes as list (for non-reactive operations)
    @Query("SELECT * FROM qr_codes ORDER BY createdAt DESC")
    suspend fun getAllQRCodesList(): List<QRCodeEntity>
    
    // Get scanned QR codes
    @Query("SELECT * FROM qr_codes WHERE isGenerated = 0 ORDER BY createdAt DESC")
    fun getScannedQRCodes(): Flow<List<QRCodeEntity>>
    
    // Get generated QR codes
    @Query("SELECT * FROM qr_codes WHERE isGenerated = 1 ORDER BY createdAt DESC")
    fun getGeneratedQRCodes(): Flow<List<QRCodeEntity>>
    
    // Get favorite QR codes
    @Query("SELECT * FROM qr_codes WHERE isFavorite = 1 ORDER BY createdAt DESC")
    fun getFavoriteQRCodes(): Flow<List<QRCodeEntity>>
    
    // Search QR codes by content or display name
    @Query("""
        SELECT * FROM qr_codes 
        WHERE content LIKE '%' || :query || '%' 
        OR displayName LIKE '%' || :query || '%' 
        ORDER BY createdAt DESC
    """)
    fun searchQRCodes(query: String): Flow<List<QRCodeEntity>>
    
    // Get QR code by ID
    @Query("SELECT * FROM qr_codes WHERE id = :id")
    suspend fun getQRCodeById(id: Long): QRCodeEntity?
    
    // Insert new QR code
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertQRCode(qrCode: QRCodeEntity): Long
    
    // Insert multiple QR codes
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertQRCodes(qrCodes: List<QRCodeEntity>)
    
    // Update QR code
    @Update
    suspend fun updateQRCode(qrCode: QRCodeEntity)
    
    // Delete QR code
    @Delete
    suspend fun deleteQRCode(qrCode: QRCodeEntity)
    
    // Delete QR code by ID
    @Query("DELETE FROM qr_codes WHERE id = :id")
    suspend fun deleteQRCodeById(id: Long)
    
    // Toggle favorite status
    @Query("UPDATE qr_codes SET isFavorite = NOT isFavorite WHERE id = :id")
    suspend fun toggleFavorite(id: Long)
    
    // Clear all QR codes
    @Query("DELETE FROM qr_codes")
    suspend fun clearAll()
    
    // Delete non-favorite QR codes
    @Query("DELETE FROM qr_codes WHERE isFavorite = 0")
    suspend fun deleteNonFavorites()
    
    // Get count of QR codes
    @Query("SELECT COUNT(*) FROM qr_codes")
    suspend fun getQRCodeCount(): Int
    
    // Get count of favorite QR codes
    @Query("SELECT COUNT(*) FROM qr_codes WHERE isFavorite = 1")
    suspend fun getFavoriteCount(): Int
    
    // Get count of scanned QR codes
    @Query("SELECT COUNT(*) FROM qr_codes WHERE isGenerated = 0")
    suspend fun getScannedCount(): Int
    
    // Get count of generated QR codes
    @Query("SELECT COUNT(*) FROM qr_codes WHERE isGenerated = 1")
    suspend fun getGeneratedCount(): Int
}
