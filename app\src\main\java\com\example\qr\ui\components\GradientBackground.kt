package com.example.qr.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import com.example.qr.ui.theme.*

@Composable
fun GradientBackground(
    modifier: Modifier = Modifier,
    applyScreenSafeArea: Boolean = true,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        BrandGradientStart.copy(alpha = 0.05f),
                        BrandGradientMiddle.copy(alpha = 0.03f),
                        BrandGradientEnd.copy(alpha = 0.05f)
                    )
                )
            )
    ) {
        content()
    }
}

@Composable
fun SparkGradientText(
    text: String,
    modifier: Modifier = Modifier
) {
    // This would require a custom text implementation with gradient
    // For now, we'll use the existing approach with separate colored text
}
