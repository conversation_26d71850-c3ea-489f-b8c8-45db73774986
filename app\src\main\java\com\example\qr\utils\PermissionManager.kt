package com.example.qr.utils

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat

object PermissionManager {

    /**
     * Check if storage permission is granted
     */
    fun hasStoragePermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ doesn't need WRITE_EXTERNAL_STORAGE for MediaStore
            true
        } else {
            // Android 9 and below need WRITE_EXTERNAL_STORAGE
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.CAMERA
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * Get required storage permissions based on Android version
     */
    fun getStoragePermissions(): Array<String> {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // Android 10+ doesn't need WRITE_EXTERNAL_STORAGE for MediaStore
            emptyArray()
        } else {
            // Android 9 and below need WRITE_EXTERNAL_STORAGE
            arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE)
        }
    }

    /**
     * Get all required permissions for the app
     */
    fun getAllRequiredPermissions(): Array<String> {
        val permissions = mutableListOf<String>()
        
        // Camera permission (always required)
        permissions.add(Manifest.permission.CAMERA)
        
        // Storage permissions (version dependent)
        permissions.addAll(getStoragePermissions())
        
        return permissions.toTypedArray()
    }

    /**
     * Check if all required permissions are granted
     */
    fun hasAllRequiredPermissions(context: Context): Boolean {
        return hasCameraPermission(context) && hasStoragePermission(context)
    }

    /**
     * Get permission rationale message
     */
    fun getPermissionRationale(permission: String): String {
        return when (permission) {
            Manifest.permission.CAMERA -> 
                "Camera permission is required to scan QR codes. Please grant camera access to use the scanner feature."
            
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> 
                "Storage permission is required to save QR codes to your gallery. Please grant storage access to save images."
            
            else -> 
                "This permission is required for the app to function properly."
        }
    }

    /**
     * Get user-friendly permission name
     */
    fun getPermissionName(permission: String): String {
        return when (permission) {
            Manifest.permission.CAMERA -> "Camera"
            Manifest.permission.WRITE_EXTERNAL_STORAGE -> "Storage"
            Manifest.permission.READ_EXTERNAL_STORAGE -> "Storage"
            else -> "Unknown Permission"
        }
    }
}
