package com.example.qr.utils

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.MediaStore
import android.util.Log
import androidx.core.content.FileProvider
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first

object ShareUtils {

    /**
     * Share the QR Spark app with others
     */
    fun shareApp(context: Context) {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, "Check out QR Spark!")
                putExtra(
                    Intent.EXTRA_TEXT,
                    """
                    🌟 QR Spark - Your Ultimate QR Companion! 🌟

                    I've been using QR Spark and it's amazing! You can:

                    📱 Scan QR codes instantly
                    ✨ Generate custom QR codes
                    💾 Save and organize your collection
                    ❤️ Mark favorites for quick access
                    🎨 Beautiful, modern design

                    Download QR Spark and experience the future of QR code management!

                    #QRSpark #QRCode #MobileApp
                    """.trimIndent()
                )
            }

            val chooserIntent = Intent.createChooser(shareIntent, "Share QR Spark")
            context.startActivity(chooserIntent)

            Log.d("ShareUtils", "App share intent launched successfully")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing app", e)
        }
    }

    /**
     * Share QR code content as text
     */
    fun shareQRContent(context: Context, content: String, qrType: String = "QR Code") {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, "Shared from QR Spark")
                putExtra(
                    Intent.EXTRA_TEXT,
                    """
                    📱 Shared from QR Spark

                    $qrType Content:
                    $content

                    Generated with QR Spark - Your Ultimate QR Companion!
                    """.trimIndent()
                )
            }

            val chooserIntent = Intent.createChooser(shareIntent, "Share QR Content")
            context.startActivity(chooserIntent)

            Log.d("ShareUtils", "QR content shared successfully")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing QR content", e)
        }
    }

    /**
     * Share QR code as image with enhanced content
     */
    fun shareQRImage(context: Context, bitmap: Bitmap, content: String, qrType: String = "QR Code") {
        try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "QRSpark_${timestamp}.png"
            val file = saveImageToCache(context, bitmap, fileName)

            if (file != null) {
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )

                // Create professional sharing message
                val shareMessage = buildString {
                    appendLine("🌟 QR Code from QR Spark")
                    appendLine()
                    appendLine("📱 Type: $qrType")
                    if (content.isNotBlank()) {
                        appendLine("📄 Content: ${if (content.length > 100) "${content.take(100)}..." else content}")
                    }
                    appendLine()
                    appendLine("✨ Generated with QR Spark - Your Ultimate QR Companion!")
                    appendLine("#QRSpark #QRCode")
                }

                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = "image/png"
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_TEXT, shareMessage)
                    putExtra(Intent.EXTRA_SUBJECT, "QR Code from QR Spark")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }

                val chooser = Intent.createChooser(shareIntent, "Share QR Code")
                context.startActivity(chooser)

                Log.d("ShareUtils", "Shared QR image for content: $content")
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing QR image", e)
        }
    }

    /**
     * Share QR code with both image and content (enhanced version)
     */
    fun shareQRCodeComplete(context: Context, bitmap: Bitmap?, content: String, qrType: String = "QR Code") {
        try {
            if (bitmap != null) {
                // Share with image
                shareQRImage(context, bitmap, content, qrType)
            } else {
                // Fallback to text only
                shareQRContent(context, content, qrType)
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing QR code", e)
            // Fallback to text sharing
            shareQRContent(context, content, qrType)
        }
    }

    /**
     * Save QR code image to gallery with enhanced error handling
     */
    fun saveQRToGallery(context: Context, bitmap: Bitmap, content: String): SaveResult {
        return try {
            // Create a meaningful filename with timestamp
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val displayName = "QRSpark_${timestamp}.png"
            val mimeType = "image/png"

            // Use different approaches based on Android version
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                saveToGalleryModern(context, bitmap, displayName, mimeType, content)
            } else {
                saveToGalleryLegacy(context, bitmap, displayName, content)
            }
        } catch (e: SecurityException) {
            Log.e("ShareUtils", "Permission denied when saving to gallery", e)
            SaveResult.Error("Permission denied. Please grant storage permission.")
        } catch (e: IOException) {
            Log.e("ShareUtils", "IO error when saving to gallery", e)
            SaveResult.Error("Failed to write image file.")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Unexpected error saving QR to gallery", e)
            SaveResult.Error("An unexpected error occurred: ${e.message}")
        }
    }

    /**
     * Modern approach for Android Q+ using MediaStore API
     */
    private fun saveToGalleryModern(
        context: Context,
        bitmap: Bitmap,
        displayName: String,
        mimeType: String,
        content: String
    ): SaveResult {
        val values = android.content.ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, displayName)
            put(MediaStore.Images.Media.MIME_TYPE, mimeType)
            put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/QR Spark")
            put(MediaStore.Images.Media.IS_PENDING, 1)

            // Add metadata
            put(MediaStore.Images.Media.DESCRIPTION, "QR Code generated by QR Spark")
            if (content.isNotBlank()) {
                put(MediaStore.Images.Media.TITLE, "QR: ${content.take(50)}")
            }
        }

        val resolver = context.contentResolver
        val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)

        return if (uri != null) {
            try {
                resolver.openOutputStream(uri)?.use { outputStream ->
                    val success = bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                    if (!success) {
                        throw IOException("Failed to compress bitmap")
                    }
                }

                // Mark as not pending
                values.clear()
                values.put(MediaStore.Images.Media.IS_PENDING, 0)
                resolver.update(uri, values, null, null)

                Log.d("ShareUtils", "Successfully saved QR image to gallery: $displayName")
                SaveResult.Success(displayName, "Pictures/QR Spark")
            } catch (e: Exception) {
                // Clean up failed entry
                resolver.delete(uri, null, null)
                throw e
            }
        } else {
            SaveResult.Error("Failed to create gallery entry")
        }
    }

    /**
     * Legacy approach for Android 9 and below
     */
    private fun saveToGalleryLegacy(
        context: Context,
        bitmap: Bitmap,
        displayName: String,
        content: String
    ): SaveResult {
        val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
        val qrSparkDir = File(picturesDir, "QR Spark")

        if (!qrSparkDir.exists()) {
            if (!qrSparkDir.mkdirs()) {
                return SaveResult.Error("Failed to create QR Spark directory")
            }
        }

        val imageFile = File(qrSparkDir, displayName)

        return try {
            FileOutputStream(imageFile).use { outputStream ->
                val success = bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                if (!success) {
                    throw IOException("Failed to compress bitmap")
                }
            }

            // Add to MediaStore so it appears in gallery (modern approach)
            val values = android.content.ContentValues().apply {
                put(MediaStore.Images.Media.DISPLAY_NAME, displayName)
                put(MediaStore.Images.Media.MIME_TYPE, "image/png")
                put(MediaStore.Images.Media.DATA, imageFile.absolutePath)
                put(MediaStore.Images.Media.DESCRIPTION, "QR Code generated by QR Spark: $content")
            }
            context.contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values)

            Log.d("ShareUtils", "Successfully saved QR image (legacy): $displayName")
            SaveResult.Success(displayName, qrSparkDir.absolutePath)
        } catch (e: Exception) {
            // Clean up failed file
            if (imageFile.exists()) {
                imageFile.delete()
            }
            throw e
        }
    }

    /**
     * Result class for save operations
     */
    sealed class SaveResult {
        data class Success(val fileName: String, val path: String) : SaveResult()
        data class Error(val message: String) : SaveResult()
    }

    /**
     * Save image to app cache for sharing
     */
    private fun saveImageToCache(context: Context, bitmap: Bitmap, fileName: String): File? {
        return try {
            val cacheDir = File(context.cacheDir, "shared_images")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val file = File(cacheDir, fileName)
            val outputStream = FileOutputStream(file)

            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
            outputStream.flush()
            outputStream.close()

            Log.d("ShareUtils", "Saved image to cache: ${file.absolutePath}")
            file
        } catch (e: IOException) {
            Log.e("ShareUtils", "Error saving image to cache", e)
            null
        }
    }

    /**
     * Share feedback or review prompt
     */
    fun shareFeedback(context: Context) {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, "QR Spark App Feedback")
                putExtra(
                    Intent.EXTRA_TEXT,
                    """
                    I'd like to share my experience with QR Spark:

                    🌟 Rating: [Your rating here]

                    💭 What I love about QR Spark:
                    - [Your feedback here]

                    🚀 Suggestions for improvement:
                    - [Your suggestions here]

                    QR Spark is a fantastic QR code app that makes scanning and generating QR codes a breeze!

                    #QRSpark #AppReview #QRCode
                    """.trimIndent()
                )
            }

            val chooserIntent = Intent.createChooser(shareIntent, "Share Feedback")
            context.startActivity(chooserIntent)

            Log.d("ShareUtils", "Feedback share intent launched successfully")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing feedback", e)
        }
    }

    /**
     * Open email client for support
     */
    fun contactSupport(context: Context) {
        try {
            val emailIntent = Intent(Intent.ACTION_SEND).apply {
                type = "message/rfc822"
                putExtra(Intent.EXTRA_EMAIL, arrayOf("<EMAIL>"))
                putExtra(Intent.EXTRA_SUBJECT, "QR Spark Support Request")
                putExtra(
                    Intent.EXTRA_TEXT,
                    """
                    Hello QR Spark Support Team,

                    I need help with:
                    [Please describe your issue or question here]

                    App Version: 1.0.0
                    Device: ${android.os.Build.MODEL}
                    Android Version: ${android.os.Build.VERSION.RELEASE}

                    Thank you for your assistance!

                    Best regards,
                    [Your name]
                    """.trimIndent()
                )
            }

            val chooserIntent = Intent.createChooser(emailIntent, "Contact Support")
            context.startActivity(chooserIntent)

            Log.d("ShareUtils", "Support email intent launched successfully")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error opening email client", e)
            // Fallback to general share if email client not available
            shareGeneralSupport(context)
        }
    }

    /**
     * Fallback support sharing method
     */
    private fun shareGeneralSupport(context: Context) {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, "QR Spark Support Request")
                putExtra(
                    Intent.EXTRA_TEXT,
                    """
                    QR Spark Support Request

                    I need help with QR Spark app.

                    Please contact: <EMAIL>

                    App Version: 1.0.0
                    Device: ${android.os.Build.MODEL}
                    Android Version: ${android.os.Build.VERSION.RELEASE}
                    """.trimIndent()
                )
            }

            val chooserIntent = Intent.createChooser(shareIntent, "Contact Support")
            context.startActivity(chooserIntent)

            Log.d("ShareUtils", "General support share launched successfully")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error with fallback support sharing", e)
        }
    }

    /**
     * Share app store link (when app is published)
     */
    fun shareAppStoreLink(context: Context) {
        try {
            val shareIntent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_SUBJECT, "Download QR Spark!")
                putExtra(
                    Intent.EXTRA_TEXT,
                    """
                    🌟 QR Spark - The Ultimate QR Code App! 🌟

                    Download QR Spark for the best QR code experience:

                    📱 Instant QR code scanning
                    ✨ Beautiful QR code generation
                    💾 Smart collection management
                    🎨 Modern, intuitive design

                    Get it now: [App Store Link]

                    #QRSpark #QRCode #MobileApp
                    """.trimIndent()
                )
            }

            val chooserIntent = Intent.createChooser(shareIntent, "Share QR Spark")
            context.startActivity(chooserIntent)

            Log.d("ShareUtils", "App store link shared successfully")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing app store link", e)
        }
    }

    /**
     * Save QR code in specified format (PNG, JPEG, PDF)
     */
    fun saveQRCodeInFormat(
        context: Context,
        bitmap: Bitmap,
        qrCodeData: QRCodeData,
        format: String
    ): SaveResult {
        return when (format.uppercase()) {
            "PNG" -> saveQRToGallery(context, bitmap, qrCodeData.content)
            "JPEG" -> saveQRAsJPEG(context, bitmap, qrCodeData.content)
            "PDF" -> saveQRAsPDF(context, bitmap, qrCodeData)
            else -> saveQRToGallery(context, bitmap, qrCodeData.content) // Default to PNG
        }
    }

    /**
     * Save QR code as JPEG format
     */
    private fun saveQRAsJPEG(context: Context, bitmap: Bitmap, content: String): SaveResult {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val displayName = "QRSpark_${timestamp}.jpg"
            val mimeType = "image/jpeg"

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val contentValues = android.content.ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, displayName)
                    put(MediaStore.Images.Media.MIME_TYPE, mimeType)
                    put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/QR Spark")
                    put(MediaStore.Images.Media.IS_PENDING, 1)
                }

                val resolver = context.contentResolver
                val uri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues)

                if (uri != null) {
                    resolver.openOutputStream(uri)?.use { outputStream ->
                        val success = bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                        if (!success) {
                            throw IOException("Failed to compress bitmap as JPEG")
                        }
                    }

                    contentValues.clear()
                    contentValues.put(MediaStore.Images.Media.IS_PENDING, 0)
                    resolver.update(uri, contentValues, null, null)

                    SaveResult.Success(displayName, "Pictures/QR Spark")
                } else {
                    SaveResult.Error("Failed to create gallery entry")
                }
            } else {
                // Legacy approach for older Android versions
                val picturesDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES)
                val qrSparkDir = File(picturesDir, "QR Spark")
                if (!qrSparkDir.exists()) {
                    qrSparkDir.mkdirs()
                }

                val file = File(qrSparkDir, displayName)
                FileOutputStream(file).use { outputStream ->
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 90, outputStream)
                }

                SaveResult.Success(displayName, qrSparkDir.absolutePath)
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error saving JPEG", e)
            SaveResult.Error("Failed to save as JPEG: ${e.message}")
        }
    }

    /**
     * Save QR code as PDF format
     */
    private fun saveQRAsPDF(context: Context, bitmap: Bitmap, qrCodeData: QRCodeData): SaveResult {
        return try {
            val result = PDFGenerator.generateQRCodePDF(
                context = context,
                qrBitmap = bitmap,
                content = qrCodeData.content,
                qrType = qrCodeData.type.name
            )

            when (result) {
                is PDFResult.Success -> SaveResult.Success(result.fileName, "Documents/QR Spark")
                is PDFResult.Error -> SaveResult.Error(result.message)
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error saving PDF", e)
            SaveResult.Error("Failed to save as PDF: ${e.message}")
        }
    }

    /**
     * Share QR code in the user's preferred format from settings
     */
    suspend fun shareQRCodeInFormat(
        context: Context,
        bitmap: Bitmap,
        qrCodeData: QRCodeData,
        overrideFormat: String? = null
    ): ShareResult {
        return try {
            val settingsRepository = SettingsRepository(context)
            val targetFormat = overrideFormat ?: settingsRepository.defaultQRFormat.first()

            Log.d("ShareUtils", "Sharing QR code in format: $targetFormat")

            when (targetFormat.uppercase()) {
                "PNG" -> shareQRImageInFormat(context, bitmap, qrCodeData, "PNG")
                "JPEG" -> shareQRImageInFormat(context, bitmap, qrCodeData, "JPEG")
                "PDF" -> shareQRAsPDF(context, bitmap, qrCodeData)
                else -> shareQRImageInFormat(context, bitmap, qrCodeData, "PNG") // Default to PNG
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing QR code in format", e)
            ShareResult.Error("Failed to share QR code: ${e.message}")
        }
    }

    /**
     * Share QR code as image in specified format (PNG or JPEG)
     */
    private fun shareQRImageInFormat(
        context: Context,
        bitmap: Bitmap,
        qrCodeData: QRCodeData,
        format: String
    ): ShareResult {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val extension = when (format.uppercase()) {
                "JPEG" -> ".jpg"
                else -> ".png"
            }
            val fileName = "QRSpark_${timestamp}${extension}"

            // Create temporary file for sharing
            val cacheDir = File(context.cacheDir, "shared_qr")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val file = File(cacheDir, fileName)
            val outputStream = FileOutputStream(file)

            val compressFormat = when (format.uppercase()) {
                "JPEG" -> Bitmap.CompressFormat.JPEG
                else -> Bitmap.CompressFormat.PNG
            }
            val quality = if (format.uppercase() == "JPEG") 90 else 100

            bitmap.compress(compressFormat, quality, outputStream)
            outputStream.close()

            if (file.exists()) {
                val uri = FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )

                val mimeType = when (format.uppercase()) {
                    "JPEG" -> "image/jpeg"
                    else -> "image/png"
                }

                val shareMessage = buildString {
                    appendLine("🌟 QR Code from QR Spark")
                    appendLine()
                    appendLine("📱 Type: ${qrCodeData.type.name}")
                    appendLine("📄 Format: ${format.uppercase()}")
                    if (qrCodeData.content.isNotBlank()) {
                        appendLine("📄 Content: ${if (qrCodeData.content.length > 100) "${qrCodeData.content.take(100)}..." else qrCodeData.content}")
                    }
                    appendLine()
                    appendLine("✨ Generated with QR Spark - Your Ultimate QR Companion!")
                    appendLine("#QRSpark #QRCode")
                }

                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    type = mimeType
                    putExtra(Intent.EXTRA_STREAM, uri)
                    putExtra(Intent.EXTRA_TEXT, shareMessage)
                    putExtra(Intent.EXTRA_SUBJECT, "QR Code from QR Spark (${format.uppercase()})")
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }

                val chooser = Intent.createChooser(shareIntent, "Share QR Code (${format.uppercase()})")
                context.startActivity(chooser)

                Log.d("ShareUtils", "Shared QR ${format.uppercase()} for content: ${qrCodeData.content}")
                ShareResult.Success("QR code shared as ${format.uppercase()}")
            } else {
                ShareResult.Error("Failed to create temporary file for sharing")
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing QR image in format $format", e)
            ShareResult.Error("Failed to share as ${format.uppercase()}: ${e.message}")
        }
    }

    /**
     * Share QR code as PDF
     */
    private fun shareQRAsPDF(
        context: Context,
        bitmap: Bitmap,
        qrCodeData: QRCodeData
    ): ShareResult {
        return try {
            // Generate PDF in temporary location for sharing
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "QRSpark_${timestamp}.pdf"

            // Create temporary file in cache directory for sharing
            val cacheDir = File(context.cacheDir, "shared_qr")
            if (!cacheDir.exists()) {
                cacheDir.mkdirs()
            }

            val tempPdfFile = File(cacheDir, fileName)

            // Try to generate PDF directly to cache location
            val result = PDFGenerator.generateQRCodePDFToFile(
                context = context,
                qrBitmap = bitmap,
                content = qrCodeData.content,
                qrType = qrCodeData.type.name,
                outputFile = tempPdfFile
            )

            when (result) {
                is PDFResult.Success -> {
                    if (tempPdfFile.exists()) {
                        sharePDFFile(context, tempPdfFile, qrCodeData)
                    } else {
                        ShareResult.Error("PDF file not found in cache")
                    }
                }
                is PDFResult.Error -> {
                    // Fallback: try to find existing PDF and copy it
                    tryFallbackPDFShare(context, bitmap, qrCodeData, tempPdfFile)
                }
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing QR PDF", e)
            ShareResult.Error("Failed to share as PDF: ${e.message}")
        }
    }

    /**
     * Share a PDF file using FileProvider
     */
    private fun sharePDFFile(context: Context, pdfFile: File, qrCodeData: QRCodeData): ShareResult {
        return try {
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                pdfFile
            )

            val shareMessage = buildString {
                appendLine("🌟 QR Code PDF from QR Spark")
                appendLine()
                appendLine("📱 Type: ${qrCodeData.type.name}")
                appendLine("📄 Format: PDF Document")
                if (qrCodeData.content.isNotBlank()) {
                    appendLine("📄 Content: ${if (qrCodeData.content.length > 100) "${qrCodeData.content.take(100)}..." else qrCodeData.content}")
                }
                appendLine()
                appendLine("✨ Generated with QR Spark - Your Ultimate QR Companion!")
                appendLine("#QRSpark #QRCode #PDF")
            }

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                type = "application/pdf"
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_TEXT, shareMessage)
                putExtra(Intent.EXTRA_SUBJECT, "QR Code PDF from QR Spark")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            val chooser = Intent.createChooser(shareIntent, "Share QR Code PDF")
            context.startActivity(chooser)

            Log.d("ShareUtils", "Shared QR PDF for content: ${qrCodeData.content}")
            ShareResult.Success("QR code shared as PDF")
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error sharing PDF file", e)
            ShareResult.Error("Failed to share PDF: ${e.message}")
        }
    }

    /**
     * Fallback method to try sharing PDF by generating to Documents first, then copying
     */
    private fun tryFallbackPDFShare(
        context: Context,
        bitmap: Bitmap,
        qrCodeData: QRCodeData,
        targetFile: File
    ): ShareResult {
        return try {
            // Generate PDF to Documents directory first
            val documentsResult = PDFGenerator.generateQRCodePDF(
                context = context,
                qrBitmap = bitmap,
                content = qrCodeData.content,
                qrType = qrCodeData.type.name
            )

            when (documentsResult) {
                is PDFResult.Success -> {
                    val sourceFile = File(documentsResult.filePath)
                    if (sourceFile.exists()) {
                        // Copy to cache directory for sharing
                        sourceFile.copyTo(targetFile, overwrite = true)
                        if (targetFile.exists()) {
                            sharePDFFile(context, targetFile, qrCodeData)
                        } else {
                            ShareResult.Error("Failed to copy PDF to cache")
                        }
                    } else {
                        ShareResult.Error("Generated PDF not found")
                    }
                }
                is PDFResult.Error -> ShareResult.Error(documentsResult.message)
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error in fallback PDF share", e)
            ShareResult.Error("Fallback PDF sharing failed: ${e.message}")
        }
    }

    /**
     * Enhanced QR code sharing that respects user's format preference
     */
    suspend fun shareQRCodeWithFormat(
        context: Context,
        bitmap: Bitmap?,
        qrCodeData: QRCodeData,
        overrideFormat: String? = null
    ): ShareResult {
        return try {
            if (bitmap != null) {
                shareQRCodeInFormat(context, bitmap, qrCodeData, overrideFormat)
            } else {
                // Fallback to text sharing
                shareQRContent(context, qrCodeData.content, qrCodeData.type.name)
                ShareResult.Success("QR content shared as text")
            }
        } catch (e: Exception) {
            Log.e("ShareUtils", "Error in enhanced QR sharing", e)
            ShareResult.Error("Failed to share QR code: ${e.message}")
        }
    }

    /**
     * Result class for sharing operations
     */
    sealed class ShareResult {
        data class Success(val message: String) : ShareResult()
        data class Error(val message: String) : ShareResult()
    }
}
