package com.example.qr.presentation.camera

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.OptIn
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.barcode.common.Barcode
import com.google.mlkit.vision.common.InputImage
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

@Composable
fun CameraPreview(
    isFlashOn: Boolean,
    onQRCodeDetected: (String) -> Unit,
    modifier: Modifier = Modifier,
    isBackCamera: Boolean = true,
    zoomLevel: Float = 1f,
    onCameraSwitch: (() -> Unit)? = null
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current

    // Camera state management
    var camera by remember { mutableStateOf<Camera?>(null) }
    var isScanning by remember { mutableStateOf(true) }
    var cameraError by remember { mutableStateOf<String?>(null) }
    var currentCameraDirection by remember { mutableStateOf(isBackCamera) }

    // Executor for camera operations - properly managed
    val cameraExecutor = remember {
        Executors.newSingleThreadExecutor().apply {
            Log.d("CameraPreview", "Camera executor created")
        }
    }

    // Get camera manager instance
    val cameraManager = remember { CameraManager.getInstance() }

    // Lifecycle observer to handle camera cleanup
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    Log.d("CameraPreview", "Lifecycle ON_PAUSE - pausing camera")
                    try {
                        cameraManager.pauseCamera()
                        camera = null
                    } catch (e: Exception) {
                        Log.e("CameraPreview", "Error during pause", e)
                    }
                }
                Lifecycle.Event.ON_STOP -> {
                    Log.d("CameraPreview", "Lifecycle ON_STOP - stopping camera")
                    try {
                        cameraManager.cleanup()
                        camera = null
                    } catch (e: Exception) {
                        Log.e("CameraPreview", "Error during stop", e)
                    }
                }
                Lifecycle.Event.ON_DESTROY -> {
                    Log.d("CameraPreview", "Lifecycle ON_DESTROY - final cleanup")
                    try {
                        cameraManager.forceCleanup()
                        if (!cameraExecutor.isShutdown) {
                            cameraExecutor.shutdown()
                        }
                    } catch (e: Exception) {
                        Log.e("CameraPreview", "Error during destroy", e)
                    }
                }
                else -> { /* No action needed */ }
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            Log.d("CameraPreview", "DisposableEffect onDispose - final cleanup")
            try {
                lifecycleOwner.lifecycle.removeObserver(observer)
                cameraManager.forceCleanup()
                if (!cameraExecutor.isShutdown) {
                    cameraExecutor.shutdown()
                }
            } catch (e: Exception) {
                Log.e("CameraPreview", "Error in dispose cleanup", e)
            }
        }
    }

    // Camera switching effect
    LaunchedEffect(isBackCamera) {
        if (currentCameraDirection != isBackCamera) {
            Log.d("CameraPreview", "Camera direction changed, switching camera")
            currentCameraDirection = isBackCamera
            // Force camera reinitialization by cleaning up first
            cameraManager.cleanup()
            camera = null
            cameraError = null
        }
    }

    // Flash control
    LaunchedEffect(isFlashOn, camera) {
        if (camera != null) {
            cameraManager.enableFlash(isFlashOn)
        }
    }

    // Zoom control - Apply zoom changes to camera
    LaunchedEffect(zoomLevel, camera) {
        if (camera != null) {
            cameraManager.setZoomLevel(zoomLevel)
            Log.d("CameraPreview", "Applied zoom level: ${zoomLevel}x")
        }
    }

    Box(modifier = modifier.fillMaxSize()) {
        AndroidView(
            factory = { ctx ->
                Log.d("CameraPreview", "Creating PreviewView")
                PreviewView(ctx).apply {
                    scaleType = PreviewView.ScaleType.FILL_CENTER
                    implementationMode = PreviewView.ImplementationMode.COMPATIBLE
                }
            },
            modifier = Modifier.fillMaxSize(),
            update = { view ->
                Log.d("CameraPreview", "AndroidView update called - Camera state: ${cameraManager.getCameraState()}, isBackCamera: $isBackCamera")

                // Initialize camera if not initialized or if camera direction changed
                if (cameraManager.getCameraState() == "NOT_INITIALIZED" ||
                    cameraManager.isUsingBackCamera() != isBackCamera) {
                    cameraManager.initializeCamera(
                        context = context,
                        previewView = view,
                        lifecycleOwner = lifecycleOwner,
                        cameraExecutor = cameraExecutor,
                        onQRCodeDetected = { qrCode ->
                            if (isScanning) {
                                isScanning = false
                                onQRCodeDetected(qrCode)
                                // Re-enable scanning after a delay
                                Handler(Looper.getMainLooper()).postDelayed({
                                    isScanning = true
                                }, 2000)
                            }
                        },
                        onSuccess = { cam ->
                            camera = cam
                            cameraError = null
                            Log.d("CameraPreview", "Camera setup completed successfully")
                        },
                        onError = { error ->
                            cameraError = error
                            Log.e("CameraPreview", "Camera setup error: $error")
                        },
                        useBackCamera = isBackCamera
                    )
                } else {
                    Log.d("CameraPreview", "Camera already initialized with correct direction, skipping setup")
                }
            }
        )

        // Scanning overlay
        ScanningOverlay(
            modifier = Modifier.fillMaxSize()
        )

        // Error Display Only (instructional text now handled by InstructionalTextCard)
        cameraError?.let { errorMessage ->
            Card(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(16.dp)
                    .fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.9f)
                )
            ) {
                Text(
                    text = errorMessage,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}

@Composable
fun ScanningOverlay(modifier: Modifier = Modifier) {
    Canvas(modifier = modifier) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Calculate scanning area (square in center)
        val scanSize = minOf(canvasWidth, canvasHeight) * 0.7f
        val left = (canvasWidth - scanSize) / 2
        val top = (canvasHeight - scanSize) / 2

        // Draw semi-transparent overlay
        drawRect(
            color = Color.Black.copy(alpha = 0.5f),
            size = Size(canvasWidth, top)
        )
        drawRect(
            color = Color.Black.copy(alpha = 0.5f),
            topLeft = Offset(0f, top + scanSize),
            size = Size(canvasWidth, canvasHeight - top - scanSize)
        )
        drawRect(
            color = Color.Black.copy(alpha = 0.5f),
            topLeft = Offset(0f, top),
            size = Size(left, scanSize)
        )
        drawRect(
            color = Color.Black.copy(alpha = 0.5f),
            topLeft = Offset(left + scanSize, top),
            size = Size(canvasWidth - left - scanSize, scanSize)
        )

        // Draw scanning frame
        val strokeWidth = 4.dp.toPx()
        val cornerLength = 30.dp.toPx()

        // Top-left corner
        drawLine(
            color = Color.White,
            start = Offset(left, top + cornerLength),
            end = Offset(left, top),
            strokeWidth = strokeWidth
        )
        drawLine(
            color = Color.White,
            start = Offset(left, top),
            end = Offset(left + cornerLength, top),
            strokeWidth = strokeWidth
        )

        // Top-right corner
        drawLine(
            color = Color.White,
            start = Offset(left + scanSize - cornerLength, top),
            end = Offset(left + scanSize, top),
            strokeWidth = strokeWidth
        )
        drawLine(
            color = Color.White,
            start = Offset(left + scanSize, top),
            end = Offset(left + scanSize, top + cornerLength),
            strokeWidth = strokeWidth
        )

        // Bottom-left corner
        drawLine(
            color = Color.White,
            start = Offset(left, top + scanSize - cornerLength),
            end = Offset(left, top + scanSize),
            strokeWidth = strokeWidth
        )
        drawLine(
            color = Color.White,
            start = Offset(left, top + scanSize),
            end = Offset(left + cornerLength, top + scanSize),
            strokeWidth = strokeWidth
        )

        // Bottom-right corner
        drawLine(
            color = Color.White,
            start = Offset(left + scanSize - cornerLength, top + scanSize),
            end = Offset(left + scanSize, top + scanSize),
            strokeWidth = strokeWidth
        )
        drawLine(
            color = Color.White,
            start = Offset(left + scanSize, top + scanSize - cornerLength),
            end = Offset(left + scanSize, top + scanSize),
            strokeWidth = strokeWidth
        )
    }
}

internal class QRCodeAnalyzer(
    private val onQRCodeDetected: (String) -> Unit
) : ImageAnalysis.Analyzer {

    private val scanner = BarcodeScanning.getClient()
    private var isProcessing = false

    @OptIn(ExperimentalGetImage::class)
    override fun analyze(imageProxy: ImageProxy) {
        // Prevent multiple simultaneous processing
        if (isProcessing) {
            imageProxy.close()
            return
        }

        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            isProcessing = true
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)

            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    try {
                        if (barcodes.isNotEmpty()) {
                            barcodes.firstOrNull()?.rawValue?.let { value ->
                                Log.d("QRCodeAnalyzer", "QR Code detected: $value")
                                onQRCodeDetected(value)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("QRCodeAnalyzer", "Error processing detected QR code", e)
                    }
                }
                .addOnFailureListener { exception ->
                    Log.e("QRCodeAnalyzer", "Barcode scanning failed", exception)
                }
                .addOnCompleteListener {
                    try {
                        imageProxy.close()
                    } catch (e: Exception) {
                        Log.e("QRCodeAnalyzer", "Error closing image proxy", e)
                    } finally {
                        isProcessing = false
                    }
                }
        } else {
            imageProxy.close()
            isProcessing = false
        }
    }

    fun cleanup() {
        try {
            scanner.close()
            Log.d("QRCodeAnalyzer", "Scanner cleaned up")
        } catch (e: Exception) {
            Log.e("QRCodeAnalyzer", "Error cleaning up scanner", e)
        }
    }
}
