package com.example.qr.utils

import android.content.Context
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.ui.components.reactiveStringResource

/**
 * Utility object for QR code localization
 * Provides localized strings for QR types, display names, and status text
 */
object QRCodeLocalization {

    /**
     * Get localized QR type name
     */
    fun getLocalizedTypeName(context: Context, type: QRCodeType): String {
        return when (type) {
            QRCodeType.TEXT -> context.getString(R.string.qr_type_text)
            QRCodeType.URL -> context.getString(R.string.qr_type_url)
            QRCodeType.WIFI -> context.getString(R.string.qr_type_wifi)
            QRCodeType.EMAIL -> context.getString(R.string.qr_type_email)
            QRCodeType.PHONE -> context.getString(R.string.qr_type_phone)
            QRCodeType.SMS -> context.getString(R.string.qr_type_sms)
            QRCodeType.CONTACT -> context.getString(R.string.qr_type_contact)
            QRCodeType.LOCATION -> context.getString(R.string.qr_type_location)
            QRCodeType.CALENDAR -> context.getString(R.string.qr_type_calendar)
            QRCodeType.OTHER -> context.getString(R.string.qr_type_other)
        }
    }

    /**
     * Get localized status text
     */
    fun getLocalizedStatus(context: Context, isGenerated: Boolean): String {
        return if (isGenerated) {
            context.getString(R.string.status_generated)
        } else {
            context.getString(R.string.status_scanned)
        }
    }

    /**
     * Generate localized display name for QR code based on content and type
     */
    fun generateLocalizedDisplayName(context: Context, content: String, type: QRCodeType): String {
        val trimmedContent = content.trim()
        
        return when (type) {
            QRCodeType.URL -> {
                val domain = extractDomain(trimmedContent)
                context.getString(R.string.qr_display_website, domain)
            }
            QRCodeType.EMAIL -> {
                context.getString(R.string.qr_display_email, trimmedContent)
            }
            QRCodeType.PHONE -> {
                context.getString(R.string.qr_display_phone, trimmedContent)
            }
            QRCodeType.WIFI -> {
                val ssid = extractWiFiSSID(trimmedContent)
                context.getString(R.string.qr_display_wifi, ssid)
            }
            QRCodeType.CONTACT -> {
                val name = extractContactName(trimmedContent)
                context.getString(R.string.qr_display_contact, name)
            }
            QRCodeType.LOCATION -> {
                val location = extractLocationName(trimmedContent)
                context.getString(R.string.qr_display_location, location)
            }
            QRCodeType.CALENDAR -> {
                val event = extractCalendarEvent(trimmedContent)
                context.getString(R.string.qr_display_calendar, event)
            }
            QRCodeType.SMS -> {
                val number = extractSMSNumber(trimmedContent)
                context.getString(R.string.qr_display_sms, number)
            }
            QRCodeType.TEXT -> {
                if (trimmedContent.length > 30) {
                    "${trimmedContent.take(30)}..."
                } else {
                    trimmedContent
                }
            }
            QRCodeType.OTHER -> {
                if (trimmedContent.length > 30) {
                    "${trimmedContent.take(30)}..."
                } else {
                    trimmedContent
                }
            }
        }
    }

    /**
     * Get localized display name for existing QR code (runtime translation)
     */
    fun getLocalizedDisplayName(context: Context, qrCode: QRCodeData): String {
        return generateLocalizedDisplayName(context, qrCode.content, qrCode.type)
    }

    // Helper functions for extracting specific information from QR content
    private fun extractDomain(url: String): String {
        return try {
            val domain = url.substringAfter("://").substringBefore("/")
            if (domain.startsWith("www.")) domain.substring(4) else domain
        } catch (e: Exception) {
            url
        }
    }

    private fun extractWiFiSSID(wifiContent: String): String {
        return try {
            // WiFi format: WIFI:T:WPA;S:SSID;P:password;H:false;;
            val ssidMatch = Regex("S:([^;]+)").find(wifiContent)
            ssidMatch?.groupValues?.get(1) ?: "WiFi Network"
        } catch (e: Exception) {
            "WiFi Network"
        }
    }

    private fun extractContactName(contactContent: String): String {
        return try {
            // VCARD format: look for FN: (Full Name) or N: (Name)
            val lines = contactContent.split("\n")
            val fnLine = lines.find { it.startsWith("FN:") }
            val nLine = lines.find { it.startsWith("N:") }
            
            when {
                fnLine != null -> fnLine.substringAfter("FN:").trim()
                nLine != null -> {
                    val nameParts = nLine.substringAfter("N:").split(";")
                    "${nameParts.getOrNull(1) ?: ""} ${nameParts.getOrNull(0) ?: ""}".trim()
                }
                else -> "Contact"
            }
        } catch (e: Exception) {
            "Contact"
        }
    }

    private fun extractLocationName(locationContent: String): String {
        return try {
            // For geo: URLs, try to extract readable location
            if (locationContent.startsWith("geo:")) {
                val coords = locationContent.substringAfter("geo:").substringBefore("?")
                "Location ($coords)"
            } else {
                "Location"
            }
        } catch (e: Exception) {
            "Location"
        }
    }

    private fun extractCalendarEvent(calendarContent: String): String {
        return try {
            // VEVENT format: look for SUMMARY:
            val lines = calendarContent.split("\n")
            val summaryLine = lines.find { it.startsWith("SUMMARY:") }
            summaryLine?.substringAfter("SUMMARY:")?.trim() ?: "Calendar Event"
        } catch (e: Exception) {
            "Calendar Event"
        }
    }

    private fun extractSMSNumber(smsContent: String): String {
        return try {
            // SMS format: smsto:number:message or sms:number
            when {
                smsContent.startsWith("smsto:") -> {
                    smsContent.substringAfter("smsto:").substringBefore(":").trim()
                }
                smsContent.startsWith("sms:") -> {
                    smsContent.substringAfter("sms:").trim()
                }
                else -> smsContent
            }
        } catch (e: Exception) {
            smsContent
        }
    }
}

/**
 * Composable functions for reactive localization
 */

@Composable
fun getLocalizedQRTypeName(type: QRCodeType): String {
    return when (type) {
        QRCodeType.TEXT -> reactiveStringResource(R.string.qr_type_text)
        QRCodeType.URL -> reactiveStringResource(R.string.qr_type_url)
        QRCodeType.WIFI -> reactiveStringResource(R.string.qr_type_wifi)
        QRCodeType.EMAIL -> reactiveStringResource(R.string.qr_type_email)
        QRCodeType.PHONE -> reactiveStringResource(R.string.qr_type_phone)
        QRCodeType.SMS -> reactiveStringResource(R.string.qr_type_sms)
        QRCodeType.CONTACT -> reactiveStringResource(R.string.qr_type_contact)
        QRCodeType.LOCATION -> reactiveStringResource(R.string.qr_type_location)
        QRCodeType.CALENDAR -> reactiveStringResource(R.string.qr_type_calendar)
        QRCodeType.OTHER -> reactiveStringResource(R.string.qr_type_other)
    }
}

@Composable
fun getLocalizedQRStatus(isGenerated: Boolean): String {
    return if (isGenerated) {
        reactiveStringResource(R.string.status_generated)
    } else {
        reactiveStringResource(R.string.status_scanned)
    }
}

@Composable
fun getLocalizedQRDisplayName(qrCode: QRCodeData): String {
    val trimmedContent = qrCode.content.trim()

    // Debug logging
    android.util.Log.d("QRLocalization", "Getting localized display name for type: ${qrCode.type}, content: ${trimmedContent.take(20)}")

    return when (qrCode.type) {
        QRCodeType.URL -> {
            val domain = extractDomain(trimmedContent)
            reactiveStringResource(R.string.qr_display_website, domain)
        }
        QRCodeType.EMAIL -> {
            reactiveStringResource(R.string.qr_display_email, trimmedContent)
        }
        QRCodeType.PHONE -> {
            reactiveStringResource(R.string.qr_display_phone, trimmedContent)
        }
        QRCodeType.WIFI -> {
            val ssid = extractWiFiSSID(trimmedContent)
            reactiveStringResource(R.string.qr_display_wifi, ssid)
        }
        QRCodeType.CONTACT -> {
            val name = extractContactName(trimmedContent)
            reactiveStringResource(R.string.qr_display_contact, name)
        }
        QRCodeType.LOCATION -> {
            val location = extractLocationName(trimmedContent)
            reactiveStringResource(R.string.qr_display_location, location)
        }
        QRCodeType.CALENDAR -> {
            val event = extractCalendarEvent(trimmedContent)
            reactiveStringResource(R.string.qr_display_calendar, event)
        }
        QRCodeType.SMS -> {
            val number = extractSMSNumber(trimmedContent)
            reactiveStringResource(R.string.qr_display_sms, number)
        }
        QRCodeType.TEXT -> {
            if (trimmedContent.length > 30) {
                "${trimmedContent.take(30)}..."
            } else {
                trimmedContent
            }
        }
        QRCodeType.OTHER -> {
            if (trimmedContent.length > 30) {
                "${trimmedContent.take(30)}..."
            } else {
                trimmedContent
            }
        }
    }
}

// Helper functions for extracting specific information from QR content
private fun extractDomain(url: String): String {
    return try {
        val domain = url.substringAfter("://").substringBefore("/")
        if (domain.startsWith("www.")) domain.substring(4) else domain
    } catch (e: Exception) {
        url
    }
}

private fun extractWiFiSSID(wifiContent: String): String {
    return try {
        // WiFi format: WIFI:T:WPA;S:SSID;P:password;H:false;;
        val ssidMatch = Regex("S:([^;]+)").find(wifiContent)
        ssidMatch?.groupValues?.get(1) ?: "WiFi Network"
    } catch (e: Exception) {
        "WiFi Network"
    }
}

private fun extractContactName(contactContent: String): String {
    return try {
        // VCARD format: look for FN: (Full Name) or N: (Name)
        val lines = contactContent.split("\n")
        val fnLine = lines.find { it.startsWith("FN:") }
        val nLine = lines.find { it.startsWith("N:") }

        when {
            fnLine != null -> fnLine.substringAfter("FN:").trim()
            nLine != null -> {
                val nameParts = nLine.substringAfter("N:").split(";")
                "${nameParts.getOrNull(1) ?: ""} ${nameParts.getOrNull(0) ?: ""}".trim()
            }
            else -> "Contact"
        }
    } catch (e: Exception) {
        "Contact"
    }
}

private fun extractLocationName(locationContent: String): String {
    return try {
        // For geo: URLs, try to extract readable location
        if (locationContent.startsWith("geo:")) {
            val coords = locationContent.substringAfter("geo:").substringBefore("?")
            "Location ($coords)"
        } else {
            "Location"
        }
    } catch (e: Exception) {
        "Location"
    }
}

private fun extractCalendarEvent(calendarContent: String): String {
    return try {
        // VEVENT format: look for SUMMARY:
        val lines = calendarContent.split("\n")
        val summaryLine = lines.find { it.startsWith("SUMMARY:") }
        summaryLine?.substringAfter("SUMMARY:")?.trim() ?: "Calendar Event"
    } catch (e: Exception) {
        "Calendar Event"
    }
}

private fun extractSMSNumber(smsContent: String): String {
    return try {
        // SMS format: smsto:number:message or sms:number
        when {
            smsContent.startsWith("smsto:") -> {
                smsContent.substringAfter("smsto:").substringBefore(":").trim()
            }
            smsContent.startsWith("sms:") -> {
                smsContent.substringAfter("sms:").trim()
            }
            else -> smsContent
        }
    } catch (e: Exception) {
        smsContent
    }
}
