package com.example.qr.presentation.screens.settings

import android.app.Application
import android.content.Context
import android.media.AudioManager
import android.media.ToneGenerator
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.example.qr.R
import com.example.qr.data.preferences.AppSettings
import com.example.qr.data.repository.SettingsRepository
import com.example.qr.utils.TutorialManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.flow.first

class SettingsViewModel(application: Application) : AndroidViewModel(application) {

    private val settingsRepository = SettingsRepository(application)
    private val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        val vibratorManager = application.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
        vibratorManager.defaultVibrator
    } else {
        @Suppress("DEPRECATION")
        application.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
    }

    private val audioManager = application.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private var toneGenerator: ToneGenerator? = null

    init {
        try {
            // Use gentler volume for premium sound quality
            toneGenerator = ToneGenerator(AudioManager.STREAM_NOTIFICATION, 65)
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "Error initializing ToneGenerator", e)
        }
    }

    private val _settings = MutableStateFlow(AppSettings())
    val settings: StateFlow<AppSettings> = _settings.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadSettings()
    }

    private fun loadSettings() {
        settingsRepository.allSettings
            .onEach { appSettings ->
                _settings.value = appSettings
                Log.d("SettingsViewModel", "Settings loaded: $appSettings")
            }
            .catch { exception ->
                // Only log non-cancellation exceptions
                if (exception !is CancellationException) {
                    Log.e("SettingsViewModel", "Error loading settings", exception)
                }
            }
            .launchIn(viewModelScope)
    }

    // Helper function to handle settings operations with proper error handling
    private fun executeSettingsOperation(
        operation: suspend () -> Unit,
        operationName: String
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                operation()
                Log.d("SettingsViewModel", "$operationName completed successfully")
            } catch (e: CancellationException) {
                // Re-throw cancellation exceptions to maintain coroutine cancellation behavior
                throw e
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error in $operationName", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Scanning settings
    fun setAutoSaveScanned(enabled: Boolean) {
        executeSettingsOperation(
            operation = { settingsRepository.setAutoSaveScanned(enabled) },
            operationName = "setAutoSaveScanned($enabled)"
        )
    }

    fun setVibrateOnScan(enabled: Boolean) {
        executeSettingsOperation(
            operation = {
                settingsRepository.setVibrateOnScan(enabled)
                // Test vibration if enabled
                if (enabled) {
                    testVibration()
                }
            },
            operationName = "setVibrateOnScan($enabled)"
        )
    }

    fun setPlaySoundOnScan(enabled: Boolean) {
        executeSettingsOperation(
            operation = {
                settingsRepository.setPlaySoundOnScan(enabled)
                // Test sound if enabled
                if (enabled) {
                    testSound()
                }
            },
            operationName = "setPlaySoundOnScan($enabled)"
        )
    }

    fun setAutoOpenLinks(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setAutoOpenLinks(enabled)
                Log.d("SettingsViewModel", "Auto-open links set to: $enabled")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting auto-open links", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Generation settings
    fun setAutoSaveGenerated(enabled: Boolean) {
        executeSettingsOperation(
            operation = { settingsRepository.setAutoSaveGenerated(enabled) },
            operationName = "setAutoSaveGenerated($enabled)"
        )
    }

    fun setDefaultQRSize(size: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setDefaultQRSize(size)

                // Get pixel value for logging
                val pixelValue = settingsRepository.getQRSizePixels(size)
                Log.d("SettingsViewModel", "Default QR size set to: $size ($pixelValue pixels)")

                // Verify the setting was saved correctly
                val savedSize = settingsRepository.defaultQRSize.first()
                Log.d("SettingsViewModel", "Verified saved size: $savedSize")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting default QR size", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun setDefaultQRFormat(format: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setDefaultQRFormat(format)
                Log.d("SettingsViewModel", "Default QR format set to: $format")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting default QR format", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // UI settings
    fun setThemeMode(mode: String) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setThemeMode(mode)
                Log.d("SettingsViewModel", "Theme mode set to: $mode")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting theme mode", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun setShowTutorials(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setShowTutorials(enabled)
                Log.d("SettingsViewModel", "Show tutorials set to: $enabled")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting show tutorials", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun setEnableAnimations(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setEnableAnimations(enabled)
                Log.d("SettingsViewModel", "Enable animations set to: $enabled")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting enable animations", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Privacy settings
    fun setAnalyticsEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setAnalyticsEnabled(enabled)
                Log.d("SettingsViewModel", "Analytics enabled set to: $enabled")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting analytics enabled", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    fun setCrashReportingEnabled(enabled: Boolean) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                settingsRepository.setCrashReportingEnabled(enabled)
                Log.d("SettingsViewModel", "Crash reporting enabled set to: $enabled")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error setting crash reporting enabled", e)
            } finally {
                _isLoading.value = false
            }
        }
    }



    // Utility functions
    fun triggerVibration() {
        if (_settings.value.vibrateOnScan) {
            testVibration()
        }
    }

    private fun testVibration() {
        try {
            vibrator?.let { vib ->
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vib.vibrate(VibrationEffect.createOneShot(100, VibrationEffect.DEFAULT_AMPLITUDE))
                } else {
                    @Suppress("DEPRECATION")
                    vib.vibrate(100)
                }
            }
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "Error triggering vibration", e)
        }
    }

    fun testSound() {
        try {
            // Check if audio is enabled
            val ringerMode = audioManager.ringerMode
            val notificationVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)

            if (ringerMode == AudioManager.RINGER_MODE_SILENT || notificationVolume == 0) {
                Log.d("SettingsViewModel", "Audio is muted, cannot test sound")
                return
            }

            // Play enhanced pleasant sound pattern
            toneGenerator?.let { generator ->
                playEnhancedTestSound(generator)
            }
            Log.d("SettingsViewModel", "Enhanced test sound played")
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "Error playing test sound", e)
        }
    }

    private fun playEnhancedTestSound(generator: ToneGenerator) {
        try {
            // Single pleasant tone matching the SoundManager
            generator.startTone(ToneGenerator.TONE_DTMF_8, 200) // Pleasant single tone

            Log.d("SettingsViewModel", "Pleasant single test tone played")
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "Error playing enhanced test sound", e)
            // Fallback to simple beep
            generator.startTone(ToneGenerator.TONE_PROP_BEEP, 200)
        }
    }

    fun getQRSizeOptions(): List<String> = listOf(
        getApplication<Application>().getString(R.string.size_small),
        getApplication<Application>().getString(R.string.size_medium),
        getApplication<Application>().getString(R.string.size_large),
        getApplication<Application>().getString(R.string.size_extra_large)
    )

    fun getQRFormatOptions(): List<String> = listOf(
        getApplication<Application>().getString(R.string.format_png),
        getApplication<Application>().getString(R.string.format_jpeg),
        getApplication<Application>().getString(R.string.format_pdf)
    )

    fun getThemeOptions(): List<String> = listOf(
        getApplication<Application>().getString(R.string.theme_light),
        getApplication<Application>().getString(R.string.theme_dark),
        getApplication<Application>().getString(R.string.theme_system)
    )

    // Reset all settings to default values
    fun resetAllSettings() {
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // Reset all settings to their default values
                settingsRepository.setAutoSaveScanned(true)
                settingsRepository.setVibrateOnScan(true)
                settingsRepository.setPlaySoundOnScan(false)
                settingsRepository.setAutoOpenLinks(true)
                settingsRepository.setAutoSaveGenerated(true)
                settingsRepository.setDefaultQRSize("Medium") // Keep English for internal storage
                settingsRepository.setDefaultQRFormat("PNG") // Keep English for internal storage
                settingsRepository.setThemeMode("System") // Keep English for internal storage
                settingsRepository.setShowTutorials(true)
                settingsRepository.setEnableAnimations(true)
                settingsRepository.setAnalyticsEnabled(false)
                settingsRepository.setCrashReportingEnabled(false)
                settingsRepository.setSelectedLanguage("en")
                settingsRepository.setFirstTimeLanguageSelection(true)

                // Reset tutorial states
                TutorialManager.resetAllTutorials(getApplication())

                Log.d("SettingsViewModel", "All settings reset to default values")
            } catch (e: Exception) {
                Log.e("SettingsViewModel", "Error resetting settings", e)
            } finally {
                _isLoading.value = false
            }
        }
    }

    // Language management functions
    fun setSelectedLanguage(languageCode: String) {
        executeSettingsOperation(
            operation = {
                settingsRepository.setSelectedLanguage(languageCode)
            },
            operationName = "setSelectedLanguage($languageCode)"
        )
    }

    fun setFirstTimeLanguageSelection(isFirstTime: Boolean) {
        executeSettingsOperation(
            operation = {
                settingsRepository.setFirstTimeLanguageSelection(isFirstTime)
            },
            operationName = "setFirstTimeLanguageSelection($isFirstTime)"
        )
    }

    override fun onCleared() {
        super.onCleared()
        try {
            toneGenerator?.release()
            toneGenerator = null
        } catch (e: Exception) {
            Log.e("SettingsViewModel", "Error cleaning up ToneGenerator", e)
        }
    }
}
