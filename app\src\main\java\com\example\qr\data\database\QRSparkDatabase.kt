package com.example.qr.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.example.qr.data.dao.QRCodeDao
import com.example.qr.data.entity.QRCodeConverters
import com.example.qr.data.entity.QRCodeEntity

@Database(
    entities = [QRCodeEntity::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(QRCodeConverters::class)
abstract class QRSparkDatabase : RoomDatabase() {
    
    abstract fun qrCodeDao(): QRCodeDao
    
    companion object {
        @Volatile
        private var INSTANCE: QRSparkDatabase? = null
        
        private const val DATABASE_NAME = "qr_spark_database"
        
        fun getDatabase(context: Context): QRSparkDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    QRSparkDatabase::class.java,
                    DATABASE_NAME
                )
                .addCallback(DatabaseCallback())
                .build()
                INSTANCE = instance
                instance
            }
        }
        
        // Database callback to handle initialization
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // Database created - sample data will be inserted by repository
            }
        }
        
        // Future migration example (for when we need to update schema)
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example migration - add new column
                // database.execSQL("ALTER TABLE qr_codes ADD COLUMN newColumn TEXT")
            }
        }
    }
}
