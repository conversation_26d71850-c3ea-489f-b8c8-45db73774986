<resources>
    <string name="app_name">QR Scanner &amp; Generator</string>

    <!-- Navigation -->
    <string name="nav_home">Home</string>
    <string name="nav_scanner">Scanner</string>
    <string name="nav_generator">Generator</string>
    <string name="nav_history">History</string>

    <!-- Home Screen -->
    <string name="home_title">QR Code App</string>
    <string name="home_subtitle">Your ultimate QR companion</string>
    <string name="home_scan_title">Scan QR</string>
    <string name="home_scan_description">Instantly decode QR codes</string>
    <string name="home_generate_title">Generate QR</string>
    <string name="home_generate_description">Craft your own unique codes</string>
    <string name="home_history_title">My Collection</string>
    <string name="home_history_description">Access your scans &amp; creations</string>

    <!-- Scanner Screen -->
    <string name="scanner_title">Scan QR</string>
    <string name="scanner_permission_required">Camera Permission Required</string>
    <string name="scanner_permission_title">Camera Permission Required</string>
    <string name="scanner_permission_message">This app needs camera access to scan QR codes.</string>
    <string name="scanner_flash_on">Turn on flash</string>
    <string name="scanner_flash_off">Turn off flash</string>
    <string name="scanner_permission_rationale">This app needs camera access to scan QR codes. Please grant camera permission to continue.</string>
    <string name="scanner_permission_denied">Camera Permission Denied</string>
    <string name="scanner_permission_settings">Camera access is required to scan QR codes. Please enable camera permission in app settings.</string>
    <string name="scanner_grant_permission">Grant Permission</string>
    <string name="scanner_try_again">Try Again</string>

    <!-- Generator Screen -->
    <string name="generator_title">Generate QR Code</string>
    <string name="generator_select_type">Select QR Code Type</string>
    <string name="generator_select_type_desc">Select the type of QR code you want to create</string>
    <string name="generator_generating">Generating QR Code...</string>
    <string name="generator_text_content">Text Content</string>
    <string name="generator_text_label">Enter your text</string>
    <string name="generator_text_placeholder">Type anything you want to share...</string>
    <string name="generator_text_hint">Enter your text here</string>
    <string name="generator_url_hint">https://example.com</string>
    <string name="generator_email_hint"><EMAIL></string>
    <string name="generator_phone_hint">+1234567890</string>
    <string name="generator_text_title">Text</string>
    <string name="generator_text_description">Plain text content</string>
    <string name="generator_url_title">Website</string>
    <string name="generator_url_description">Web URL or link</string>
    <string name="generator_wifi_title">WiFi</string>
    <string name="generator_wifi_description">WiFi network credentials</string>
    <string name="generator_email_title">Email</string>
    <string name="generator_email_description">Email address</string>
    <string name="generator_phone_title">Phone</string>
    <string name="generator_phone_description">Phone number</string>
    <string name="generator_sms_title">SMS</string>
    <string name="generator_sms_description">Text message</string>
    <string name="generator_contact_title">Contact</string>
    <string name="generator_contact_description">Contact information</string>
    <string name="generator_location_title">Location</string>
    <string name="generator_location_description">Geographic coordinates</string>
    <string name="generator_generate_button">Generate QR Code</string>

    <!-- History Screen -->
    <string name="history_title">History</string>
    <string name="history_tab_all">All</string>
    <string name="history_tab_scanned">Scanned</string>
    <string name="history_tab_generated">Generated</string>
    <string name="history_tab_favorites">Favorites</string>
    <string name="history_search_hint">Search QR codes…</string>
    <string name="history_empty">No QR codes yet</string>
    <string name="history_empty_title">No QR codes yet</string>
    <string name="history_empty_message">Scan or generate your first QR code to get started</string>
    <string name="history_no_results">No results found</string>
    <string name="history_add_favorite">Add to favorites</string>
    <string name="history_remove_favorite">Remove from favorites</string>

    <!-- Common -->
    <string name="back">Back</string>
    <string name="search">Search</string>
    <string name="close">Close</string>
    <string name="more_options">More options</string>
    <string name="share">Share</string>
    <string name="copy">Copy</string>
    <string name="delete">Delete</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="ok">OK</string>

    <!-- QR Code Types -->
    <string name="qr_type_text">Text</string>
    <string name="qr_type_url">Website</string>
    <string name="qr_type_wifi">WiFi</string>
    <string name="qr_type_email">Email</string>
    <string name="qr_type_phone">Phone</string>
    <string name="qr_type_sms">SMS</string>
    <string name="qr_type_contact">Contact</string>
    <string name="qr_type_location">Location</string>
    <string name="qr_type_calendar">Calendar</string>
    <string name="qr_type_other">Other</string>

    <!-- Actions -->
    <string name="action_open_website">Open Website</string>
    <string name="action_connect_wifi">Connect to WiFi</string>
    <string name="action_send_email">Send Email</string>
    <string name="action_call">Call</string>
    <string name="action_send_sms">Send SMS</string>
    <string name="action_add_contact">Add Contact</string>
    <string name="action_open_maps">Open in Maps</string>
    <string name="action_add_calendar">Add to Calendar</string>
    <string name="action_copy_text">Copy Text</string>

    <!-- Settings -->
    <string name="settings_title">Settings</string>
    <string name="settings_scanning">Scanning</string>
    <string name="settings_auto_save_scanned">Auto-save scanned QR codes</string>
    <string name="settings_auto_save_scanned_desc">Automatically save QR codes to your collection</string>
    <string name="settings_vibrate_on_scan">Vibrate on scan</string>
    <string name="settings_vibrate_on_scan_desc">Haptic feedback when QR code is detected</string>
    <string name="settings_play_sound_on_scan">Play sound on scan</string>
    <string name="settings_play_sound_on_scan_desc">Audio feedback when QR code is detected</string>
    <string name="settings_auto_open_links">Auto-open links</string>
    <string name="settings_auto_open_links_desc">Automatically open URLs when scanned</string>

    <string name="settings_generation">Generation</string>
    <string name="settings_auto_save_generated">Auto-save generated QR codes</string>
    <string name="settings_auto_save_generated_desc">Automatically save created QR codes</string>
    <string name="settings_default_qr_size">Default QR code size</string>
    <string name="settings_default_qr_size_desc">Set the default size for generated QR codes</string>
    <string name="settings_default_qr_format">Default QR format</string>
    <string name="settings_default_qr_format_desc">Set the default format for generated QR codes</string>

    <string name="settings_appearance">UI &amp; Experience</string>
    <string name="settings_theme_mode">Theme mode</string>
    <string name="settings_theme_mode_desc">Choose between light, dark, or system theme</string>
    <string name="settings_enable_animations">Enable animations</string>
    <string name="settings_enable_animations_desc">Use smooth animations throughout the app</string>
    <string name="settings_language">Language</string>
    <string name="settings_language_desc">Change app language</string>

    <string name="settings_privacy">Privacy</string>
    <string name="settings_analytics">Analytics</string>
    <string name="settings_analytics_desc">Help improve the app by sharing anonymous usage data</string>
    <string name="settings_crash_reporting">Crash reporting</string>
    <string name="settings_crash_reporting_desc">Automatically send crash reports to help improve the app</string>
    <string name="settings_privacy_policy">Privacy Policy</string>
    <string name="settings_privacy_policy_desc">View our privacy policy</string>

    <string name="settings_about">About</string>
    <string name="settings_app_version">App Version</string>
    <string name="settings_app_version_desc">QR Spark v1.0.0 • Tap to copy</string>
    <string name="settings_share_app">Share QR Spark</string>
    <string name="settings_share_app_desc">Tell your friends about QR Spark</string>

    <!-- Additional Settings -->
    <string name="settings_show_tutorials">Show tutorials</string>
    <string name="settings_show_tutorials_desc">Display helpful tips and tutorials</string>
    <string name="settings_reset_all">Reset to Defaults</string>
    <string name="settings_reset_all_desc">Restore all settings to their default values</string>
    <string name="settings_reset_dialog_title">Reset All Settings</string>
    <string name="settings_reset_dialog_message">This will restore all settings to their default values:</string>
    <string name="settings_reset_dialog_warning">This action cannot be undone.</string>
    <string name="settings_reset_items_1">• Auto-save preferences</string>
    <string name="settings_reset_items_2">• Vibration and sound settings</string>
    <string name="settings_reset_items_3">• Default QR code size and format</string>
    <string name="settings_reset_items_4">• UI and animation preferences</string>
    <string name="settings_reset_confirm">Reset</string>
    <string name="settings_restore">Restore</string>
    <string name="settings_app_settings_title">App Settings</string>
    <string name="settings_app_settings_subtitle">Customize your QR Spark experience</string>
    <string name="settings_rate_app">Rate QR Spark</string>
    <string name="settings_rate_app_desc">Help us improve with your feedback</string>
    <string name="settings_size_dialog_title">Select QR Code Size</string>
    <string name="settings_format_dialog_title">Select QR Code Format</string>

    <!-- Size and Format Options -->
    <string name="size_small">Small</string>
    <string name="size_medium">Medium</string>
    <string name="size_large">Large</string>
    <string name="size_extra_large">Extra Large</string>
    <string name="format_png">PNG</string>
    <string name="format_jpeg">JPEG</string>
    <string name="format_svg">SVG</string>
    <string name="format_pdf">PDF</string>
    <string name="theme_light">Light</string>
    <string name="theme_dark">Dark</string>
    <string name="theme_system">System</string>

    <!-- Theme Descriptions -->
    <string name="theme_light_description">Always use light theme</string>
    <string name="theme_dark_description">Always use dark theme</string>
    <string name="theme_system_description">Follow system theme setting</string>

    <!-- Theme Dialog -->
    <string name="settings_theme_dialog_title">Select Theme</string>

    <!-- Language Selection -->
    <string name="language_welcome_title">Welcome to QR Spark!</string>
    <string name="language_welcome_subtitle">Please select your preferred language to get started</string>
    <string name="language_select_title">Select Language</string>
    <string name="language_current">Current language</string>

    <!-- Status Messages -->
    <string name="status_auto_saved">Automatically saved to collection</string>
    <string name="status_favorited">Added to favorites</string>
    <string name="status_not_saved">Not saved (auto-save disabled)</string>
    <string name="status_loading">Loading...</string>
    <string name="status_done">Done</string>
    <string name="status_failed">Failed</string>

    <!-- Common Actions -->
    <string name="action_copy">Copy</string>
    <string name="action_share">Share</string>
    <string name="action_save">Save</string>
    <string name="action_delete">Delete</string>
    <string name="action_edit">Edit</string>
    <string name="action_favorite">Favorite</string>
    <string name="action_cancel">Cancel</string>
    <string name="action_ok">OK</string>
    <string name="action_continue">Continue</string>
    <string name="action_apply">Apply</string>
    <string name="action_close">Close</string>
    <string name="action_done">Done</string>
    <string name="action_add_to_favorites">Add to Favorites</string>
    <string name="action_favorited">Favorited</string>
    <string name="action_saving">Saving...</string>
    <string name="action_saved">Saved</string>
    <string name="action_failed">Failed</string>
    <string name="action_sharing">Sharing...</string>
    <string name="action_shared">Shared!</string>
    <string name="action_copying">Copying...</string>
    <string name="action_copied">Copied!</string>
    <string name="action_loading">Loading...</string>

    <!-- Messages -->
    <string name="message_copied">Copied to clipboard</string>
    <string name="message_saved">Saved successfully</string>
    <string name="message_deleted">Deleted successfully</string>
    <string name="message_error">An error occurred</string>
    <string name="message_no_camera">Camera not available</string>
    <string name="message_invalid_qr">Invalid QR code</string>
    <string name="message_url_copied">URL copied to clipboard</string>
    <string name="message_text_copied">Text copied to clipboard</string>
    <string name="message_qr_type_not_supported">QR code type not supported yet</string>
    <string name="message_failed_qr_action">Failed to handle QR code action: %s</string>
    <string name="message_content_copied">Content copied to clipboard</string>
    <string name="message_qr_saved_gallery">QR code saved to Pictures/QR Spark</string>
    <string name="message_failed_save_qr">Failed to save QR code</string>

    <!-- Status -->
    <string name="status_generated">Generated</string>
    <string name="status_scanned">Scanned</string>
    <string name="status_saved">Saved to collection</string>

    <!-- Permissions -->
    <string name="permission_camera_required">Camera Permission Required</string>
    <string name="permission_camera_denied">Camera Permission Denied</string>
    <string name="permission_camera_message">Camera access is required to scan QR codes. Please grant camera permission to continue.</string>
    <string name="permission_grant">Grant Permission</string>
    <string name="permission_try_again">Try Again</string>

    <!-- Dialog Actions -->
    <string name="dialog_copy">Copy</string>
    <string name="dialog_share">Share</string>
    <string name="dialog_save">Save</string>
    <string name="dialog_open">Open</string>
    <string name="dialog_call">Call</string>
    <string name="dialog_send_sms">Send SMS</string>
    <string name="dialog_send_email">Send Email</string>
    <string name="dialog_connect_wifi">Connect WiFi</string>
    <string name="dialog_view_location">View Location</string>
    <string name="dialog_add_contact">Add Contact</string>
    <string name="dialog_dismiss">Dismiss</string>
    <string name="dialog_cancel">Cancel</string>
    <string name="dialog_ok">OK</string>
    <string name="dialog_yes">Yes</string>
    <string name="dialog_no">No</string>

    <!-- QR Code Result Dialog -->
    <string name="qr_result_title">QR Code Result</string>
    <string name="qr_result_content">Content</string>
    <string name="qr_result_type">Type</string>
    <string name="qr_result_created">Created</string>
    <string name="qr_result_auto_saved">Auto-saved</string>
    <string name="qr_result_not_saved">Not saved</string>
    <string name="qr_result_favorite_added">Added to favorites</string>
    <string name="qr_result_favorite_removed">Removed from favorites</string>

    <!-- Generator Result Dialog -->
    <string name="qr_generated_title">QR Code Generated!</string>
    <string name="qr_generated_success">Your QR code has been generated successfully</string>
    <string name="qr_generated_save_gallery">Save to Gallery</string>
    <string name="qr_generated_share_image">Share Image</string>
    <string name="qr_generated_regenerate">Regenerate</string>
    <string name="qr_generated_customize">Customize</string>

    <!-- Scanner Result Dialog -->
    <string name="qr_scanned_title">QR Code Scanned!</string>
    <string name="qr_scanned_success">QR code has been scanned successfully</string>

    <!-- Save Confirmation Dialog -->
    <string name="save_dialog_title">Save QR Code</string>
    <string name="save_dialog_message">Do you want to save this QR code to your gallery?</string>
    <string name="save_dialog_success">QR code saved successfully</string>
    <string name="save_dialog_error">Error saving QR code</string>

    <!-- Delete Confirmation Dialog -->
    <string name="dialog_delete_title">Delete QR Code</string>
    <string name="dialog_delete_message">Are you sure you want to delete \"%1$s\"? This action cannot be undone.</string>



    <!-- QR Code Display Name Prefixes -->
    <string name="qr_display_website">Website: %1$s</string>
    <string name="qr_display_email">Email: %1$s</string>
    <string name="qr_display_phone">Phone: %1$s</string>
    <string name="qr_display_wifi">WiFi: %1$s</string>
    <string name="qr_display_contact">Contact: %1$s</string>
    <string name="qr_display_location">Location: %1$s</string>
    <string name="qr_display_calendar">Calendar: %1$s</string>
    <string name="qr_display_sms">SMS: %1$s</string>



    <!-- Tab Names -->
    <string name="tab_all">All</string>
    <string name="tab_scanned">Scanned</string>
    <string name="tab_generated">Generated</string>
    <string name="tab_favorites">Favorites</string>

    <!-- Shorter Tab Names for Better Multilingual Support -->
    <string name="tab_all_short">All</string>
    <string name="tab_scanned_short">Scan</string>
    <string name="tab_generated_short">Gen</string>
    <string name="tab_favorites_short">Fav</string>

    <!-- History Screen Headers -->
    <string name="history_title_my">My</string>
    <string name="history_title_collection">Collection</string>

    <!-- Empty State Messages -->
    <string name="history_empty_subtitle">Scan or generate your first QR code to get started</string>
    <string name="history_search_no_results">No results found</string>

    <!-- Bulk Actions -->
    <string name="bulk_action_delete_all">Delete All</string>
    <string name="bulk_action_delete_non_favorites">Delete Non-Favorites</string>
    <string name="bulk_action_more_options">More options</string>

    <!-- Search -->
    <string name="search_placeholder">Search QR codes...</string>
    <string name="search_close">Close search</string>
    <string name="search_open">Search</string>

    <!-- Scanner Screen -->
    <string name="scanner_instruction">Point your camera at a QR code to scan</string>
    <string name="scanner_upload_image">Upload QR Image</string>
    <string name="cd_upload_qr_image">Upload QR Image</string>

    <!-- Content Descriptions -->
    <string name="cd_back">Back</string>
    <string name="cd_search">Search</string>
    <string name="cd_close_search">Close search</string>
    <string name="cd_more_options">More options</string>
    <string name="cd_add_to_favorites">Add to favorites</string>
    <string name="cd_remove_from_favorites">Remove from favorites</string>
    <string name="cd_delete_qr_code">Delete QR code</string>

    <!-- Share Dialog -->
    <string name="share_dialog_title">Share QR Code</string>
    <string name="share_dialog_image">Share as image</string>
    <string name="share_dialog_text">Share as text</string>
    <string name="share_dialog_both">Share both</string>

    <!-- Help & FAQ Screen -->
    <string name="help_title">Help &amp; FAQ</string>
    <string name="help_subtitle">Your ultimate QR companion</string>
    <string name="help_faq_title">Frequently Asked Questions</string>
    <string name="help_faq_subtitle">Find answers to common questions about QR Spark</string>
    <string name="help_need_more_title">Need More Help?</string>
    <string name="help_need_more_subtitle">If you can\'t find the answer you\'re looking for, feel free to contact our support team.</string>
    <string name="help_contact_support">Contact Support</string>

    <!-- FAQ Questions -->
    <string name="faq_scan_question">How do I scan a QR code?</string>
    <string name="faq_scan_answer">Tap \'Scan QR\' on the home screen, point your camera at the QR code, and it will be automatically detected and processed.</string>

    <string name="faq_create_question">How do I create a QR code?</string>
    <string name="faq_create_answer">Tap \'Generate QR\' on the home screen, choose the type of QR code you want to create, fill in the required information, and tap \'Generate QR Code\'.</string>

    <string name="faq_saved_question">Where are my QR codes saved?</string>
    <string name="faq_saved_answer">All your scanned and generated QR codes are automatically saved in \'My Collection\'. You can access them anytime from the home screen.</string>

    <string name="faq_favorites_question">How do I mark QR codes as favorites?</string>
    <string name="faq_favorites_answer">When viewing a QR code, tap the heart icon to add it to your favorites. Favorite QR codes appear at the top of your collection.</string>

    <string name="faq_share_question">Can I share QR codes?</string>
    <string name="faq_share_answer">Yes! When viewing a QR code, tap the share button to share the QR code image or its content with other apps.</string>

    <string name="faq_types_question">What types of QR codes can I create?</string>
    <string name="faq_types_answer">You can create QR codes for text, URLs, WiFi credentials, email addresses, phone numbers, SMS messages, contacts, and locations.</string>

    <!-- Generator Forms -->
    <string name="form_wifi_network">WiFi Network</string>
    <string name="form_network_name">Network Name (SSID)</string>
    <string name="form_password">Password</string>
    <string name="form_security_type">Security Type</string>
    <string name="form_hidden_network">Hidden Network</string>
    <string name="form_email_message">Email Message</string>
    <string name="form_email_address">Email Address</string>
    <string name="form_email_placeholder"><EMAIL></string>
    <string name="form_subject">Subject</string>
    <string name="form_message_body">Message Body</string>
    <string name="form_phone_number">Phone Number</string>
    <string name="form_sms_message">SMS Message</string>
    <string name="form_contact_info">Contact Information</string>
    <string name="form_first_name">First Name</string>
    <string name="form_last_name">Last Name</string>
    <string name="form_organization">Organization</string>
    <string name="form_location_coordinates">Location Coordinates</string>
    <string name="form_latitude">Latitude</string>
    <string name="form_longitude">Longitude</string>
    <string name="form_url_address">URL Address</string>
    <string name="form_url_placeholder">https://example.com</string>

    <!-- Navigation Titles -->
    <string name="nav_title_home">QR Spark</string>
    <string name="nav_title_scanner">Scan QR</string>
    <string name="nav_title_generator">Generate QR</string>
    <string name="nav_title_history">My Collection</string>
    <string name="nav_title_settings">Settings</string>
    <string name="nav_title_help">Help &amp; FAQ</string>

    <!-- Error Messages -->
    <string name="error_content_empty">Content cannot be empty</string>
    <string name="error_failed_favorite">Failed to update favorite: %s</string>
    <string name="error_invalid_email">Please enter a valid email address</string>
    <string name="error_invalid_phone">Please enter a valid phone number</string>
    <string name="error_invalid_url">Please enter a valid URL</string>
    <string name="error_invalid_coordinates">Please enter valid coordinates</string>

    <!-- Search and History -->
    <string name="search_qr_codes">Search QR codes...</string>
    <string name="bulk_actions">Bulk Actions</string>
    <string name="delete_all">Delete All</string>
    <string name="delete_non_favorites">Delete Non-Favorites</string>

    <!-- Share Messages -->
    <string name="share_app_subject">Check out QR Spark!</string>
    <string name="share_app_message">🌟 QR Spark - Your Ultimate QR Companion! 🌟\n\nI\'ve been using QR Spark and it\'s amazing! You can:\n\n📱 Scan QR codes instantly\n✨ Generate custom QR codes\n💾 Save and organize your collection\n❤️ Mark favorites for quick access\n🎨 Beautiful, modern design\n\nDownload QR Spark and experience the future of QR code management!\n\n#QRSpark #QRCode #MobileApp</string>
    <string name="share_qr_subject">Shared from QR Spark</string>
    <string name="share_qr_message" formatted="false">📱 Shared from QR Spark\n\n%s Content:\n%s\n\nGenerated with QR Spark - Your Ultimate QR Companion!</string>
    <string name="share_qr_content">Share QR Content</string>
    <string name="share_qr_image_message" formatted="false">🌟 QR Code from QR Spark\n\n📱 Type: %s\n📄 Content: %s\n\n✨ Generated with QR Spark - Your Ultimate QR Companion!\n#QRSpark #QRCode</string>
    <string name="contact_support">Contact Support</string>
    <string name="support_request_subject">QR Spark Support Request</string>
    <string name="support_request_message" formatted="false">QR Spark Support Request\n\nI need help with QR Spark app.\n\nPlease contact: <EMAIL>\n\nApp Version: 1.0.0\nDevice: %s\nAndroid Version: %s</string>

    <!-- Security Options -->
    <string name="security_wpa">WPA</string>
    <string name="security_wpa2">WPA2</string>
    <string name="security_wep">WEP</string>
    <string name="security_none">None</string>

    <!-- Scanner Screen -->
    <string name="scanner_smart_scan">Smart Scan</string>
    <string name="scanner_upload_qr_image">Upload QR Image</string>
    <string name="scanner_point_camera">Point your camera at a QR code to scan</string>
    <string name="scanner_qr_scanned">QR Code Scanned!</string>
    <string name="scanner_website">Website</string>
    <string name="scanner_open_website">Open Website</string>
    <string name="scanner_add_to_favorites">Add to Favorites</string>
    <string name="scanner_close">Close</string>

    <!-- Generator Screen -->
    <string name="generator_qr_generated">QR Code Generated!</string>
    <string name="generator_save_to_gallery">Save to Gallery</string>
    <string name="generator_add_to_favorites">Add to Favorites</string>
    <string name="generator_close">Close</string>
    <string name="generator_generate_text">Generate Text</string>
    <string name="generator_plain_text_content">Plain text content</string>

    <!-- Camera Permission -->
    <string name="camera_permission_denied">Camera Permission Denied</string>
    <string name="camera_access_required">Camera access is required to scan QR codes. Please enable camera permission in app settings.</string>
    <string name="try_again">Try Again</string>



    <!-- QR Code Details -->
    <string name="qr_characters">%d characters</string>
    <string name="qr_type_url_caps">URL</string>

    <!-- Missing Generator Strings -->
    <string name="generate_text">Generate Text</string>
    <string name="plain_text_content">Plain text content</string>
    <string name="text_content">Text Content</string>
    <string name="enter_your_text">Enter your text</string>
    <string name="type_anything_you_want_to_share">Type anything you want to share...</string>
    <string name="generate_qr_code">Generate QR Code</string>

    <!-- Missing Type Selection Strings -->
    <string name="select_type">Select Type</string>
    <string name="select_the_type_of_qr_code">Select the type of QR code you want to create</string>
    <string name="text">Text</string>
    <string name="website">Website</string>
    <string name="web_url_or_link">Web URL or link</string>
    <string name="wifi">WiFi</string>
    <string name="wifi_network_credentials">WiFi network credentials</string>
    <string name="email">Email</string>
    <string name="email_address">Email address</string>
    <string name="phone">Phone</string>
    <string name="phone_number">Phone number</string>
    <string name="sms">SMS</string>
    <string name="text_message">Text message</string>

    <!-- Missing Settings Strings -->
    <string name="settings_auto_save">Auto Save</string>
    <string name="settings_auto_save_desc">Automatically save scanned QR codes</string>
    <string name="settings_qr_format">QR Format</string>
    <string name="settings_qr_format_desc">Default format for saved QR codes</string>
    <string name="settings_qr_size">QR Size</string>
    <string name="settings_qr_size_desc">Default size for generated QR codes</string>
    <string name="settings_sound">Sound</string>
    <string name="settings_sound_desc">Play sound when scanning QR codes</string>
    <string name="settings_theme">Theme</string>
    <string name="settings_theme_desc">Choose app theme</string>
    <string name="settings_vibration">Vibration</string>
    <string name="settings_vibration_desc">Vibrate when scanning QR codes</string>

    <!-- Missing Help Strings -->
    <string name="help_app_version">App Version</string>
    <string name="help_app_version_desc">QR Spark v1.0.0</string>
    <string name="help_contact_support_desc">Need more help? Contact us</string>
    <string name="help_generating_qr">Generating QR</string>
    <string name="help_generating_qr_desc">Create custom QR codes</string>
    <string name="help_getting_started">Getting Started</string>
    <string name="help_getting_started_desc">Learn the basics of QR Spark</string>
    <string name="help_managing_history">Managing History</string>
    <string name="help_managing_history_desc">Organize your QR code collection</string>
    <string name="help_privacy_security">Privacy &amp; Security</string>
    <string name="help_privacy_security_desc">Your data is safe with us</string>
    <string name="help_scanning_qr">Scanning QR</string>
    <string name="help_scanning_qr_desc">How to scan QR codes effectively</string>
    <string name="help_tips_tricks">Tips &amp; Tricks</string>
    <string name="help_tips_tricks_desc">Get the most out of QR Spark</string>
    <string name="help_troubleshooting">Troubleshooting</string>
    <string name="help_troubleshooting_desc">Solve common issues</string>

    <!-- Missing Action Strings -->
    <string name="action_call_phone">Call</string>
    <string name="action_open_url">Open URL</string>
    <string name="action_finish">Finish</string>
    <string name="action_get_started">Get Started</string>
    <string name="action_skip">Skip</string>
    <string name="action_retry">Retry</string>

    <!-- Tutorial Strings -->
    <string name="tutorial_welcome_title">Welcome to QR Spark!</string>
    <string name="tutorial_welcome_description">Your all-in-one QR solution. Scan instantly, generate easily, manage effortlessly.</string>
    <string name="tutorial_scanning_title">Instant Scanning</string>
    <string name="tutorial_scanning_description">Scan QR codes with your camera or from images. Save time with instant recognition.</string>
    <string name="tutorial_generation_title">Easy Generation</string>
    <string name="tutorial_generation_description">Create custom QR codes for text, URLs, WiFi, and contacts. Share information easily.</string>
    <string name="tutorial_management_title">Smart Management</string>
    <string name="tutorial_management_description">Access your complete QR history with favorites and search. Never lose important codes.</string>

    <!-- Scanner Tutorial Strings -->
    <string name="tutorial_scanner_camera_title">Instant Camera Scanning</string>
    <string name="tutorial_scanner_camera_description">Point your camera at any QR code for instant recognition. No buttons needed.</string>
    <string name="tutorial_scanner_upload_title">Scan from Images</string>
    <string name="tutorial_scanner_upload_description">Upload photos from your gallery to scan QR codes from saved images.</string>
    <string name="tutorial_scanner_controls_title">Smart Controls</string>
    <string name="tutorial_scanner_controls_description">Use flash, zoom, and camera switching for perfect scans in any condition.</string>

    <!-- Generator Tutorial Strings -->
    <string name="tutorial_generator_type_title">Choose Your Type</string>
    <string name="tutorial_generator_type_description">Select from text, URL, WiFi, contact, and more. Each type is optimized for its purpose.</string>
    <string name="tutorial_generator_content_title">Add Your Content</string>
    <string name="tutorial_generator_content_description">Fill in your information. Smart forms make it quick and error-free.</string>
    <string name="tutorial_generator_share_title">Generate &amp; Share</string>
    <string name="tutorial_generator_share_description">Create your QR code instantly. Save, share, or print with one tap.</string>

    <!-- History Tutorial Strings -->
    <string name="tutorial_history_collection_title">Your QR Collection</string>
    <string name="tutorial_history_collection_description">All scanned and generated codes in one place. Never lose important information again.</string>
    <string name="tutorial_history_organization_title">Smart Organization</string>
    <string name="tutorial_history_organization_description">Mark favorites, search by content, and filter by type for instant access.</string>
    <string name="tutorial_history_actions_title">Quick Actions</string>
    <string name="tutorial_history_actions_description">Tap any code for instant actions: share, copy, or open links directly.</string>



    <!-- QR Scanner Messages -->
    <string name="message_qr_content">QR Content</string>

    <!-- QR Generator Messages -->
    <string name="message_no_qr_image">No QR code image available to save</string>
    <string name="message_storage_permission_required">Storage permission required to save images</string>
    <string name="message_saved_to_gallery">Saved to Gallery</string>
    <string name="message_save_failed">Save failed</string>
    <string name="message_share_failed">Share failed</string>
    <string name="qr_code_generated">QR Code Generated</string>

    <!-- Generator Form Hardcoded Strings -->
    <string name="generate_qr_code_button">Generate QR Code</string>

    <!-- Content Descriptions -->
    <string name="content_description_more_info">More information</string>

    <!-- Format Descriptions -->
    <string name="format_png_description">High quality, lossless compression</string>
    <string name="format_jpeg_description">Smaller file size, good for sharing</string>
    <string name="format_svg_description">Vector format, scalable</string>
    <string name="format_pdf_description">Document format, printable</string>

    <!-- Settings Info Dialog Titles -->
    <string name="info_auto_save_scanned_title">Auto Save Scanned QR Codes</string>
    <string name="info_sound_feedback_title">Sound Feedback</string>
    <string name="info_tutorial_guidance_title">Tutorial Guidance</string>
    <string name="info_ui_animations_title">UI Animations</string>
    <string name="info_haptic_feedback_title">Haptic Feedback</string>
    <string name="info_auto_open_links_title">Automatic Link Opening</string>
    <string name="info_auto_save_generated_title">Auto Save Generated QR Codes</string>

    <!-- Settings Info Dialog Descriptions -->
    <string name="info_auto_save_scanned_description">When enabled, all QR codes you scan will be automatically saved to your collection for easy access later. You can always manage your saved codes in the History section.</string>
    <string name="info_sound_feedback_description">Plays a pleasant beep sound when QR codes are successfully detected. This provides audio confirmation that your scan was successful. The sound respects your device\'s volume settings.</string>
    <string name="info_tutorial_guidance_description">Shows helpful tutorial overlays and onboarding guides when you first use features. These tutorials explain how to use the app effectively and can be skipped at any time.</string>
    <string name="info_ui_animations_description">Controls smooth transitions, loading animations, and visual effects throughout the app. Disabling animations can improve performance on older devices and helps users who prefer reduced motion.</string>
    <string name="info_haptic_feedback_description">Provides a gentle vibration when QR codes are successfully scanned. This gives you tactile confirmation that your scan was detected, especially useful in noisy environments.</string>
    <string name="info_auto_open_links_description">When enabled, URLs found in QR codes will automatically open in your browser. When disabled, you\'ll see a preview first and can choose whether to open the link for better security.</string>
    <string name="info_auto_save_generated_description">Automatically saves QR codes you create to your device and collection. This ensures you don\'t lose your generated codes and can easily access them later for sharing or printing.</string>

    <!-- Action Buttons -->
    <string name="action_test_sound">Test Sound</string>
</resources>