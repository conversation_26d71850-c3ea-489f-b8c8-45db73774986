<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">QR 스캐너 &amp; 생성기</string>

    <!-- Navigation -->
    <string name="nav_home">홈</string>
    <string name="nav_scanner">스캐너</string>
    <string name="nav_generator">생성기</string>
    <string name="nav_history">기록</string>

    <!-- Home Screen -->
    <string name="home_title">QR 앱</string>
    <string name="home_subtitle">당신의 궁극적인 QR 동반자</string>
    <string name="home_scan_title">QR 스캔</string>
    <string name="home_scan_description">QR 코드를 즉시 디코딩</string>
    <string name="home_generate_title">QR 생성</string>
    <string name="home_generate_description">고유한 코드 생성</string>
    <string name="home_history_title">내 컬렉션</string>
    <string name="home_history_description">스캔과 생성물에 액세스</string>

    <!-- Scanner Screen -->
    <string name="scanner_title">QR 스캐너</string>
    <string name="scanner_permission_title">카메라 권한 필요</string>
    <string name="scanner_permission_message">이 앱은 QR 코드를 스캔하기 위해 카메라 액세스가 필요합니다.</string>
    <string name="scanner_grant_permission">권한 허용</string>
    <string name="scanner_flash_on">플래시 켜짐</string>
    <string name="scanner_flash_off">플래시 꺼짐</string>

    <!-- Generator Screen -->
    <string name="generator_title">QR 생성기</string>
    <string name="generator_text_title">텍스트</string>
    <string name="generator_text_description">간단한 텍스트 내용</string>
    <string name="generator_text_hint">여기에 텍스트를 입력하세요...</string>
    <string name="generator_text_label">텍스트를 입력하세요</string>
    <string name="generator_text_placeholder">공유하고 싶은 내용을 무엇이든 작성하세요...</string>
    <string name="generator_text_content">텍스트 내용</string>
    <string name="generator_url_title">웹사이트</string>
    <string name="generator_url_description">URL 또는 웹 링크</string>
    <string name="generator_url_hint">https://example.com</string>
    <string name="generator_wifi_title">WiFi</string>
    <string name="generator_wifi_description">WiFi 네트워크 자격 증명</string>
    <string name="generator_email_title">이메일</string>
    <string name="generator_email_description">이메일 주소</string>
    <string name="generator_email_hint"><EMAIL></string>
    <string name="generator_phone_title">전화</string>
    <string name="generator_phone_description">전화번호</string>
    <string name="generator_phone_hint">+82123456789</string>
    <string name="generator_sms_title">SMS</string>
    <string name="generator_sms_description">문자 메시지</string>
    <string name="generator_contact_title">연락처</string>
    <string name="generator_contact_description">연락처 정보</string>
    <string name="generator_location_title">위치</string>
    <string name="generator_location_description">지리적 좌표</string>
    <string name="generator_generate_button">QR 코드 생성</string>
    <string name="generator_select_type_desc">생성하려는 QR 코드 유형을 선택하세요</string>
    <string name="generator_generating">QR 코드 생성 중...</string>

    <!-- History Screen -->
    <string name="history_title">내 컬렉션</string>
    <string name="history_empty_title">아직 QR 코드가 없습니다</string>
    <string name="history_empty_message">QR 코드를 스캔하거나 생성하여 여기에서 확인하세요</string>
    <string name="history_search_hint">QR 코드 검색...</string>
    <string name="history_tab_all">전체</string>
    <string name="history_tab_scanned">스캔됨</string>
    <string name="history_tab_generated">생성됨</string>
    <string name="history_tab_favorites">즐겨찾기</string>
    <string name="history_no_results">결과를 찾을 수 없습니다</string>
    <string name="history_add_favorite">즐겨찾기에 추가</string>

    <!-- Settings Screen -->
    <string name="settings_title">설정</string>
    <string name="settings_auto_save">자동 저장</string>
    <string name="settings_auto_save_desc">스캔한 QR 코드를 자동으로 저장</string>
    <string name="settings_auto_save_generated">생성된 코드 저장</string>
    <string name="settings_auto_save_generated_desc">생성된 QR 코드를 자동으로 저장</string>
    <string name="settings_vibration">진동</string>
    <string name="settings_vibration_desc">QR 코드 스캔 시 진동</string>
    <string name="settings_sound">소리</string>
    <string name="settings_sound_desc">QR 코드 스캔 시 소리 재생</string>
    <string name="settings_theme">테마</string>
    <string name="settings_theme_desc">앱 테마 선택</string>
    <string name="settings_language">언어</string>
    <string name="settings_language_desc">앱 언어 변경</string>
    <string name="settings_qr_size">QR 크기</string>
    <string name="settings_qr_size_desc">생성된 QR 코드의 기본 크기</string>
    <string name="settings_qr_format">QR 형식</string>
    <string name="settings_qr_format_desc">저장된 QR 코드의 기본 형식</string>

    <!-- QR Code Types -->
    <string name="qr_type_text">텍스트</string>
    <string name="qr_type_url">URL</string>
    <string name="qr_type_wifi">WiFi</string>
    <string name="qr_type_email">이메일</string>
    <string name="qr_type_phone">전화</string>
    <string name="qr_type_sms">SMS</string>
    <string name="qr_type_contact">연락처</string>
    <string name="qr_type_location">위치</string>
    <string name="qr_type_other">기타</string>

    <!-- Size and Format Options -->
    <string name="size_small">작음</string>
    <string name="size_medium">보통</string>
    <string name="size_large">큼</string>
    <string name="size_extra_large">매우 큼</string>

    <!-- Common Actions -->
    <string name="action_copy">복사</string>
    <string name="action_share">공유</string>
    <string name="action_save">저장</string>
    <string name="action_delete">삭제</string>
    <string name="action_edit">편집</string>
    <string name="action_favorite">즐겨찾기</string>
    <string name="action_cancel">취소</string>
    <string name="action_ok">확인</string>
    <string name="action_continue">계속</string>
    <string name="action_apply">적용</string>
    <string name="action_close">닫기</string>
    <string name="action_done">완료</string>
    <string name="action_add_to_favorites">즐겨찾기에 추가</string>
    <string name="action_favorited">즐겨찾기됨</string>
    <string name="action_saving">저장 중...</string>
    <string name="action_saved">저장됨</string>
    <string name="action_failed">실패</string>
    <string name="action_sharing">공유 중...</string>
    <string name="action_shared">공유됨!</string>
    <string name="action_copying">복사 중...</string>
    <string name="action_copied">복사됨!</string>
    <string name="action_loading">로딩 중...</string>

    <!-- Messages -->
    <string name="message_copied">클립보드에 복사됨</string>
    <string name="message_saved">성공적으로 저장됨</string>
    <string name="message_deleted">성공적으로 삭제됨</string>
    <string name="message_error">오류가 발생했습니다</string>
    <string name="message_no_camera">카메라를 사용할 수 없습니다</string>
    <string name="message_invalid_qr">유효하지 않은 QR 코드</string>
    <string name="message_url_copied">URL이 클립보드에 복사됨</string>
    <string name="message_text_copied">텍스트가 클립보드에 복사됨</string>
    <string name="message_qr_type_not_supported">QR 코드 유형이 아직 지원되지 않습니다</string>
    <string name="message_failed_qr_action">QR 코드 작업 처리 실패: %s</string>
    <string name="message_content_copied">내용이 클립보드에 복사됨</string>
    <string name="message_qr_saved_gallery">QR 코드가 사진/QR Spark에 저장됨</string>
    <string name="message_failed_save_qr">QR 코드 저장 실패</string>

    <!-- Status -->
    <string name="status_generated">생성됨</string>
    <string name="status_scanned">스캔됨</string>
    <string name="status_saved">컬렉션에 저장됨</string>

    <!-- Permissions -->
    <string name="permission_camera_required">카메라 권한 필요</string>
    <string name="permission_camera_denied">카메라 권한 거부됨</string>
    <string name="permission_camera_message">QR 코드를 스캔하려면 카메라 액세스가 필요합니다. 계속하려면 카메라 권한을 허용하세요.</string>
    <string name="permission_grant">권한 허용</string>
    <string name="permission_try_again">다시 시도</string>

    <!-- Language Selection -->
    <string name="language_welcome_title">QR Spark에 오신 것을 환영합니다!</string>
    <string name="language_welcome_subtitle">시작하려면 선호하는 언어를 선택하세요</string>
    <string name="language_select_title">언어 선택</string>
    <string name="language_current">현재 언어</string>

    <!-- Status Messages -->
    <string name="status_auto_saved">컬렉션에 자동 저장됨</string>
    <string name="status_favorited">즐겨찾기에 추가됨</string>
    <string name="status_not_saved">저장되지 않음 (자동 저장 비활성화)</string>
    <string name="status_loading">로딩 중...</string>
    <string name="status_done">완료</string>
    <string name="status_failed">실패</string>

    <!-- Generator Result Dialog -->
    <string name="qr_generated_title">QR 코드가 생성되었습니다!</string>
    <string name="qr_generated_success">QR 코드가 성공적으로 생성되었습니다</string>
    <string name="qr_generated_save_gallery">갤러리에 저장</string>
    <string name="qr_generated_share_image">이미지 공유</string>
    <string name="qr_generated_regenerate">재생성</string>
    <string name="qr_generated_customize">사용자 정의</string>

    <!-- Scanner Result Dialog -->
    <string name="qr_scanned_title">QR 코드가 스캔되었습니다!</string>
    <string name="qr_scanned_success">QR 코드가 성공적으로 스캔되었습니다</string>

    <!-- Navigation Titles -->
    <string name="nav_title_home">QR Spark</string>
    <string name="nav_title_scanner">QR 스캔</string>
    <string name="nav_title_generator">QR 생성</string>
    <string name="nav_title_history">내 컬렉션</string>
    <string name="nav_title_settings">설정</string>
    <string name="nav_title_help">도움말 &amp; FAQ</string>

    <!-- Help & FAQ Screen -->
    <string name="help_title">도움말 &amp; FAQ</string>
    <string name="help_subtitle">당신의 궁극적인 QR 동반자</string>
    <string name="help_faq_title">자주 묻는 질문</string>
    <string name="help_faq_subtitle">QR Spark에 대한 일반적인 질문의 답변을 찾아보세요</string>
    <string name="help_need_more_title">더 많은 도움이 필요하신가요?</string>
    <string name="help_need_more_subtitle">찾고 있는 답변을 찾을 수 없다면 언제든지 지원팀에 문의하세요.</string>
    <string name="help_contact_support">지원팀 문의</string>

    <!-- FAQ Questions -->
    <string name="faq_scan_question">QR 코드를 스캔하는 방법은?</string>
    <string name="faq_scan_answer">홈 화면에서 \'QR 스캔\'을 탭하고 카메라를 QR 코드에 향하면 자동으로 감지되고 처리됩니다.</string>

    <string name="faq_create_question">QR 코드를 생성하는 방법은?</string>
    <string name="faq_create_answer">홈 화면에서 \'QR 생성\'을 탭하고 생성하려는 QR 코드 유형을 선택한 후 필요한 정보를 입력하고 \'QR 코드 생성\'을 탭하세요.</string>

    <string name="faq_saved_question">내 QR 코드는 어디에 저장되나요?</string>
    <string name="faq_saved_answer">스캔하고 생성한 모든 QR 코드는 \'내 컬렉션\'에 자동으로 저장됩니다. 홈 화면에서 언제든지 액세스할 수 있습니다.</string>

    <string name="faq_favorites_question">QR 코드를 즐겨찾기로 표시하는 방법은?</string>
    <string name="faq_favorites_answer">QR 코드를 볼 때 하트 아이콘을 탭하여 즐겨찾기에 추가하세요. 즐겨찾기 QR 코드는 컬렉션 상단에 나타납니다.</string>

    <string name="faq_share_question">QR 코드를 공유할 수 있나요?</string>
    <string name="faq_share_answer">네! QR 코드를 볼 때 공유 버튼을 탭하여 QR 코드 이미지나 내용을 다른 앱과 공유할 수 있습니다.</string>

    <string name="faq_types_question">어떤 유형의 QR 코드를 생성할 수 있나요?</string>
    <string name="faq_types_answer">텍스트, URL, WiFi 자격 증명, 이메일 주소, 전화번호, SMS 메시지, 연락처, 위치에 대한 QR 코드를 생성할 수 있습니다.</string>

    <!-- Security Options -->
    <string name="security_wpa">WPA</string>
    <string name="security_wpa2">WPA2</string>
    <string name="security_wep">WEP</string>
    <string name="security_none">없음</string>

    <!-- Theme Options -->
    <string name="theme_light">밝음</string>
    <string name="theme_dark">어두움</string>
    <string name="theme_system">시스템</string>

    <!-- Camera Permission -->
    <string name="camera_permission_denied">카메라 권한 거부됨</string>
    <string name="camera_access_required">QR 코드를 스캔하려면 카메라 액세스가 필요합니다. 앱 설정에서 카메라 권한을 활성화하세요.</string>
    <string name="try_again">다시 시도</string>

    <!-- Missing Common Actions -->
    <string name="back">뒤로</string>
    <string name="search">검색</string>
    <string name="action_open_website">웹사이트 열기</string>
    <string name="action_connect_wifi">WiFi 연결</string>
    <string name="action_send_email">이메일 보내기</string>
    <string name="action_call">전화 걸기</string>
    <string name="action_send_sms">SMS 보내기</string>
    <string name="action_add_contact">연락처 추가</string>
    <string name="action_open_maps">지도에서 열기</string>
    <string name="action_add_calendar">캘린더에 추가</string>
    <string name="action_copy_text">텍스트 복사</string>

    <!-- Generator Related Missing -->
    <string name="generate_text">텍스트 생성</string>
    <string name="plain_text_content">일반 텍스트 내용</string>
    <string name="text_content">텍스트 내용</string>
    <string name="enter_your_text">텍스트를 입력하세요</string>
    <string name="type_anything_you_want_to_share">공유하고 싶은 내용을 무엇이든 입력하세요...</string>
    <string name="select_the_type_of_qr_code">생성하려는 QR 코드 유형을 선택하세요</string>
    <string name="web_url_or_link">웹 URL 또는 링크</string>
    <string name="wifi_network_credentials">WiFi 네트워크 자격 증명</string>
    <string name="email_address">이메일 주소</string>
    <string name="phone_number">전화번호</string>
    <string name="text_message">문자 메시지</string>

    <!-- Scanner Related Missing -->
    <string name="scanner_try_again">다시 시도</string>
    <string name="scanner_smart_scan">스마트 스캔</string>
    <string name="scanner_upload_qr_image">QR 이미지 업로드</string>
    <string name="scanner_point_camera">QR 코드에 카메라를 향해 스캔하세요</string>
    <string name="scanner_qr_scanned">QR 코드가 스캔되었습니다!</string>
    <string name="scanner_website">웹사이트</string>
    <string name="scanner_open_website">웹사이트 열기</string>
    <string name="scanner_add_to_favorites">즐겨찾기에 추가</string>

    <!-- Generator Screen Extended Missing -->
    <string name="generator_qr_generated">QR 코드가 생성되었습니다!</string>
    <string name="generator_save_to_gallery">갤러리에 저장</string>
    <string name="generator_add_to_favorites">즐겨찾기에 추가</string>
    <string name="generator_close">닫기</string>
    <string name="generator_generate_text">텍스트 생성</string>
    <string name="generator_plain_text_content">일반 텍스트 내용</string>

    <!-- QR Code Details Missing -->
    <string name="qr_characters">%d자</string>
    <string name="qr_type_url_caps">URL</string>

    <!-- Missing Essential Strings -->
    <string name="format_pdf">PDF</string>
    <string name="qr_type_calendar">캘린더</string>
    <string name="settings_privacy_policy">개인정보 보호정책</string>
    <string name="settings_privacy_policy_desc">개인정보 보호정책 보기</string>
    <string name="close">닫기</string>
    <string name="save">저장</string>
    <string name="cancel">취소</string>
    <string name="ok">확인</string>
    <string name="copy">복사</string>
    <string name="delete">삭제</string>
    <string name="share">공유</string>
    <string name="more_options">더 많은 옵션</string>
    <string name="history_empty">아직 QR 코드가 없습니다</string>
    <string name="history_remove_favorite">즐겨찾기에서 제거</string>
    <string name="scanner_permission_required">카메라 권한이 필요합니다</string>
    <string name="scanner_permission_rationale">이 앱은 QR 코드를 스캔하기 위해 카메라 액세스가 필요합니다. 계속하려면 카메라 권한을 허용해 주세요.</string>
    <string name="scanner_permission_denied">카메라 권한이 거부되었습니다</string>
    <string name="scanner_permission_settings">QR 코드를 스캔하려면 카메라 액세스가 필요합니다. 앱 설정에서 카메라 권한을 활성화해 주세요.</string>
    <string name="generator_select_type">QR 코드 유형 선택</string>
    <string name="select_type">유형 선택</string>
    <string name="text">텍스트</string>
    <string name="website">웹사이트</string>
    <string name="wifi">WiFi</string>
    <string name="email">이메일</string>
    <string name="phone">전화</string>
    <string name="sms">SMS</string>
    <string name="search_qr_codes">QR 코드 검색...</string>
    <string name="bulk_actions">일괄 작업</string>
    <string name="delete_all">모두 삭제</string>
    <string name="delete_non_favorites">즐겨찾기가 아닌 항목 삭제</string>
    <string name="share_app_subject">QR Spark를 발견하세요!</string>
    <string name="share_app_message">🌟 QR Spark - 당신의 궁극적인 QR 동반자! 🌟\n\nQR Spark를 사용하고 있는데 정말 훌륭합니다! 할 수 있는 것들:\n\n📱 QR 코드를 즉시 스캔\n✨ 맞춤형 QR 코드 생성\n💾 컬렉션 저장 및 정리\n❤️ 빠른 액세스를 위한 즐겨찾기 표시\n🎨 현대적이고 우아한 디자인\n\nQR Spark를 다운로드하고 QR 코드 관리의 미래를 발견하세요!\n\n#QRSpark #QR코드 #모바일앱</string>
    <string name="share_qr_subject">QR Spark에서 공유됨</string>
    <string name="share_qr_message" formatted="false">📱 QR Spark에서 공유됨\n\n%s 내용:\n%s\n\nQR Spark로 생성됨 - 당신의 궁극적인 QR 동반자!</string>
    <string name="share_qr_content">QR 내용 공유</string>
    <string name="share_qr_image_message" formatted="false">🌟 QR Spark의 QR 코드\n\n📱 유형: %s\n📄 내용: %s\n\n✨ QR Spark로 생성됨 - 당신의 궁극적인 QR 동반자!\n#QRSpark #QR코드</string>
    <string name="contact_support">지원팀 연락</string>
    <string name="support_request_subject">QR Spark 지원 요청</string>
    <string name="support_request_message" formatted="false">QR Spark 지원 요청\n\nQR Spark 앱에 대한 도움이 필요합니다.\n\n연락처: <EMAIL>\n\n앱 버전: 1.0.0\n기기: %s\nAndroid 버전: %s</string>
    <string name="generate_qr_code">QR 코드 생성</string>
    <string name="scanner_close">닫기</string>
    <string name="error_content_empty">내용이 비어있을 수 없습니다</string>
    <string name="error_failed_favorite">즐겨찾기 업데이트 실패: %s</string>
    <string name="error_invalid_email">유효한 이메일 주소를 입력하세요</string>
    <string name="error_invalid_phone">유효한 전화번호를 입력하세요</string>
    <string name="error_invalid_url">유효한 URL을 입력하세요</string>
    <string name="error_invalid_coordinates">유효한 좌표를 입력하세요</string>
    <string name="settings_show_tutorials">튜토리얼 표시</string>
    <string name="settings_show_tutorials_desc">유용한 팁과 튜토리얼 표시</string>
    <string name="settings_reset_all">기본값으로 재설정</string>
    <string name="settings_reset_all_desc">모든 설정을 기본값으로 재설정</string>
    <string name="settings_reset_dialog_title">모든 설정 재설정</string>
    <string name="settings_reset_dialog_message">모든 설정이 기본값으로 재설정됩니다:</string>
    <string name="settings_reset_dialog_warning">이 작업은 되돌릴 수 없습니다.</string>
    <string name="settings_reset_items_1">• 자동 저장 기본 설정</string>
    <string name="settings_reset_items_2">• 진동 및 사운드 설정</string>
    <string name="settings_reset_items_3">• 기본 QR 코드 크기 및 형식</string>
    <string name="settings_reset_items_4">• UI 및 애니메이션 기본 설정</string>
    <string name="settings_reset_confirm">재설정</string>
    <string name="settings_restore">복원</string>
    <string name="settings_app_settings_title">앱 설정</string>
    <string name="settings_app_settings_subtitle">QR Spark 경험 맞춤화</string>
    <string name="settings_rate_app">QR Spark 평가</string>
    <string name="settings_rate_app_desc">피드백으로 개선에 도움을 주세요</string>
    <string name="settings_size_dialog_title">QR 코드 크기 선택</string>
    <string name="settings_format_dialog_title">QR 코드 형식 선택</string>
    <string name="help_getting_started">시작하기</string>
    <string name="help_getting_started_desc">QR Spark의 기본 사항 배우기</string>
    <string name="help_scanning_qr">QR 코드 스캔</string>
    <string name="help_scanning_qr_desc">효과적으로 QR 코드를 스캔하는 방법</string>
    <string name="help_generating_qr">QR 코드 생성</string>
    <string name="help_generating_qr_desc">맞춤형 QR 코드 만들기</string>
    <string name="help_managing_history">기록 관리</string>
    <string name="help_managing_history_desc">QR 코드 컬렉션 정리</string>
    <string name="help_troubleshooting">문제 해결</string>
    <string name="help_troubleshooting_desc">일반적인 문제 해결</string>
    <string name="help_privacy_security">개인정보 보호 &amp; 보안</string>
    <string name="help_privacy_security_desc">귀하의 데이터는 저희와 함께 안전합니다</string>
    <string name="help_tips_tricks">팁 &amp; 요령</string>
    <string name="help_tips_tricks_desc">QR Spark를 최대한 활용하기</string>
    <string name="help_contact_support_desc">더 많은 도움이 필요하신가요? 연락해 주세요</string>
    <string name="help_app_version">앱 버전</string>
    <string name="help_app_version_desc">QR Spark v1.0.0</string>
    <string name="action_open_url">URL 열기</string>
    <string name="action_call_phone">전화 걸기</string>

    <!-- Additional Missing Settings Strings -->
    <string name="settings_scanning">스캔</string>
    <string name="settings_auto_save_scanned">스캔한 QR 코드 자동 저장</string>
    <string name="settings_auto_save_scanned_desc">QR 코드를 자동으로 컬렉션에 추가</string>
    <string name="settings_vibrate_on_scan">스캔 시 진동</string>
    <string name="settings_vibrate_on_scan_desc">QR 코드가 감지될 때 햅틱 피드백</string>
    <string name="settings_play_sound_on_scan">스캔 시 소리 재생</string>
    <string name="settings_play_sound_on_scan_desc">QR 코드가 감지될 때 오디오 피드백</string>
    <string name="settings_auto_open_links">링크 자동 열기</string>
    <string name="settings_auto_open_links_desc">스캔할 때 URL을 자동으로 열기</string>
    <string name="settings_generation">생성</string>
    <string name="settings_default_qr_size">기본 QR 코드 크기</string>
    <string name="settings_default_qr_size_desc">생성된 QR 코드의 기본 크기 설정</string>
    <string name="settings_default_qr_format">기본 QR 형식</string>
    <string name="settings_default_qr_format_desc">생성된 QR 코드의 기본 형식 설정</string>
    <string name="settings_appearance">UI 및 경험</string>
    <string name="settings_theme_mode">테마 모드</string>
    <string name="settings_theme_mode_desc">밝은 테마, 어두운 테마 또는 시스템 테마 선택</string>
    <string name="settings_enable_animations">애니메이션 활성화</string>
    <string name="settings_enable_animations_desc">앱 전체에서 부드러운 애니메이션 사용</string>
    <string name="settings_app_version_desc">QR Spark v1.0.0 • 복사하려면 탭</string>

    <!-- Missing Action Strings -->
    <string name="action_finish">완료</string>
    <string name="action_get_started">시작하기</string>
    <string name="action_skip">건너뛰기</string>

    <!-- Tutorial Strings -->
    <string name="tutorial_welcome_title">QR Spark에 오신 것을 환영합니다!</string>
    <string name="tutorial_welcome_description">올인원 QR 솔루션. 즉시 스캔하고, 쉽게 생성하고, 손쉽게 관리하세요.</string>
    <string name="tutorial_scanning_title">즉시 스캔</string>
    <string name="tutorial_scanning_description">카메라나 이미지로 QR 코드를 스캔하세요. 즉시 인식으로 시간을 절약하세요.</string>
    <string name="tutorial_generation_title">쉬운 생성</string>
    <string name="tutorial_generation_description">텍스트, URL, WiFi, 연락처용 맞춤 QR 코드를 만드세요. 정보를 쉽게 공유하세요.</string>
    <string name="tutorial_management_title">스마트 관리</string>
    <string name="tutorial_management_description">즐겨찾기와 검색이 포함된 완전한 QR 기록에 액세스하세요. 중요한 코드를 잃지 마세요.</string>

    <!-- Scanner Tutorial Strings -->
    <string name="tutorial_scanner_camera_title">즉시 카메라 스캔</string>
    <string name="tutorial_scanner_camera_description">모든 QR 코드에 카메라를 향하면 즉시 인식됩니다. 버튼이 필요하지 않습니다.</string>
    <string name="tutorial_scanner_upload_title">이미지에서 스캔</string>
    <string name="tutorial_scanner_upload_description">갤러리에서 사진을 업로드하여 저장된 이미지에서 QR 코드를 스캔하세요.</string>
    <string name="tutorial_scanner_controls_title">스마트 컨트롤</string>
    <string name="tutorial_scanner_controls_description">플래시, 줌, 카메라 전환을 사용하여 모든 조건에서 완벽한 스캔을 하세요.</string>

    <!-- Generator Tutorial Strings -->
    <string name="tutorial_generator_type_title">유형 선택</string>
    <string name="tutorial_generator_type_description">텍스트, URL, WiFi, 연락처 등에서 선택하세요. 각 유형은 목적에 최적화되어 있습니다.</string>
    <string name="tutorial_generator_content_title">콘텐츠 추가</string>
    <string name="tutorial_generator_content_description">정보를 입력하세요. 스마트 양식으로 빠르고 오류 없이 작성하세요.</string>
    <string name="tutorial_generator_share_title">생성 및 공유</string>
    <string name="tutorial_generator_share_description">QR 코드를 즉시 생성하세요. 한 번의 탭으로 저장, 공유 또는 인쇄하세요.</string>

    <!-- History Tutorial Strings -->
    <string name="tutorial_history_collection_title">QR 컬렉션</string>
    <string name="tutorial_history_collection_description">스캔되고 생성된 모든 코드가 한 곳에 있습니다. 중요한 정보를 다시는 잃지 마세요.</string>
    <string name="tutorial_history_organization_title">스마트 정리</string>
    <string name="tutorial_history_organization_description">즐겨찾기 표시, 콘텐츠로 검색, 유형별 필터링으로 즉시 액세스하세요.</string>
    <string name="tutorial_history_actions_title">빠른 작업</string>
    <string name="tutorial_history_actions_description">모든 코드를 탭하여 즉시 작업: 공유, 복사 또는 링크를 직접 열기.</string>

    <!-- Content Descriptions -->
    <string name="content_description_more_info">추가 정보</string>

    <!-- Format Descriptions -->
    <string name="format_png_description">고품질, 무손실 압축</string>
    <string name="format_jpeg_description">파일 크기가 작아 공유에 적합</string>
    <string name="format_svg_description">벡터 형식, 확장 가능</string>
    <string name="format_pdf_description">문서 형식, 인쇄 가능</string>

    <!-- Settings Info Dialog Titles -->
    <string name="info_auto_save_scanned_title">스캔한 QR 코드 자동 저장</string>
    <string name="info_sound_feedback_title">소리 피드백</string>
    <string name="info_tutorial_guidance_title">튜토리얼 가이드</string>
    <string name="info_ui_animations_title">UI 애니메이션</string>
    <string name="info_haptic_feedback_title">햅틱 피드백</string>
    <string name="info_auto_open_links_title">링크 자동 열기</string>
    <string name="info_auto_save_generated_title">생성한 QR 코드 자동 저장</string>

    <!-- Settings Info Dialog Descriptions -->
    <string name="info_auto_save_scanned_description">활성화하면 스캔한 모든 QR 코드가 자동으로 컬렉션에 저장되어 나중에 쉽게 액세스할 수 있습니다. 저장된 코드는 기록 섹션에서 언제든지 관리할 수 있습니다.</string>
    <string name="info_sound_feedback_description">QR 코드가 성공적으로 감지되면 기분 좋은 소리를 재생합니다. 이는 스캔이 성공했다는 청각적 확인을 제공합니다. 소리는 기기의 볼륨 설정을 따릅니다.</string>
    <string name="info_tutorial_guidance_description">기능을 처음 사용할 때 유용한 튜토리얼 오버레이와 온보딩 가이드를 표시합니다. 이러한 튜토리얼은 앱을 효과적으로 사용하는 방법을 설명하며 언제든지 건너뛸 수 있습니다.</string>
    <string name="info_ui_animations_description">앱 전체의 부드러운 전환, 로딩 애니메이션 및 시각 효과를 제어합니다. 애니메이션을 비활성화하면 구형 기기의 성능이 향상되고 움직임을 줄이고 싶은 사용자에게 도움이 됩니다.</string>
    <string name="info_haptic_feedback_description">QR 코드가 성공적으로 스캔되면 부드러운 진동을 제공합니다. 이는 스캔이 감지되었다는 촉각적 확인을 제공하며, 특히 시끄러운 환경에서 유용합니다.</string>
    <string name="info_auto_open_links_description">활성화하면 QR 코드에서 발견된 URL이 자동으로 브라우저에서 열립니다. 비활성화하면 먼저 미리보기를 보고 보안을 위해 링크를 열지 선택할 수 있습니다.</string>
    <string name="info_auto_save_generated_description">생성한 QR 코드를 기기와 컬렉션에 자동으로 저장합니다. 이렇게 하면 생성된 코드를 잃지 않고 나중에 공유나 인쇄를 위해 쉽게 액세스할 수 있습니다.</string>

    <!-- Action Buttons -->
    <string name="action_test_sound">소리 테스트</string>

    <!-- Tab Names -->
    <string name="tab_all">전체</string>
    <string name="tab_scanned">스캔됨</string>
    <string name="tab_generated">생성됨</string>
    <string name="tab_favorites">즐겨찾기</string>

    <!-- Shorter Tab Names for Better Multilingual Support -->
    <string name="tab_all_short">전체</string>
    <string name="tab_scanned_short">스캔</string>
    <string name="tab_generated_short">생성</string>
    <string name="tab_favorites_short">즐겨찾기</string>

    <!-- History Screen Headers -->
    <string name="history_title_my">내</string>
    <string name="history_title_collection">컬렉션</string>

    <!-- Bulk Actions -->
    <string name="bulk_action_delete_all">모두 삭제</string>
    <string name="bulk_action_delete_non_favorites">즐겨찾기 외 삭제</string>
    <string name="bulk_action_more_options">더 많은 옵션</string>

    <!-- Search -->
    <string name="search_placeholder">QR 코드 검색...</string>
    <string name="search_close">검색 닫기</string>
    <string name="search_open">검색</string>

    <!-- Content Descriptions -->
    <string name="cd_back">뒤로</string>
    <string name="cd_search">검색</string>
    <string name="cd_close_search">검색 닫기</string>
    <string name="cd_more_options">더 많은 옵션</string>
    <string name="cd_add_to_favorites">즐겨찾기에 추가</string>
    <string name="cd_remove_from_favorites">즐겨찾기에서 제거</string>
    <string name="cd_delete_qr_code">QR 코드 삭제</string>

    <!-- Scanner Screen -->
    <string name="scanner_instruction">QR 코드를 스캔하려면 카메라를 향하세요</string>
    <string name="scanner_upload_image">QR 이미지 업로드</string>
    <string name="cd_upload_qr_image">QR 이미지 업로드</string>

</resources>
