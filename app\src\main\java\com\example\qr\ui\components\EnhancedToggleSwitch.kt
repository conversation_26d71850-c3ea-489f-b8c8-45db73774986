package com.example.qr.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape

import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.unit.dp
import com.example.qr.ui.theme.*
import kotlinx.coroutines.delay

/**
 * Enhanced toggle switch with QR Spark design language
 * Features:
 * - Color coordination with setting icons
 * - Smooth animations and haptic feedback
 * - Clear visual states (on/off/disabled)
 * - Accessibility support
 * - Brand consistency
 */
@Composable
fun EnhancedToggleSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    iconColor: Color = ScanColor,
    hapticFeedback: Boolean = true
) {
    val haptic = LocalHapticFeedback.current
    val interactionSource = remember { MutableInteractionSource() }
    
    // Animation states
    var isPressed by remember { mutableStateOf(false) }
    var justToggled by remember { mutableStateOf(false) }
    
    // Color animations based on icon color and state
    val trackColor by animateColorAsState(
        targetValue = when {
            !enabled -> MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
            checked -> iconColor.copy(alpha = 0.3f)
            else -> MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
        },
        animationSpec = tween(durationMillis = 200),
        label = "track_color"
    )
    
    val thumbColor by animateColorAsState(
        targetValue = when {
            !enabled -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
            checked -> iconColor
            else -> MaterialTheme.colorScheme.outline
        },
        animationSpec = tween(durationMillis = 200),
        label = "thumb_color"
    )
    
    val borderColor by animateColorAsState(
        targetValue = when {
            !enabled -> Color.Transparent
            checked -> iconColor.copy(alpha = 0.6f)
            else -> MaterialTheme.colorScheme.outline.copy(alpha = 0.3f)
        },
        animationSpec = tween(durationMillis = 200),
        label = "border_color"
    )
    
    // Position animation
    val thumbPosition by animateFloatAsState(
        targetValue = if (checked) 1f else 0f,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "thumb_position"
    )
    
    // Scale animation for press feedback
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = tween(durationMillis = 100),
        label = "scale"
    )
    
    // Success pulse animation
    val successScale by animateFloatAsState(
        targetValue = if (justToggled) 1.1f else 1f,
        animationSpec = tween(durationMillis = 150),
        label = "success_scale"
    )
    
    // Reset success animation
    LaunchedEffect(justToggled) {
        if (justToggled) {
            delay(150)
            justToggled = false
        }
    }
    
    Box(
        modifier = modifier
            .size(width = 52.dp, height = 32.dp)
            .scale(scale * successScale)
            .clickable(
                interactionSource = interactionSource,
                indication = null, // Using custom visual feedback instead
                enabled = enabled
            ) {
                if (hapticFeedback) {
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                }
                isPressed = true
                justToggled = true
                onCheckedChange(!checked)
            }
    ) {
        // Track
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    color = trackColor,
                    shape = RoundedCornerShape(16.dp)
                )
                .border(
                    width = if (checked) 1.5.dp else 1.dp,
                    color = borderColor,
                    shape = RoundedCornerShape(16.dp)
                )
        )
        
        // Thumb
        Box(
            modifier = Modifier
                .size(28.dp)
                .offset(x = (20.dp * thumbPosition))
                .align(Alignment.CenterStart)
                .padding(2.dp)
                .shadow(
                    elevation = if (checked) 6.dp else 4.dp,
                    shape = CircleShape,
                    ambientColor = thumbColor.copy(alpha = 0.2f),
                    spotColor = thumbColor.copy(alpha = 0.3f)
                )
                .background(
                    color = thumbColor,
                    shape = CircleShape
                )
                .border(
                    width = if (checked) 0.dp else 0.5.dp,
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.2f),
                    shape = CircleShape
                )
        )
    }
    
    // Reset press state
    LaunchedEffect(isPressed) {
        if (isPressed) {
            delay(100)
            isPressed = false
        }
    }
}

/**
 * Predefined toggle switch variants for common settings
 */
@Composable
fun ScanToggleSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    EnhancedToggleSwitch(
        checked = checked,
        onCheckedChange = onCheckedChange,
        modifier = modifier,
        enabled = enabled,
        iconColor = ScanColor
    )
}

@Composable
fun GenerateToggleSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    EnhancedToggleSwitch(
        checked = checked,
        onCheckedChange = onCheckedChange,
        modifier = modifier,
        enabled = enabled,
        iconColor = GenerateColor
    )
}

@Composable
fun SuccessToggleSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    EnhancedToggleSwitch(
        checked = checked,
        onCheckedChange = onCheckedChange,
        modifier = modifier,
        enabled = enabled,
        iconColor = SuccessGreen
    )
}

@Composable
fun InfoToggleSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    EnhancedToggleSwitch(
        checked = checked,
        onCheckedChange = onCheckedChange,
        modifier = modifier,
        enabled = enabled,
        iconColor = InfoBlue
    )
}

@Composable
fun WarningToggleSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    EnhancedToggleSwitch(
        checked = checked,
        onCheckedChange = onCheckedChange,
        modifier = modifier,
        enabled = enabled,
        iconColor = WarningOrange
    )
}
