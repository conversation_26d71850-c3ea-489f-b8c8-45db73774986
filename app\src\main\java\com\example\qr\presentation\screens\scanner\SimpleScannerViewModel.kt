package com.example.qr.presentation.screens.scanner

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.widget.Toast
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.data.repository.PersistentQRRepository
import com.example.qr.data.repository.SettingsRepository
import com.example.qr.utils.QRCodeAnalyzer
import com.example.qr.utils.SoundManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Date

class SimpleScannerViewModel(private val context: Context) : ViewModel() {

    private val _uiState = MutableStateFlow(ScannerUiState())
    val uiState: StateFlow<ScannerUiState> = _uiState.asStateFlow()

    private val settingsRepository = SettingsRepository(context)
    private val qrRepository = PersistentQRRepository(context)
    private val soundManager = SoundManager(context, settingsRepository)
    private val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
        vibratorManager.defaultVibrator
    } else {
        @Suppress("DEPRECATION")
        context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
    }

    fun onQRCodeDetected(content: String) {
        val analysisResult = QRCodeAnalyzer.analyzeQRCodeContent(content, context)

        viewModelScope.launch {
            try {
                // Provide feedback based on settings
                provideScanFeedback()

                val qrCodeData = QRCodeData(
                    content = content,
                    type = analysisResult.type,
                    format = analysisResult.type.name,
                    displayName = analysisResult.displayName,
                    createdAt = Date(),
                    isGenerated = false,
                    isFavorite = false
                )

                // Check if auto-save is enabled
                val autoSaveEnabled = settingsRepository.autoSaveScanned.first()
                val savedQRCode = if (autoSaveEnabled) {
                    // Save to repository
                    val id = qrRepository.addQRCode(qrCodeData)
                    qrCodeData.copy(id = id)
                } else {
                    qrCodeData
                }

                _uiState.value = ScannerUiState(
                    scannedQRCode = savedQRCode,
                    showResult = true,
                    isLoading = false,
                    error = null,
                    autoSaved = autoSaveEnabled
                )
            } catch (e: Exception) {
                _uiState.value = ScannerUiState(
                    error = "Failed to process QR code: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    private suspend fun provideScanFeedback() {
        try {
            // Vibration feedback
            val vibrateEnabled = settingsRepository.vibrateOnScan.first()
            if (vibrateEnabled) {
                vibrator?.let { vib ->
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        vib.vibrate(VibrationEffect.createOneShot(100, VibrationEffect.DEFAULT_AMPLITUDE))
                    } else {
                        @Suppress("DEPRECATION")
                        vib.vibrate(100)
                    }
                }
            }

            // Sound feedback using SoundManager
            soundManager.playPleasantBeep()
            android.util.Log.d("ScannerViewModel", "Scan feedback provided (vibration: $vibrateEnabled)")
        } catch (e: Exception) {
            android.util.Log.e("ScannerViewModel", "Error providing scan feedback", e)
        }
    }

    fun handleQRCodeAction(context: Context, qrCode: QRCodeData) {
        viewModelScope.launch {
            try {
                when (qrCode.type) {
                    QRCodeType.URL -> {
                        val autoOpenLinks = settingsRepository.autoOpenLinks.first()
                        if (autoOpenLinks) {
                            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(qrCode.content))
                            context.startActivity(intent)
                        } else {
                            // Copy to clipboard instead
                            val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                            val clip = android.content.ClipData.newPlainText("QR Code URL", qrCode.content)
                            clipboard.setPrimaryClip(clip)
                            Toast.makeText(context, context.getString(R.string.message_url_copied), Toast.LENGTH_SHORT).show()
                        }
                    }

                QRCodeType.EMAIL -> {
                    val emailUri = if (qrCode.content.startsWith("mailto:")) {
                        Uri.parse(qrCode.content)
                    } else {
                        Uri.parse("mailto:${qrCode.content}")
                    }
                    val intent = Intent(Intent.ACTION_SENDTO, emailUri)
                    context.startActivity(intent)
                }

                QRCodeType.PHONE -> {
                    val phoneNumber = qrCode.content.removePrefix("tel:")
                    val intent = Intent(Intent.ACTION_DIAL, Uri.parse("tel:$phoneNumber"))
                    context.startActivity(intent)
                }

                QRCodeType.SMS -> {
                    val smsUri = if (qrCode.content.startsWith("sms:")) {
                        Uri.parse(qrCode.content)
                    } else {
                        Uri.parse("sms:${qrCode.content}")
                    }
                    val intent = Intent(Intent.ACTION_SENDTO, smsUri)
                    context.startActivity(intent)
                }

                QRCodeType.TEXT -> {
                    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                    val clip = android.content.ClipData.newPlainText("QR Code", qrCode.content)
                    clipboard.setPrimaryClip(clip)
                    Toast.makeText(context, context.getString(R.string.message_text_copied), Toast.LENGTH_SHORT).show()
                }

                    else -> {
                        Toast.makeText(context, context.getString(R.string.message_qr_type_not_supported), Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                Toast.makeText(context, context.getString(R.string.message_failed_qr_action, e.message ?: ""), Toast.LENGTH_LONG).show()
            }
        }
    }

    fun toggleFavorite(qrCode: QRCodeData) {
        viewModelScope.launch {
            try {
                // If the QR code doesn't have an ID (not saved yet), save it first
                val qrCodeToUpdate = if (qrCode.id == 0L) {
                    val id = qrRepository.addQRCode(qrCode)
                    qrCode.copy(id = id)
                } else {
                    qrCode
                }

                // Toggle favorite status
                qrRepository.toggleFavorite(qrCodeToUpdate.id)
                val updatedQRCode = qrCodeToUpdate.copy(isFavorite = !qrCodeToUpdate.isFavorite)

                // Update UI state - if item is now saved (has ID), mark as saved
                _uiState.value = ScannerUiState(
                    scannedQRCode = updatedQRCode,
                    showResult = _uiState.value.showResult,
                    isLoading = _uiState.value.isLoading,
                    error = _uiState.value.error,
                    autoSaved = updatedQRCode.id != 0L // Mark as saved if it has an ID
                )
            } catch (e: Exception) {
                _uiState.value = ScannerUiState(
                    scannedQRCode = _uiState.value.scannedQRCode,
                    showResult = _uiState.value.showResult,
                    isLoading = _uiState.value.isLoading,
                    error = "Failed to update favorite: ${e.message}",
                    autoSaved = _uiState.value.autoSaved
                )
            }
        }
    }

    fun dismissResult() {
        _uiState.value = ScannerUiState(
            showResult = false,
            scannedQRCode = null,
            isLoading = false,
            error = null
        )
    }

    fun clearError() {
        _uiState.value = ScannerUiState(
            scannedQRCode = _uiState.value.scannedQRCode,
            showResult = _uiState.value.showResult,
            isLoading = _uiState.value.isLoading,
            error = null
        )
    }

    override fun onCleared() {
        super.onCleared()
        soundManager.cleanup()
    }
}

data class ScannerUiState(
    val isLoading: Boolean = false,
    val scannedQRCode: QRCodeData? = null,
    val showResult: Boolean = false,
    val error: String? = null,
    val autoSaved: Boolean = false
)
