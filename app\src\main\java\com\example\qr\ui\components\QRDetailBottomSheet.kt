package com.example.qr.ui.components

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.os.Environment
import android.provider.MediaStore
import android.widget.Toast
import androidx.compose.animation.core.*
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.AnimatedVisibility
import kotlinx.coroutines.launch
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.scale
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.stringResource
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.presentation.screens.generator.getQRTypeColor
import com.example.qr.ui.theme.QRSparkTextStyles
import com.example.qr.ui.components.ReactiveLanguageContent
import com.example.qr.ui.components.reactiveStringResource
import com.example.qr.utils.getLocalizedQRDisplayName
import com.example.qr.utils.getLocalizedQRStatus
import com.example.qr.utils.QRCodeGenerator
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRDetailBottomSheet(
    qrCode: QRCodeData,
    onDismiss: () -> Unit,
    onToggleFavorite: (QRCodeData) -> Unit,
    onDelete: (QRCodeData) -> Unit,
    onEdit: ((QRCodeData) -> Unit)? = null,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val typeColor = getQRTypeColor(qrCode.type)
    val dateFormatter = remember { SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault()) }

    // Generate QR code bitmap
    val qrBitmap = remember(qrCode.content) {
        QRCodeGenerator.generateQRCode(
            content = qrCode.content,
            width = 300,
            height = 300
        )
    }

    var showCopiedFeedback by remember { mutableStateOf(false) }
    var showSavedFeedback by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    // Animation for favorite button
    val favoriteScale by animateFloatAsState(
        targetValue = if (qrCode.isFavorite) 1.2f else 1.0f,
        animationSpec = tween(durationMillis = 200),
        label = "favorite_scale"
    )

    ReactiveLanguageContent {
        ModalBottomSheet(
            onDismissRequest = onDismiss,
            modifier = modifier,
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface,
            dragHandle = {
            Surface(
                modifier = Modifier
                    .padding(vertical = 8.dp)
                    .size(width = 32.dp, height = 4.dp),
                shape = RoundedCornerShape(2.dp),
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f)
            ) {}
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header with type and favorite
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Type badge
                Surface(
                    shape = RoundedCornerShape(20.dp),
                    color = typeColor.copy(alpha = 0.1f)
                ) {
                    Text(
                        text = qrCode.type.name,
                        modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                        style = MaterialTheme.typography.labelMedium,
                        color = typeColor,
                        fontWeight = FontWeight.Medium
                    )
                }

                // Favorite button
                IconButton(
                    onClick = { onToggleFavorite(qrCode) },
                    modifier = Modifier.scale(favoriteScale)
                ) {
                    Icon(
                        imageVector = if (qrCode.isFavorite) Icons.Filled.Favorite else Icons.Filled.FavoriteBorder,
                        contentDescription = if (qrCode.isFavorite) "Remove from favorites" else "Add to favorites",
                        tint = if (qrCode.isFavorite) typeColor else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // QR Code Image
            if (qrBitmap != null) {
                Card(
                    modifier = Modifier.size(250.dp),
                    shape = RoundedCornerShape(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    ),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Image(
                            bitmap = qrBitmap.asImageBitmap(),
                            contentDescription = "QR Code",
                            modifier = Modifier
                                .fillMaxSize()
                                .clip(RoundedCornerShape(12.dp))
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // QR Code Information
            Card(
                modifier = Modifier.fillMaxWidth(),
                shape = RoundedCornerShape(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.3f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp)
                ) {
                    // Use localized display name
                    Text(
                        text = getLocalizedQRDisplayName(qrCode),
                        style = QRSparkTextStyles.cardTitle,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = qrCode.content,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Start
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    // Creation info with localized status
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = getLocalizedQRStatus(qrCode.isGenerated),
                            style = MaterialTheme.typography.bodySmall,
                            color = typeColor,
                            fontWeight = FontWeight.Medium
                        )

                        Text(
                            text = dateFormatter.format(qrCode.createdAt),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Action Buttons
            QRDetailActionButtons(
                qrCode = qrCode,
                qrBitmap = qrBitmap,
                typeColor = typeColor,
                onCopy = {
                    copyToClipboard(context, qrCode.content)
                    showCopiedFeedback = true
                },
                onShare = {
                    shareQRCode(context, qrCode, qrBitmap)
                },
                onSave = {
                    saveQRCodeToDevice(context, qrCode, qrBitmap) { success ->
                        if (success) showSavedFeedback = true
                    }
                },
                onEdit = onEdit?.let { { it(qrCode) } },
                onDelete = { showDeleteDialog = true }
            )

            Spacer(modifier = Modifier.height(16.dp))
        }
    }

    // Feedback Snackbars
    LaunchedEffect(showCopiedFeedback) {
        if (showCopiedFeedback) {
            kotlinx.coroutines.delay(2000)
            showCopiedFeedback = false
        }
    }

    LaunchedEffect(showSavedFeedback) {
        if (showSavedFeedback) {
            kotlinx.coroutines.delay(2000)
            showSavedFeedback = false
        }
    }

    // Delete confirmation dialog
    if (showDeleteDialog) {
        DeleteConfirmationDialog(
            qrCode = qrCode,
            onConfirm = {
                onDelete(qrCode)
                showDeleteDialog = false
                onDismiss() // Close the bottom sheet after deletion
            },
            onDismiss = {
                showDeleteDialog = false
            }
        )
    } // Close ModalBottomSheet
    } // Close ReactiveLanguageContent
}

@Composable
private fun QRDetailActionButtons(
    qrCode: QRCodeData,
    qrBitmap: Bitmap?,
    typeColor: androidx.compose.ui.graphics.Color,
    onCopy: () -> Unit,
    onShare: () -> Unit,
    onSave: () -> Unit,
    onEdit: (() -> Unit)?,
    onDelete: () -> Unit
) {
    // Animation states for buttons
    var isCopyLoading by remember { mutableStateOf(false) }
    var copySuccess by remember { mutableStateOf(false) }
    var isShareLoading by remember { mutableStateOf(false) }
    var shareSuccess by remember { mutableStateOf(false) }
    var isSaveLoading by remember { mutableStateOf(false) }
    var saveSuccess by remember { mutableStateOf(false) }

    val scope = rememberCoroutineScope()

    // Animation for staggered entrance
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        kotlinx.coroutines.delay(100)
        isVisible = true
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        // Primary actions row with animation
        AnimatedVisibility(
            visible = isVisible,
            enter = slideInVertically(
                initialOffsetY = { it / 2 },
                animationSpec = tween(durationMillis = 300, delayMillis = 0)
            )
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
            // Enhanced Copy button with animation
            AnimatedActionButton(
                onClick = {
                    scope.launch {
                        isCopyLoading = true
                        onCopy()
                        kotlinx.coroutines.delay(300)
                        isCopyLoading = false
                        copySuccess = true
                        kotlinx.coroutines.delay(1500)
                        copySuccess = false
                    }
                },
                icon = Icons.Default.ContentCopy,
                text = stringResource(R.string.action_copy),
                isLoading = isCopyLoading,
                isSuccess = copySuccess,
                modifier = Modifier.weight(1f),
                containerColor = typeColor.copy(alpha = 0.1f),
                contentColor = typeColor
            )

            // Enhanced Share button with animation
            AnimatedActionButton(
                onClick = {
                    scope.launch {
                        isShareLoading = true
                        onShare()
                        kotlinx.coroutines.delay(500)
                        isShareLoading = false
                        shareSuccess = true
                        kotlinx.coroutines.delay(1500)
                        shareSuccess = false
                    }
                },
                icon = Icons.Default.Share,
                text = stringResource(R.string.action_share),
                isLoading = isShareLoading,
                isSuccess = shareSuccess,
                modifier = Modifier.weight(1f),
                containerColor = MaterialTheme.colorScheme.primaryContainer,
                contentColor = MaterialTheme.colorScheme.primary
            )

            // Enhanced Save button with animation
            AnimatedActionButton(
                onClick = {
                    scope.launch {
                        isSaveLoading = true
                        onSave()
                        kotlinx.coroutines.delay(800)
                        isSaveLoading = false
                        saveSuccess = true
                        kotlinx.coroutines.delay(1500)
                        saveSuccess = false
                    }
                },
                icon = Icons.Default.Download,
                text = stringResource(R.string.action_save),
                isLoading = isSaveLoading,
                isSuccess = saveSuccess,
                modifier = Modifier.weight(1f),
                containerColor = MaterialTheme.colorScheme.secondaryContainer,
                contentColor = MaterialTheme.colorScheme.secondary
            )
            }
        }

        // Secondary actions row with animation
        AnimatedVisibility(
            visible = isVisible,
            enter = slideInVertically(
                initialOffsetY = { it / 2 },
                animationSpec = tween(durationMillis = 300, delayMillis = 150)
            )
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
            // Edit button (if applicable)
            if (onEdit != null && qrCode.isGenerated) {
                QRActionButton(
                    onClick = onEdit,
                    icon = Icons.Default.Edit,
                    text = stringResource(R.string.action_edit),
                    modifier = Modifier.weight(1f),
                    containerColor = MaterialTheme.colorScheme.surfaceVariant,
                    contentColor = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // Delete button
            QRActionButton(
                onClick = onDelete,
                icon = Icons.Default.Delete,
                text = stringResource(R.string.action_delete),
                modifier = if (onEdit != null && qrCode.isGenerated) Modifier.weight(1f) else Modifier.fillMaxWidth(),
                containerColor = MaterialTheme.colorScheme.errorContainer,
                contentColor = MaterialTheme.colorScheme.error
            )
            }
        }
    }
}

@Composable
private fun AnimatedActionButton(
    onClick: () -> Unit,
    icon: ImageVector,
    text: String,
    isLoading: Boolean = false,
    isSuccess: Boolean = false,
    modifier: Modifier = Modifier,
    containerColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.surfaceVariant,
    contentColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.onSurfaceVariant
) {
    // Animation for button scale
    val buttonScale by animateFloatAsState(
        targetValue = if (isLoading) 0.95f else if (isSuccess) 1.05f else 1.0f,
        animationSpec = tween(durationMillis = 200),
        label = "button_scale"
    )

    // Animation for content alpha
    val contentAlpha by animateFloatAsState(
        targetValue = if (isLoading) 0.6f else 1.0f,
        animationSpec = tween(durationMillis = 200),
        label = "content_alpha"
    )

    Button(
        onClick = if (isLoading) { {} } else onClick,
        modifier = modifier
            .height(48.dp)
            .scale(buttonScale),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSuccess) {
                androidx.compose.ui.graphics.Color(0xFF4CAF50).copy(alpha = 0.2f)
            } else containerColor,
            contentColor = if (isSuccess) {
                androidx.compose.ui.graphics.Color(0xFF4CAF50)
            } else contentColor
        ),
        shape = RoundedCornerShape(12.dp),
        enabled = !isLoading
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.alpha(contentAlpha)
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(18.dp),
                    strokeWidth = 2.dp,
                    color = contentColor
                )
            } else {
                Icon(
                    imageVector = if (isSuccess) Icons.Default.Check else icon,
                    contentDescription = text,
                    modifier = Modifier.size(18.dp)
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (isSuccess) stringResource(R.string.action_done) + "!" else text,
                style = MaterialTheme.typography.labelMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun QRActionButton(
    onClick: () -> Unit,
    icon: ImageVector,
    text: String,
    modifier: Modifier = Modifier,
    containerColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.surfaceVariant,
    contentColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.onSurfaceVariant
) {
    Button(
        onClick = onClick,
        modifier = modifier.height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = text,
            modifier = Modifier.size(18.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.labelMedium,
            fontWeight = FontWeight.Medium
        )
    }
}

// Utility functions
private fun copyToClipboard(context: Context, content: String) {
    val clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clip = ClipData.newPlainText("QR Code Content", content)
    clipboard.setPrimaryClip(clip)
    Toast.makeText(context, context.getString(R.string.message_content_copied), Toast.LENGTH_SHORT).show()
}

private fun shareQRCode(context: Context, qrCode: QRCodeData, bitmap: Bitmap?) {
    try {
        if (bitmap != null) {
            // Use coroutine for format-aware sharing
            kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
                val result = com.example.qr.utils.ShareUtils.shareQRCodeWithFormat(
                    context = context,
                    bitmap = bitmap,
                    qrCodeData = qrCode
                )

                kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                    when (result) {
                        is com.example.qr.utils.ShareUtils.ShareResult.Success -> {
                            // Success feedback could be added here if needed
                        }
                        is com.example.qr.utils.ShareUtils.ShareResult.Error -> {
                            // Fallback to simple sharing
                            com.example.qr.utils.ShareUtils.shareQRContent(
                                context = context,
                                content = qrCode.content,
                                qrType = qrCode.type.name
                            )
                        }
                    }
                }
            }
        } else {
            // Fallback to text sharing
            com.example.qr.utils.ShareUtils.shareQRContent(
                context = context,
                content = qrCode.content,
                qrType = qrCode.type.name
            )
        }
    } catch (e: Exception) {
        e.printStackTrace()
        // Fallback to simple text sharing
        val shareIntent = Intent().apply {
            action = Intent.ACTION_SEND
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, "QR Code: ${qrCode.content}")
        }
        context.startActivity(Intent.createChooser(shareIntent, "Share QR Code"))
    }
}

private fun saveQRCodeToDevice(
    context: Context,
    qrCode: QRCodeData,
    bitmap: Bitmap?,
    onResult: (Boolean) -> Unit
) {
    if (bitmap == null) {
        onResult(false)
        return
    }

    // Use coroutine to get the default format from settings
    kotlinx.coroutines.CoroutineScope(kotlinx.coroutines.Dispatchers.IO).launch {
        try {
            val settingsRepository = SettingsRepository(context)
            val defaultFormat = settingsRepository.defaultQRFormat.first()

            val result = com.example.qr.utils.ShareUtils.saveQRCodeInFormat(
                context = context,
                bitmap = bitmap,
                qrCodeData = qrCode,
                format = defaultFormat
            )

            kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                when (result) {
                    is com.example.qr.utils.ShareUtils.SaveResult.Success -> {
                        val message = when (defaultFormat.uppercase()) {
                            "PDF" -> "QR code saved as PDF in ${result.path}"
                            "JPEG" -> "QR code saved as JPEG in ${result.path}"
                            else -> context.getString(R.string.message_qr_saved_gallery)
                        }
                        Toast.makeText(context, message, Toast.LENGTH_SHORT).show()
                        onResult(true)
                    }
                    is com.example.qr.utils.ShareUtils.SaveResult.Error -> {
                        Toast.makeText(context, "Failed to save QR code: ${result.message}", Toast.LENGTH_SHORT).show()
                        onResult(false)
                    }
                }
            }
        } catch (e: Exception) {
            kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                e.printStackTrace()
                Toast.makeText(context, "Failed to save QR code: ${e.message}", Toast.LENGTH_SHORT).show()
                onResult(false)
            }
        }
    }
}

@Composable
fun DeleteConfirmationDialog(
    qrCode: QRCodeData,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = stringResource(R.string.dialog_delete_title),
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Text(
                text = stringResource(
                    R.string.dialog_delete_message,
                    qrCode.displayName
                )
            )
        },
        confirmButton = {
            Button(
                onClick = onConfirm,
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.error
                )
            ) {
                Text(
                    text = stringResource(R.string.action_delete),
                    color = Color.White
                )
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(
                    text = stringResource(R.string.action_cancel),
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        shape = RoundedCornerShape(20.dp)
    )
}
