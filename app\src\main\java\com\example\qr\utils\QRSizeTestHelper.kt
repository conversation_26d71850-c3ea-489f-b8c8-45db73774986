package com.example.qr.utils

import android.content.Context
import android.util.Log
import com.example.qr.data.model.QRCodeType
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first

/**
 * Test helper to verify QR size setting functionality
 */
object QRSizeTestHelper {
    
    private const val TAG = "QRSizeTestHelper"
    
    /**
     * Test the default QR size setting functionality
     */
    suspend fun testDefaultQRSizeSetting(context: Context) {
        Log.d(TAG, "=== Testing Default QR Size Setting ===")
        
        val settingsRepository = SettingsRepository(context)
        
        // Test 1: Check current default size
        val currentSize = settingsRepository.defaultQRSize.first()
        val currentPixels = settingsRepository.getQRSizePixels(currentSize)
        Log.d(TAG, "Current default size: $currentSize = $currentPixels pixels")
        
        // Test 2: Test all size options
        val sizeOptions = listOf("Small", "Medium", "Large", "Extra Large")
        sizeOptions.forEach { size ->
            val pixels = settingsRepository.getQRSizePixels(size)
            Log.d(TAG, "Size option: $size = $pixels pixels")
        }
        
        // Test 3: Test QR generation with default size
        try {
            val testBitmap = QRGenerationService.generateQRBitmap(
                context = context,
                content = "Test QR Code",
                customization = null // Should use default size
            )
            
            if (testBitmap != null) {
                Log.d(TAG, "✅ QR generation successful with size: ${testBitmap.width}x${testBitmap.height}")
                Log.d(TAG, "Expected size: $currentPixels pixels")
                
                if (testBitmap.width == currentPixels && testBitmap.height == currentPixels) {
                    Log.d(TAG, "✅ Size matches user's default setting!")
                } else {
                    Log.e(TAG, "❌ Size mismatch! Expected: $currentPixels, Got: ${testBitmap.width}")
                }
            } else {
                Log.e(TAG, "❌ QR generation failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error testing QR generation", e)
        }
        
        // Test 4: Test with explicit customization
        try {
            val customBitmap = QRGenerationService.generateQRBitmap(
                context = context,
                content = "Test QR Code",
                customization = QRCodeCustomization(size = 256) // Explicit size
            )
            
            if (customBitmap != null) {
                Log.d(TAG, "✅ Custom QR generation successful with size: ${customBitmap.width}x${customBitmap.height}")
                
                if (customBitmap.width == 256 && customBitmap.height == 256) {
                    Log.d(TAG, "✅ Custom size respected!")
                } else {
                    Log.e(TAG, "❌ Custom size not respected! Expected: 256, Got: ${customBitmap.width}")
                }
            } else {
                Log.e(TAG, "❌ Custom QR generation failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error testing custom QR generation", e)
        }
        
        Log.d(TAG, "=== QR Size Test Complete ===")
    }
    
    /**
     * Test changing the default size setting
     */
    suspend fun testSizeSettingChange(context: Context, newSize: String) {
        Log.d(TAG, "=== Testing Size Setting Change to $newSize ===")
        
        val settingsRepository = SettingsRepository(context)
        
        // Get current size
        val oldSize = settingsRepository.defaultQRSize.first()
        Log.d(TAG, "Old size: $oldSize")
        
        // Change size
        settingsRepository.setDefaultQRSize(newSize)
        
        // Verify change
        val updatedSize = settingsRepository.defaultQRSize.first()
        Log.d(TAG, "Updated size: $updatedSize")
        
        if (updatedSize == newSize) {
            Log.d(TAG, "✅ Size setting changed successfully!")
            
            // Test QR generation with new size
            val testBitmap = QRGenerationService.generateQRBitmap(
                context = context,
                content = "Test QR Code",
                customization = null
            )
            
            val expectedPixels = settingsRepository.getQRSizePixels(newSize)
            if (testBitmap != null && testBitmap.width == expectedPixels) {
                Log.d(TAG, "✅ QR generation uses new default size: ${testBitmap.width}px")
            } else {
                Log.e(TAG, "❌ QR generation doesn't use new default size")
            }
        } else {
            Log.e(TAG, "❌ Size setting change failed!")
        }
        
        Log.d(TAG, "=== Size Setting Change Test Complete ===")
    }
}
