package com.example.qr.utils

import android.content.Context
import com.example.qr.data.model.QRCodeType
import java.util.regex.Pattern

object QRCodeAnalyzer {

    private val URL_PATTERN = Pattern.compile(
        "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$",
        Pattern.CASE_INSENSITIVE
    )

    private val EMAIL_PATTERN = Pattern.compile(
        "^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$"
    )

    private val PHONE_PATTERN = Pattern.compile(
        "^[+]?[0-9\\s\\-()]{7,15}$"
    )

    fun analyzeQRCodeContent(content: String, context: Context? = null): QRCodeAnalysisResult {
        val trimmedContent = content.trim()

        return when {
            trimmedContent.startsWith("WIFI:", ignoreCase = true) -> {
                QRCodeAnalysisResult(
                    type = QRCodeType.WIFI,
                    displayName = "WiFi Network",
                    actionText = "Connect to WiFi"
                )
            }

            trimmedContent.startsWith("mailto:", ignoreCase = true) -> {
                val email = trimmedContent.substring(7)
                QRCodeAnalysisResult(
                    type = QRCodeType.EMAIL,
                    displayName = "Email: $email",
                    actionText = "Send Email"
                )
            }

            trimmedContent.startsWith("tel:", ignoreCase = true) -> {
                val phone = trimmedContent.substring(4)
                QRCodeAnalysisResult(
                    type = QRCodeType.PHONE,
                    displayName = "Phone: $phone",
                    actionText = "Call"
                )
            }

            trimmedContent.startsWith("sms:", ignoreCase = true) -> {
                val phone = trimmedContent.substring(4)
                QRCodeAnalysisResult(
                    type = QRCodeType.SMS,
                    displayName = "SMS: $phone",
                    actionText = "Send SMS"
                )
            }

            trimmedContent.startsWith("BEGIN:VCARD", ignoreCase = true) -> {
                QRCodeAnalysisResult(
                    type = QRCodeType.CONTACT,
                    displayName = "Contact Card",
                    actionText = "Add Contact"
                )
            }

            trimmedContent.startsWith("geo:", ignoreCase = true) -> {
                QRCodeAnalysisResult(
                    type = QRCodeType.LOCATION,
                    displayName = "Location",
                    actionText = "Open in Maps"
                )
            }

            trimmedContent.startsWith("BEGIN:VEVENT", ignoreCase = true) -> {
                QRCodeAnalysisResult(
                    type = QRCodeType.CALENDAR,
                    displayName = "Calendar Event",
                    actionText = "Add to Calendar"
                )
            }

            URL_PATTERN.matcher(trimmedContent).matches() -> {
                val displayName = if (context != null) {
                    QRCodeLocalization.generateLocalizedDisplayName(context, trimmedContent, QRCodeType.URL)
                } else {
                    "Website: ${extractDomain(trimmedContent)}"
                }
                QRCodeAnalysisResult(
                    type = QRCodeType.URL,
                    displayName = displayName,
                    actionText = "Open Website"
                )
            }

            EMAIL_PATTERN.matcher(trimmedContent).matches() -> {
                val displayName = if (context != null) {
                    QRCodeLocalization.generateLocalizedDisplayName(context, trimmedContent, QRCodeType.EMAIL)
                } else {
                    "Email: $trimmedContent"
                }
                QRCodeAnalysisResult(
                    type = QRCodeType.EMAIL,
                    displayName = displayName,
                    actionText = "Send Email"
                )
            }

            PHONE_PATTERN.matcher(trimmedContent).matches() -> {
                val displayName = if (context != null) {
                    QRCodeLocalization.generateLocalizedDisplayName(context, trimmedContent, QRCodeType.PHONE)
                } else {
                    "Phone: $trimmedContent"
                }
                QRCodeAnalysisResult(
                    type = QRCodeType.PHONE,
                    displayName = displayName,
                    actionText = "Call"
                )
            }

            else -> {
                QRCodeAnalysisResult(
                    type = QRCodeType.TEXT,
                    displayName = if (trimmedContent.length > 30) {
                        "${trimmedContent.take(30)}..."
                    } else {
                        trimmedContent
                    },
                    actionText = "Copy Text"
                )
            }
        }
    }

    private fun extractDomain(url: String): String {
        return try {
            val domain = url.substringAfter("://").substringBefore("/")
            if (domain.startsWith("www.")) domain.substring(4) else domain
        } catch (e: Exception) {
            url
        }
    }
}

data class QRCodeAnalysisResult(
    val type: QRCodeType,
    val displayName: String,
    val actionText: String
)
