package com.example.qr.presentation.screens.history

import android.util.Log
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.Message
import androidx.compose.material.icons.automirrored.filled.TextSnippet
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset

import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.ui.theme.*
import com.example.qr.ui.components.GradientBackground
import com.example.qr.ui.components.QRDetailBottomSheet
import com.example.qr.ui.theme.QRSparkTextStyles
import com.example.qr.utils.SafeAreaUtils
import com.example.qr.ui.components.SparkCard
import com.example.qr.ui.components.ReactiveLanguageContent
import com.example.qr.ui.components.reactiveStringResource
import com.example.qr.utils.getLocalizedQRDisplayName
import com.example.qr.utils.getLocalizedQRStatus
import com.example.qr.utils.getLocalizedQRTypeName
import com.example.qr.utils.LanguageManager
import com.example.qr.presentation.navigation.Screen
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun HistoryScreen(
    navController: NavController,
    viewModel: SimpleHistoryViewModel = run {
        val context = LocalContext.current
        viewModel { SimpleHistoryViewModel(context) }
    }
) {
    val selectedTab by viewModel.selectedTab.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val filteredQRCodes by viewModel.filteredQRCodes.collectAsState()
    val uiState by viewModel.uiState.collectAsState()

    var showSearch by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var qrCodeToDelete by remember { mutableStateOf<QRCodeData?>(null) }
    var showBulkActions by remember { mutableStateOf(false) }
    var showDetailBottomSheet by remember { mutableStateOf(false) }
    var selectedQRCode by remember { mutableStateOf<QRCodeData?>(null) }

    val context = LocalContext.current
    val snackbarHostState = remember { SnackbarHostState() }

    // Show error snackbar
    LaunchedEffect(uiState.error) {
        uiState.error?.let { error ->
            snackbarHostState.showSnackbar(
                message = error,
                actionLabel = "Dismiss"
            )
            viewModel.clearError()
        }
    }

    ReactiveLanguageContent {
        // Localized tab names with responsive sizing
        val tabs = listOf(
            reactiveStringResource(R.string.tab_all_short),
            reactiveStringResource(R.string.tab_scanned_short),
            reactiveStringResource(R.string.tab_generated_short),
            reactiveStringResource(R.string.tab_favorites_short)
        )

        GradientBackground(applyScreenSafeArea = false) {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.displayCutout),
            topBar = {
                SparkTopAppBar(
                    title = if (showSearch) null else stringResource(R.string.nav_title_history),
                    showSearch = showSearch,
                    searchQuery = searchQuery,
                    onSearchQueryChange = { viewModel.updateSearchQuery(it) },
                    onSearchToggle = {
                        showSearch = !showSearch
                        if (!showSearch) viewModel.updateSearchQuery("")
                    },
                    onBackClick = { navController.popBackStack() },
                    showBulkActions = showBulkActions,
                    onBulkActionsToggle = { showBulkActions = !showBulkActions },
                    onDeleteAll = {
                        viewModel.deleteAllQRCodes()
                        showBulkActions = false
                    },
                    onDeleteNonFavorites = {
                        viewModel.deleteNonFavoriteQRCodes()
                        showBulkActions = false
                    },
                    hasItems = filteredQRCodes.isNotEmpty()
                )
            },
            snackbarHost = { SnackbarHost(snackbarHostState) },
            containerColor = Color.Transparent,
            contentWindowInsets = SafeAreaUtils.scaffoldContentWindowInsets
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                // Spark-styled Tab Row
                SparkTabRow(
                    selectedTab = selectedTab,
                    tabs = tabs,
                    onTabSelected = { viewModel.selectTab(it) }
                )

                // Content with animations
                SparkQRCodeList(
                    qrCodes = filteredQRCodes,
                    searchQuery = searchQuery,
                    onFavoriteToggle = { viewModel.toggleFavorite(it) },
                    onItemClick = { qrCode ->
                        selectedQRCode = qrCode
                        showDetailBottomSheet = true
                    },
                    onDeleteItem = { qrCode ->
                        qrCodeToDelete = qrCode
                        showDeleteDialog = true
                    }
                )
            }
        }
    }

    // Delete confirmation dialog
    if (showDeleteDialog && qrCodeToDelete != null) {
        DeleteConfirmationDialog(
            qrCode = qrCodeToDelete!!,
            onConfirm = {
                viewModel.deleteQRCode(qrCodeToDelete!!)
                showDeleteDialog = false
                qrCodeToDelete = null
            },
            onDismiss = {
                showDeleteDialog = false
                qrCodeToDelete = null
            }
        )
    }

    // QR Detail Bottom Sheet
    if (showDetailBottomSheet && selectedQRCode != null) {
        QRDetailBottomSheet(
            qrCode = selectedQRCode!!,
            onDismiss = {
                showDetailBottomSheet = false
                selectedQRCode = null
            },
            onToggleFavorite = { qrCode ->
                viewModel.toggleFavorite(qrCode)
                selectedQRCode = selectedQRCode?.copy(isFavorite = !selectedQRCode!!.isFavorite)
            },
            onDelete = { qrCode ->
                viewModel.deleteQRCode(qrCode)
                showDetailBottomSheet = false
                selectedQRCode = null
            },
            onEdit = { qrCode ->
                // Navigate to generator screen for editing
                showDetailBottomSheet = false
                selectedQRCode = null
                navController.navigate(Screen.Generator.route)
            }
        )
        } // Close ReactiveLanguageContent
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SparkTopAppBar(
    title: String?,
    showSearch: Boolean,
    searchQuery: String,
    onSearchQueryChange: (String) -> Unit,
    onSearchToggle: () -> Unit,
    onBackClick: () -> Unit,
    showBulkActions: Boolean,
    onBulkActionsToggle: () -> Unit,
    onDeleteAll: () -> Unit,
    onDeleteNonFavorites: () -> Unit,
    hasItems: Boolean
) {
    // Observe language changes to force recomposition of dropdown menu
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()
    TopAppBar(
        title = {
            if (showSearch) {
                OutlinedTextField(
                    value = searchQuery,
                    onValueChange = onSearchQueryChange,
                    placeholder = {
                        Text(
                            reactiveStringResource(R.string.search_placeholder),
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    },
                    modifier = Modifier.fillMaxWidth(),
                    singleLine = true,
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedBorderColor = SparkGreen,
                        cursorColor = SparkGreen
                    ),
                    shape = RoundedCornerShape(12.dp)
                )
            } else {
                // Consistent header styling with brand accent
                Text(
                    text = "${reactiveStringResource(R.string.history_title_my)} ${reactiveStringResource(R.string.history_title_collection)}",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        },
        navigationIcon = {
            IconButton(onClick = onBackClick) {
                Icon(
                    Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = reactiveStringResource(R.string.cd_back),
                    tint = MaterialTheme.colorScheme.onSurface
                )
            }
        },
        actions = {
            IconButton(onClick = onSearchToggle) {
                Icon(
                    imageVector = if (showSearch) Icons.Default.Close else Icons.Default.Search,
                    contentDescription = if (showSearch) reactiveStringResource(R.string.cd_close_search) else reactiveStringResource(R.string.cd_search),
                    tint = if (showSearch) SparkPink else MaterialTheme.colorScheme.onSurface
                )
            }

            if (!showSearch && hasItems) {
                IconButton(onClick = onBulkActionsToggle) {
                    Icon(
                        Icons.Default.MoreVert,
                        contentDescription = reactiveStringResource(R.string.cd_more_options),
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }

                // Force recomposition when language changes
                key(currentLanguage.code) {
                    DropdownMenu(
                        expanded = showBulkActions,
                        onDismissRequest = { onBulkActionsToggle() }
                    ) {
                        DropdownMenuItem(
                            text = { Text(reactiveStringResource(R.string.bulk_action_delete_all)) },
                            onClick = onDeleteAll,
                            leadingIcon = {
                                Icon(Icons.Default.DeleteSweep, contentDescription = null)
                            }
                        )
                        DropdownMenuItem(
                            text = { Text(reactiveStringResource(R.string.bulk_action_delete_non_favorites)) },
                            onClick = onDeleteNonFavorites,
                            leadingIcon = {
                                Icon(Icons.Default.Delete, contentDescription = null)
                            }
                        )
                    }
                }
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent
        )
    )
}

@Composable
private fun SparkTabRow(
    selectedTab: Int,
    tabs: List<String>,
    onTabSelected: (Int) -> Unit
) {
    TabRow(
        selectedTabIndex = selectedTab,
        containerColor = Color.Transparent,
        contentColor = MaterialTheme.colorScheme.onSurface,
        indicator = { tabPositions ->
            if (selectedTab < tabPositions.size) {
                Box(
                    modifier = Modifier
                        .tabIndicatorOffset(tabPositions[selectedTab])
                        .height(3.dp)
                        .background(
                            color = when (selectedTab) {
                                0 -> SparkGreen
                                1 -> ScanColor
                                2 -> GenerateColor
                                3 -> CollectionColor
                                else -> SparkGreen
                            },
                            shape = RoundedCornerShape(topStart = 3.dp, topEnd = 3.dp)
                        )
                )
            }
        }
    ) {
        tabs.forEachIndexed { index, title ->
            Tab(
                selected = selectedTab == index,
                onClick = { onTabSelected(index) },
                text = {
                    // Responsive tab design with icons and adaptive text
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 4.dp)
                    ) {
                        // Tab icon
                        Icon(
                            imageVector = when (index) {
                                0 -> Icons.Default.GridView
                                1 -> Icons.Default.QrCodeScanner
                                2 -> Icons.Default.QrCode2
                                3 -> Icons.Default.Favorite
                                else -> Icons.Default.GridView
                            },
                            contentDescription = null,
                            modifier = Modifier.size(16.dp),
                            tint = if (selectedTab == index) {
                                when (index) {
                                    0 -> SparkGreen
                                    1 -> ScanColor
                                    2 -> GenerateColor
                                    3 -> CollectionColor
                                    else -> SparkGreen
                                }
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            }
                        )

                        Spacer(modifier = Modifier.width(6.dp))

                        // Adaptive text with overflow handling
                        Text(
                            text = title,
                            style = QRSparkTextStyles.navigationLabel.copy(
                                fontWeight = if (selectedTab == index) FontWeight.SemiBold else FontWeight.Normal,
                                fontSize = 12.sp
                            ),
                            color = if (selectedTab == index) {
                                when (index) {
                                    0 -> SparkGreen
                                    1 -> ScanColor
                                    2 -> GenerateColor
                                    3 -> CollectionColor
                                    else -> SparkGreen
                                }
                            } else {
                                MaterialTheme.colorScheme.onSurfaceVariant
                            },
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.weight(1f, fill = false)
                        )
                    }
                }
            )
        }
    }
}

@Composable
fun DeleteConfirmationDialog(
    qrCode: QRCodeData,
    onConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    ReactiveLanguageContent {
        AlertDialog(
            onDismissRequest = onDismiss,
            title = { Text(reactiveStringResource(R.string.dialog_delete_title)) },
            text = {
                Text(reactiveStringResource(R.string.dialog_delete_message, qrCode.displayName))
            },
            confirmButton = {
                Button(
                    onClick = onConfirm,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text(reactiveStringResource(R.string.action_delete))
                }
            },
            dismissButton = {
                TextButton(onClick = onDismiss) {
                    Text(reactiveStringResource(R.string.action_cancel))
                }
            }
        )
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun SparkQRCodeList(
    qrCodes: List<QRCodeData>,
    searchQuery: String,
    onFavoriteToggle: (QRCodeData) -> Unit,
    onItemClick: (QRCodeData) -> Unit,
    onDeleteItem: (QRCodeData) -> Unit
) {
    val filteredQRCodes = remember(qrCodes, searchQuery) {
        if (searchQuery.isBlank()) {
            qrCodes
        } else {
            qrCodes.filter {
                it.content.contains(searchQuery, ignoreCase = true) ||
                it.displayName.contains(searchQuery, ignoreCase = true)
            }
        }
    }

    val listState = rememberLazyListState()

    AnimatedVisibility(
        visible = true,
        enter = fadeIn() + slideInVertically(),
        exit = fadeOut() + slideOutVertically()
    ) {
        if (filteredQRCodes.isEmpty()) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.padding(32.dp)
                ) {
                    // Spark-styled empty state icon
                    Box(
                        modifier = Modifier
                            .size(80.dp)
                            .background(
                                color = SparkGreen.copy(alpha = 0.1f),
                                shape = CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.QrCode2,
                            contentDescription = null,
                            modifier = Modifier.size(40.dp),
                            tint = SparkGreen
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Text(
                        text = if (searchQuery.isBlank())
                            reactiveStringResource(R.string.history_empty_title)
                        else
                            reactiveStringResource(R.string.history_no_results),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.SemiBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    if (searchQuery.isBlank()) {
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = reactiveStringResource(R.string.history_empty_message),
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = androidx.compose.ui.text.style.TextAlign.Center
                        )
                    }
                }
            }
        } else {
            LazyColumn(
                state = listState,
                modifier = Modifier.fillMaxSize(),
                contentPadding = PaddingValues(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(
                    items = filteredQRCodes,
                    key = { it.id }
                ) { qrCode ->
                    SparkQRCodeItem(
                        qrCode = qrCode,
                        onFavoriteToggle = { onFavoriteToggle(qrCode) },
                        onClick = { onItemClick(qrCode) },
                        onDelete = { onDeleteItem(qrCode) },
                        modifier = Modifier.animateItem(
                            fadeInSpec = tween(durationMillis = 300),
                            fadeOutSpec = tween(durationMillis = 300),
                            placementSpec = tween(durationMillis = 300)
                        )
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SparkQRCodeItem(
    qrCode: QRCodeData,
    onFavoriteToggle: () -> Unit,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Observe language changes to trigger recomposition
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    // Language-aware date formatter
    val dateFormatter = remember(currentLanguage) {
        SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    }

    SparkCard(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (qrCode.isFavorite) {
                when (qrCode.type) {
                    QRCodeType.URL -> ScanColor.copy(alpha = 0.1f)
                    QRCodeType.TEXT -> GenerateColor.copy(alpha = 0.1f)
                    else -> SparkGreen.copy(alpha = 0.1f)
                }
            } else {
                MaterialTheme.colorScheme.surface
            }
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Spark-styled QR Code type icon
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .background(
                        color = getTypeColor(qrCode.type).copy(alpha = 0.1f),
                        shape = RoundedCornerShape(12.dp)
                    ),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = getTypeIcon(qrCode.type),
                    contentDescription = getLocalizedQRTypeName(qrCode.type),
                    tint = getTypeColor(qrCode.type),
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.width(16.dp))

            // Enhanced content with clear hierarchy
            Column(modifier = Modifier.weight(1f)) {
                // Primary title: Localized display name with type context
                Text(
                    text = getLocalizedQRDisplayName(qrCode),
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Spacer(modifier = Modifier.height(2.dp))

                // Secondary subtitle: Content preview (avoid duplication)
                if (qrCode.content != qrCode.displayName && qrCode.content.isNotBlank()) {
                    Text(
                        text = qrCode.content.take(40).let {
                            if (qrCode.content.length > 40) "$it..." else it
                        },
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )

                    Spacer(modifier = Modifier.height(2.dp))
                }

                // Tertiary status: Localized timestamp with status
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Status indicator with color coding
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(
                                color = if (qrCode.isGenerated) GenerateColor else ScanColor,
                                shape = CircleShape
                            )
                    )

                    Spacer(modifier = Modifier.width(6.dp))

                    Text(
                        text = "${getLocalizedQRStatus(qrCode.isGenerated)} • ${dateFormatter.format(qrCode.createdAt)}",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }

            // Spark-styled Actions
            Row {
                IconButton(onClick = onFavoriteToggle) {
                    val favoriteScale by animateFloatAsState(
                        targetValue = if (qrCode.isFavorite) 1.2f else 1f,
                        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
                        label = "favorite_scale"
                    )

                    Icon(
                        imageVector = if (qrCode.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                        contentDescription = if (qrCode.isFavorite)
                            reactiveStringResource(R.string.cd_remove_from_favorites)
                        else
                            reactiveStringResource(R.string.cd_add_to_favorites),
                        tint = if (qrCode.isFavorite) CollectionColor else MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.graphicsLayer {
                            scaleX = favoriteScale
                            scaleY = favoriteScale
                        }
                    )
                }

                IconButton(onClick = onDelete) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = reactiveStringResource(R.string.cd_delete_qr_code),
                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

private fun getTypeIcon(type: QRCodeType): ImageVector = when (type) {
    QRCodeType.TEXT -> Icons.AutoMirrored.Filled.TextSnippet
    QRCodeType.URL -> Icons.Default.Language
    QRCodeType.WIFI -> Icons.Default.NetworkWifi
    QRCodeType.EMAIL -> Icons.Default.Email
    QRCodeType.PHONE -> Icons.Default.Phone
    QRCodeType.SMS -> Icons.AutoMirrored.Filled.Message
    QRCodeType.CONTACT -> Icons.Default.Person
    QRCodeType.LOCATION -> Icons.Default.LocationOn
    QRCodeType.CALENDAR -> Icons.Default.CalendarToday
    QRCodeType.OTHER -> Icons.Default.QrCode2
}

@Composable
private fun getTypeColor(type: QRCodeType): Color = when (type) {
    QRCodeType.TEXT -> GenerateColor
    QRCodeType.URL -> ScanColor
    QRCodeType.WIFI -> SparkGreen
    QRCodeType.EMAIL -> CollectionColor
    QRCodeType.PHONE -> SuccessGreen
    QRCodeType.SMS -> SparkPink
    QRCodeType.CONTACT -> InfoBlue
    QRCodeType.LOCATION -> WarningOrange
    QRCodeType.CALENDAR -> SparkRed
    QRCodeType.OTHER -> MaterialTheme.colorScheme.onSurfaceVariant
}