package com.example.qr.presentation.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.example.qr.presentation.screens.generator.QRGeneratorScreen
import com.example.qr.presentation.screens.history.HistoryScreen
import com.example.qr.presentation.screens.home.HomeScreen
import com.example.qr.presentation.screens.scanner.QRScannerScreen
import com.example.qr.presentation.screens.settings.SettingsScreen
import com.example.qr.presentation.screens.help.HelpScreen
import com.example.qr.R
import com.example.qr.ui.screens.SparkHomeScreen
import com.example.qr.ui.components.ReactiveLanguageContent
import com.example.qr.utils.ShareUtils

@Composable
fun QRNavigation(navController: NavHostController) {
    val context = LocalContext.current

    NavHost(
        navController = navController,
        startDestination = Screen.Home.route
    ) {
        composable(Screen.Home.route) {
            ReactiveLanguageContent {
                SparkHomeScreen(
                    onScanClick = { navController.navigate(Screen.Scanner.route) },
                    onGenerateClick = { navController.navigate(Screen.Generator.route) },
                    onCollectionClick = { navController.navigate(Screen.History.route) },
                    onSettingsClick = { navController.navigate(Screen.Settings.route) },
                    onShareClick = { ShareUtils.shareApp(context) },
                    onHelpClick = { navController.navigate(Screen.Help.route) }
                )
            }
        }

        composable(Screen.Scanner.route) {
            ReactiveLanguageContent {
                QRScannerScreen(navController = navController)
            }
        }

        composable(Screen.Generator.route) {
            ReactiveLanguageContent {
                QRGeneratorScreen(navController = navController)
            }
        }

        composable(Screen.History.route) {
            ReactiveLanguageContent {
                HistoryScreen(navController = navController)
            }
        }

        composable(Screen.Settings.route) {
            SettingsScreen(navController = navController)
        }

        composable(Screen.Help.route) {
            ReactiveLanguageContent {
                HelpScreen(navController = navController)
            }
        }
    }
}

sealed class Screen(val route: String, val titleRes: Int) {
    data object Home : Screen("home", R.string.nav_title_home)
    data object Scanner : Screen("scanner", R.string.nav_title_scanner)
    data object Generator : Screen("generator", R.string.nav_title_generator)
    data object History : Screen("history", R.string.nav_title_history)
    data object Settings : Screen("settings", R.string.nav_title_settings)
    data object Help : Screen("help", R.string.nav_title_help)
}
