package com.example.qr.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.qr.ui.theme.QRTheme
import com.example.qr.ui.theme.SuccessGreen
import com.example.qr.utils.TutorialStep

/**
 * Comprehensive test preview for tutorial navigation buttons
 * Tests all button states, sizing, and text visibility
 */
@Preview(showBackground = true, widthDp = 400)
@Composable
fun TutorialNavigationButtonsPreview() {
    QRTheme {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                Text(
                    "Tutorial Navigation Button Tests",
                    style = MaterialTheme.typography.headlineSmall
                )
            }
            
            // Test 1: First step (only Continue visible)
            item {
                TestCard(
                    title = "First Step - Only Continue Button",
                    description = "Tests single button layout with proper centering"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 0,
                        totalSteps = 4,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { }
                    )
                }
            }
            
            // Test 2: Middle step (both buttons visible)
            item {
                TestCard(
                    title = "Middle Step - Back and Continue",
                    description = "Tests dual button layout with equal sizing"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 1,
                        totalSteps = 4,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { }
                    )
                }
            }
            
            // Test 3: Final step (Back and Finish)
            item {
                TestCard(
                    title = "Final Step - Back and Finish",
                    description = "Tests success button variant with proper styling"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 3,
                        totalSteps = 4,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { }
                    )
                }
            }
            
            // Test 4: Custom action text
            item {
                TestCard(
                    title = "Custom Action Text",
                    description = "Tests longer text handling and wrapping"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 1,
                        totalSteps = 3,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { },
                        customActionText = "Get Started Now"
                    )
                }
            }
            
            // Test 5: Very long custom text
            item {
                TestCard(
                    title = "Long Text Wrapping Test",
                    description = "Tests improved text wrapping without mid-word breaks"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 2,
                        totalSteps = 4,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { },
                        customActionText = "Continue to Advanced Settings"
                    )
                }
            }

            // Test 6: Extreme text length test
            item {
                TestCard(
                    title = "Extreme Text Length Test",
                    description = "Tests button behavior with very long text that would normally cause issues"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 1,
                        totalSteps = 3,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { },
                        customActionText = "Continue to Advanced Configuration and Settings"
                    )
                }
            }
            
            // Test 7: Single step tutorial
            item {
                TestCard(
                    title = "Single Step Tutorial",
                    description = "Tests single-step tutorial with only Finish button"
                ) {
                    TutorialNavigationButtons(
                        currentStep = 0,
                        totalSteps = 1,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { }
                    )
                }
            }

            // Test 8: Font size optimization test
            item {
                TestCard(
                    title = "Font Size Optimization Test",
                    description = "Tests 14sp font size prevents text overflow while maintaining readability"
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        Text(
                            "✅ Optimized: 14sp font size prevents overflow",
                            style = MaterialTheme.typography.labelSmall,
                            color = SuccessGreen
                        )
                        TutorialNavigationButtons(
                            currentStep = 1,
                            totalSteps = 3,
                            onPrevious = { },
                            onNext = { },
                            onFinish = { },
                            customActionText = "Continue to Advanced Configuration"
                        )
                    }
                }
            }

            // Test 9: Multilingual text length test
            item {
                TestCard(
                    title = "Multilingual Text Length Test",
                    description = "Tests button sizing with longer multilingual text equivalents"
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(12.dp)) {
                        // English
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text("EN:", style = MaterialTheme.typography.labelSmall, modifier = Modifier.width(24.dp))
                            TutorialNavigationButtons(
                                currentStep = 1,
                                totalSteps = 3,
                                onPrevious = { },
                                onNext = { },
                                onFinish = { },
                                customActionText = "Continue"
                            )
                        }

                        // Spanish (longer)
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text("ES:", style = MaterialTheme.typography.labelSmall, modifier = Modifier.width(24.dp))
                            TutorialNavigationButtons(
                                currentStep = 1,
                                totalSteps = 3,
                                onPrevious = { },
                                onNext = { },
                                onFinish = { },
                                customActionText = "Continuar a Configuración Avanzada"
                            )
                        }

                        // French (longer)
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Text("FR:", style = MaterialTheme.typography.labelSmall, modifier = Modifier.width(24.dp))
                            TutorialNavigationButtons(
                                currentStep = 1,
                                totalSteps = 3,
                                onPrevious = { },
                                onNext = { },
                                onFinish = { },
                                customActionText = "Continuer vers Configuration Avancée"
                            )
                        }
                    }
                }
            }

            // Test 10: Button variant consistency test
            item {
                TestCard(
                    title = "Button Variant Font Consistency Test",
                    description = "Tests 14sp font size across all button variants"
                ) {
                    Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                        // Primary variant
                        TutorialNavigationButtons(
                            currentStep = 1,
                            totalSteps = 4,
                            onPrevious = { },
                            onNext = { },
                            onFinish = { },
                            customActionText = "Primary Button Test"
                        )

                        // Secondary + Success variants
                        TutorialNavigationButtons(
                            currentStep = 2,
                            totalSteps = 4,
                            onPrevious = { },
                            onNext = { },
                            onFinish = { },
                            customActionText = "Secondary & Success Test"
                        )

                        // Final step (Success variant)
                        TutorialNavigationButtons(
                            currentStep = 3,
                            totalSteps = 4,
                            onPrevious = { },
                            onNext = { },
                            onFinish = { },
                            customActionText = "Success Button Test"
                        )
                    }
                }
            }
        }
    }
}

/**
 * Test card wrapper for consistent testing layout
 */
@Composable
private fun TestCard(
    title: String,
    description: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.primary
                )
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f)
                )
            }
            
            content()
        }
    }
}

/**
 * Preview for complete tutorial dialog with navigation buttons
 */
@Preview(showBackground = true, widthDp = 400, heightDp = 600)
@Composable
fun CompleteTutorialDialogPreview() {
    QRTheme {
        var showTutorial by remember { mutableStateOf(true) }
        
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Button(
                onClick = { showTutorial = true }
            ) {
                Text("Show Tutorial")
            }
            
            if (showTutorial) {
                TutorialOverlay(
                    steps = listOf(
                        TutorialStep(
                            title = "Welcome to QR Spark!",
                            description = "Your all-in-one QR solution. Scan instantly, generate easily, manage effortlessly.",
                            action = "Get Started"
                        ),
                        TutorialStep(
                            title = "Instant Scanning",
                            description = "Scan QR codes with your camera or from images. Save time with instant recognition.",
                            action = "Continue"
                        ),
                        TutorialStep(
                            title = "Easy Generation",
                            description = "Create custom QR codes for text, URLs, WiFi, and contacts. Share information easily.",
                            action = "Continue"
                        ),
                        TutorialStep(
                            title = "Smart Management",
                            description = "Access your complete QR history with favorites and search. Never lose important codes.",
                            action = "Finish"
                        )
                    ),
                    onComplete = { showTutorial = false },
                    onSkip = { showTutorial = false }
                )
            }
        }
    }
}

/**
 * Preview for button dimension testing
 */
@Preview(showBackground = true, widthDp = 350)
@Composable
fun ButtonDimensionTestPreview() {
    QRTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                "Button Dimension Tests",
                style = MaterialTheme.typography.headlineSmall
            )
            
            // Test narrow dialog width
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text("Narrow Dialog Test (350dp)", style = MaterialTheme.typography.labelMedium)
                    Spacer(modifier = Modifier.height(8.dp))
                    TutorialNavigationButtons(
                        currentStep = 1,
                        totalSteps = 3,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { }
                    )
                }
            }
            
            // Test with very long text
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = Color.White)
            ) {
                Column(modifier = Modifier.padding(16.dp)) {
                    Text("Long Text Stress Test", style = MaterialTheme.typography.labelMedium)
                    Spacer(modifier = Modifier.height(8.dp))
                    TutorialNavigationButtons(
                        currentStep = 1,
                        totalSteps = 3,
                        onPrevious = { },
                        onNext = { },
                        onFinish = { },
                        customActionText = "Continue to Advanced Configuration"
                    )
                }
            }
        }
    }
}
