package com.example.qr.ui.screens

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Help
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.qr.R
import com.example.qr.ui.theme.*
import com.example.qr.ui.components.GradientBackground
import com.example.qr.ui.components.SparkActionCard
import com.example.qr.ui.components.TutorialTrigger
import com.example.qr.utils.SafeAreaUtils
import com.example.qr.utils.TutorialManager
import com.example.qr.utils.TutorialContent
import com.example.qr.utils.AnimationManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SparkHomeScreen(
    onScanClick: () -> Unit,
    onGenerateClick: () -> Unit,
    onCollectionClick: () -> Unit,
    onSettingsClick: () -> Unit,
    onShareClick: () -> Unit,
    onHelpClick: () -> Unit
) {
    GradientBackground(applyScreenSafeArea = false) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.statusBars)
                .windowInsetsPadding(WindowInsets.navigationBars)
                .windowInsetsPadding(WindowInsets.displayCutout)
                .padding(SafeAreaUtils.responsiveContentPadding()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
        Spacer(modifier = Modifier.height(32.dp))

        // QR Spark Branding
        SparkBrandingSection()

        Spacer(modifier = Modifier.height(80.dp))

        // Main Action Cards
        MainActionCards(
            onScanClick = onScanClick,
            onGenerateClick = onGenerateClick,
            onCollectionClick = onCollectionClick
        )

        Spacer(modifier = Modifier.weight(1f))

        // Bottom Action Buttons
        BottomActionRow(
            onSettingsClick = onSettingsClick,
            onShareClick = onShareClick,
            onHelpClick = onHelpClick
        )

            Spacer(modifier = Modifier.height(40.dp))
        }

        // Tutorial overlay for first-time users
        val context = LocalContext.current
        TutorialTrigger(
            tutorialKey = TutorialManager.TutorialKeys.HOME_SCREEN,
            steps = TutorialContent.getHomeTutorials(context)
        )
    }
}

@Composable
private fun SparkBrandingSection() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // QR Spark Title with Improved Typography Hierarchy
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "QR ",
                fontSize = 32.sp, // Slightly smaller for better balance
                fontWeight = FontWeight.Bold,
                color = GenerateColor // Use primary blue
            )
            Text(
                text = "Spark",
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = SparkPink
            )
        }

        Spacer(modifier = Modifier.height(12.dp))

        Text(
            text = stringResource(R.string.home_subtitle),
            fontSize = 16.sp,
            fontStyle = androidx.compose.ui.text.font.FontStyle.Italic,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            lineHeight = 22.sp
        )
    }
}

@Composable
private fun MainActionCards(
    onScanClick: () -> Unit,
    onGenerateClick: () -> Unit,
    onCollectionClick: () -> Unit
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Scan QR Card
        SparkActionCard(
            title = stringResource(R.string.home_scan_title),
            subtitle = stringResource(R.string.home_scan_description),
            icon = {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = ScanColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.QrCodeScanner,
                        contentDescription = "Scan QR",
                        tint = ScanColor,
                        modifier = Modifier.size(24.dp)
                    )
                }
            },
            onClick = onScanClick
        )

        // Generate QR Card
        SparkActionCard(
            title = stringResource(R.string.home_generate_title),
            subtitle = stringResource(R.string.home_generate_description),
            icon = {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = GenerateColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.QrCode2,
                        contentDescription = "Generate QR",
                        tint = GenerateColor,
                        modifier = Modifier.size(24.dp)
                    )
                }
            },
            onClick = onGenerateClick
        )

        // My Collection Card
        SparkActionCard(
            title = stringResource(R.string.home_history_title),
            subtitle = stringResource(R.string.home_history_description),
            icon = {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = CollectionColor.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(12.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.GridView, // More meaningful collection icon
                        contentDescription = "My Collection",
                        tint = CollectionColor,
                        modifier = Modifier.size(24.dp)
                    )
                }
            },
            onClick = onCollectionClick,
            trailingIcon = {
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "Navigate",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    modifier = Modifier.size(24.dp)
                )
            }
        )
    }
}

@Composable
private fun BottomActionRow(
    onSettingsClick: () -> Unit,
    onShareClick: () -> Unit,
    onHelpClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        LabeledActionButton(
            icon = Icons.Default.Settings,
            label = stringResource(R.string.nav_title_settings),
            onClick = onSettingsClick
        )

        LabeledActionButton(
            icon = Icons.Default.Share,
            label = stringResource(R.string.share),
            onClick = onShareClick
        )

        LabeledActionButton(
            icon = Icons.AutoMirrored.Filled.Help,
            label = stringResource(R.string.nav_title_help),
            onClick = onHelpClick
        )
    }
}

@Composable
private fun LabeledActionButton(
    icon: ImageVector,
    label: String,
    onClick: () -> Unit
) {
    var isPressed by remember { mutableStateOf(false) }
    val scale by AnimationManager.animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = AnimationManager.getTweenSpec<Float>(150),
        label = "button_scale"
    )

    Column(
        modifier = Modifier
            .scale(scale)
            .clickable {
                isPressed = true
                onClick()
            }
            .padding(8.dp), // Better tap area
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(48.dp) // Minimum 48dp tap target
                .clip(CircleShape)
                .background(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                            MaterialTheme.colorScheme.primary.copy(alpha = 0.05f)
                        )
                    )
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(24.dp)
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = label,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }

    LaunchedEffect(isPressed) {
        if (isPressed) {
            kotlinx.coroutines.delay(150)
            isPressed = false
        }
    }
}
