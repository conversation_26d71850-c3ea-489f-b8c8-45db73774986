package com.example.qr.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.res.stringResource
import com.example.qr.R
import com.example.qr.ui.theme.*
import kotlinx.coroutines.delay

/**
 * Enhanced tutorial button with QR Spark design language
 */
@Composable
fun EnhancedTutorialButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    variant: TutorialButtonVariant = TutorialButtonVariant.Primary,
    icon: ImageVector? = null,
    hapticFeedback: Boolean = true
) {
    val haptic = LocalHapticFeedback.current
    val interactionSource = remember { MutableInteractionSource() }
    
    var isPressed by remember { mutableStateOf(false) }
    var justClicked by remember { mutableStateOf(false) }
    
    // Color scheme based on variant
    val colors = when (variant) {
        TutorialButtonVariant.Primary -> TutorialButtonColors(
            background = ScanColor,
            content = Color.White,
            border = ScanColor.copy(alpha = 0.3f),
            shadow = ScanColor.copy(alpha = 0.2f)
        )
        TutorialButtonVariant.Secondary -> TutorialButtonColors(
            background = Color.Transparent,
            content = MaterialTheme.colorScheme.onSurface,
            border = MaterialTheme.colorScheme.outline.copy(alpha = 0.5f),
            shadow = Color.Transparent
        )
        TutorialButtonVariant.Success -> TutorialButtonColors(
            background = SuccessGreen,
            content = Color.White,
            border = SuccessGreen.copy(alpha = 0.3f),
            shadow = SuccessGreen.copy(alpha = 0.2f)
        )
        TutorialButtonVariant.Info -> TutorialButtonColors(
            background = InfoBlue,
            content = Color.White,
            border = InfoBlue.copy(alpha = 0.3f),
            shadow = InfoBlue.copy(alpha = 0.2f)
        )
    }
    
    // Animations
    val backgroundColor by animateColorAsState(
        targetValue = when {
            !enabled -> colors.background.copy(alpha = 0.3f)
            isPressed -> colors.background.copy(alpha = 0.8f)
            else -> colors.background
        },
        animationSpec = tween(durationMillis = 150),
        label = "background_color"
    )
    
    val contentColor by animateColorAsState(
        targetValue = when {
            !enabled -> colors.content.copy(alpha = 0.5f)
            else -> colors.content
        },
        animationSpec = tween(durationMillis = 150),
        label = "content_color"
    )
    
    val scale by animateFloatAsState(
        targetValue = when {
            isPressed -> 0.95f
            justClicked -> 1.05f
            else -> 1f
        },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "scale"
    )
    
    // Reset animations
    LaunchedEffect(isPressed) {
        if (isPressed) {
            delay(100)
            isPressed = false
        }
    }
    
    LaunchedEffect(justClicked) {
        if (justClicked) {
            delay(150)
            justClicked = false
        }
    }
    
    Box(
        modifier = modifier
            .scale(scale)
            .shadow(
                elevation = if (variant == TutorialButtonVariant.Secondary) 0.dp else 8.dp,
                shape = RoundedCornerShape(28.dp),
                ambientColor = colors.shadow,
                spotColor = colors.shadow
            )
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(28.dp)
            )
            .border(
                width = if (variant == TutorialButtonVariant.Secondary) 1.5.dp else 0.dp,
                color = colors.border,
                shape = RoundedCornerShape(28.dp)
            )
            .clip(RoundedCornerShape(28.dp))
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled
            ) {
                if (hapticFeedback) {
                    haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                }
                isPressed = true
                justClicked = true
                onClick()
            }
            .padding(horizontal = 24.dp, vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = contentColor,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            
            Text(
                text = text,
                color = contentColor,
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                fontFamily = SafeFontFamily,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Enhanced tutorial progress bar with modern design
 */
@Composable
fun EnhancedTutorialProgressBar(
    currentStep: Int,
    totalSteps: Int,
    modifier: Modifier = Modifier
) {
    val progress = (currentStep + 1).toFloat() / totalSteps.toFloat()

    val animatedProgress by animateFloatAsState(
        targetValue = progress,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "progress"
    )

    Column(modifier = modifier) {
        // Thin progress bar at top
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(3.dp)
                .background(
                    color = MaterialTheme.colorScheme.outline.copy(alpha = 0.15f),
                    shape = RoundedCornerShape(1.5.dp)
                )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(animatedProgress)
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(ScanColor, SuccessGreen)
                        ),
                        shape = RoundedCornerShape(1.5.dp)
                    )
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Step indicator with modern typography
        Text(
            text = "Step ${currentStep + 1} of $totalSteps",
            style = MaterialTheme.typography.labelLarge.copy(
                fontWeight = FontWeight.SemiBold,
                fontSize = 13.sp,
                letterSpacing = 0.8.sp
            ),
            color = ScanColor,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center,
            fontFamily = SafeFontFamily
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Dot indicators at bottom
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center
        ) {
            repeat(totalSteps) { index ->
                val isActive = index <= currentStep
                val isCurrent = index == currentStep

                val dotColor by animateColorAsState(
                    targetValue = when {
                        isCurrent -> ScanColor
                        isActive -> ScanColor.copy(alpha = 0.6f)
                        else -> MaterialTheme.colorScheme.outline.copy(alpha = 0.25f)
                    },
                    animationSpec = tween(durationMillis = 300),
                    label = "dot_color_$index"
                )

                val dotSize by animateDpAsState(
                    targetValue = if (isCurrent) 10.dp else 8.dp,
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    ),
                    label = "dot_size_$index"
                )

                Box(
                    modifier = Modifier
                        .size(dotSize)
                        .background(
                            color = dotColor,
                            shape = CircleShape
                        )
                        .shadow(
                            elevation = if (isCurrent) 2.dp else 0.dp,
                            shape = CircleShape
                        )
                )

                if (index < totalSteps - 1) {
                    Spacer(modifier = Modifier.width(12.dp))
                }
            }
        }
    }
}

/**
 * Tutorial button variants
 */
enum class TutorialButtonVariant {
    Primary,
    Secondary,
    Success,
    Info
}

/**
 * Tutorial button color scheme
 */
data class TutorialButtonColors(
    val background: Color,
    val content: Color,
    val border: Color,
    val shadow: Color
)

/**
 * Predefined tutorial button variants
 */
@Composable
fun TutorialPrimaryButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null
) {
    EnhancedTutorialButton(
        text = text,
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        variant = TutorialButtonVariant.Primary,
        icon = icon
    )
}

@Composable
fun TutorialSecondaryButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null
) {
    EnhancedTutorialButton(
        text = text,
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        variant = TutorialButtonVariant.Secondary,
        icon = icon
    )
}

@Composable
fun TutorialSuccessButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    icon: ImageVector? = null
) {
    EnhancedTutorialButton(
        text = text,
        onClick = onClick,
        modifier = modifier,
        enabled = enabled,
        variant = TutorialButtonVariant.Success,
        icon = icon
    )
}

/**
 * Standardized tutorial navigation buttons
 */
@Composable
fun TutorialNavigationButtons(
    currentStep: Int,
    totalSteps: Int,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onFinish: () -> Unit,
    modifier: Modifier = Modifier,
    customActionText: String? = null
) {
    val isFirstStep = currentStep == 0
    val isLastStep = currentStep == totalSteps - 1

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Previous button (invisible spacer on first step for consistent layout)
        if (isFirstStep) {
            Spacer(modifier = Modifier.weight(1f))
        } else {
            StandardTutorialButton(
                text = "Back",
                onClick = onPrevious,
                icon = Icons.AutoMirrored.Filled.ArrowBack,
                variant = TutorialButtonVariant.Secondary,
                modifier = Modifier.weight(1f)
            )
        }

        // Next/Finish button
        if (isLastStep) {
            StandardTutorialButton(
                text = customActionText ?: "Finish",
                onClick = onFinish,
                icon = Icons.Default.Check,
                variant = TutorialButtonVariant.Success,
                modifier = Modifier.weight(1f)
            )
        } else {
            StandardTutorialButton(
                text = customActionText ?: "Continue",
                onClick = onNext,
                icon = Icons.AutoMirrored.Filled.ArrowForward,
                variant = TutorialButtonVariant.Primary,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * Standardized tutorial button with improved text wrapping and layout
 */
@Composable
private fun StandardTutorialButton(
    text: String,
    onClick: () -> Unit,
    variant: TutorialButtonVariant,
    modifier: Modifier = Modifier,
    icon: ImageVector? = null,
    enabled: Boolean = true
) {
    val haptic = LocalHapticFeedback.current
    val interactionSource = remember { MutableInteractionSource() }

    var isPressed by remember { mutableStateOf(false) }
    var justClicked by remember { mutableStateOf(false) }

    val colors = when (variant) {
        TutorialButtonVariant.Primary -> TutorialButtonColors(
            background = ScanColor,
            content = Color.White,
            border = ScanColor.copy(alpha = 0.3f),
            shadow = ScanColor.copy(alpha = 0.2f)
        )
        TutorialButtonVariant.Secondary -> TutorialButtonColors(
            background = MaterialTheme.colorScheme.surface,
            content = MaterialTheme.colorScheme.onSurface,
            border = MaterialTheme.colorScheme.outline.copy(alpha = 0.7f),
            shadow = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)
        )
        TutorialButtonVariant.Success -> TutorialButtonColors(
            background = SuccessGreen,
            content = Color.White,
            border = SuccessGreen.copy(alpha = 0.3f),
            shadow = SuccessGreen.copy(alpha = 0.2f)
        )
        TutorialButtonVariant.Info -> TutorialButtonColors(
            background = InfoBlue,
            content = Color.White,
            border = InfoBlue.copy(alpha = 0.3f),
            shadow = InfoBlue.copy(alpha = 0.2f)
        )
    }

    val backgroundColor by animateColorAsState(
        targetValue = if (!enabled) colors.background.copy(alpha = 0.3f)
        else if (isPressed) colors.background.copy(alpha = 0.8f)
        else colors.background,
        animationSpec = tween(150), label = "bgColor"
    )

    val contentColor by animateColorAsState(
        targetValue = if (!enabled) colors.content.copy(alpha = 0.5f)
        else colors.content,
        animationSpec = tween(150), label = "contentColor"
    )

    val scale by animateFloatAsState(
        targetValue = when {
            isPressed -> 0.95f
            justClicked -> 1.05f
            else -> 1f
        },
        animationSpec = spring(Spring.DampingRatioMediumBouncy, Spring.StiffnessMedium),
        label = "scaleAnim"
    )

    LaunchedEffect(isPressed) {
        if (isPressed) {
            delay(100)
            isPressed = false
        }
    }

    LaunchedEffect(justClicked) {
        if (justClicked) {
            delay(150)
            justClicked = false
        }
    }

    Box(
        modifier = modifier
            .height(56.dp)
            .scale(scale)
            .shadow(
                elevation = if (variant == TutorialButtonVariant.Secondary) 4.dp else 8.dp,
                shape = RoundedCornerShape(28.dp),
                ambientColor = colors.shadow,
                spotColor = colors.shadow
            )
            .background(backgroundColor, RoundedCornerShape(28.dp))
            .border(
                width = if (variant == TutorialButtonVariant.Secondary) 1.5.dp else 0.dp,
                color = colors.border,
                shape = RoundedCornerShape(28.dp)
            )
            .clip(RoundedCornerShape(28.dp))
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled
            ) {
                haptic.performHapticFeedback(HapticFeedbackType.LongPress)
                isPressed = true
                justClicked = true
                onClick()
            }
            .padding(horizontal = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier.wrapContentSize()
        ) {
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = contentColor,
                    modifier = Modifier
                        .size(18.dp)
                        .padding(end = 8.dp)
                )
            }

            Text(
                text = text,
                color = contentColor,
                fontSize = 14.sp, // Reduced from 16sp to prevent text overflow
                fontWeight = FontWeight.SemiBold,
                fontFamily = SafeFontFamily,
                textAlign = TextAlign.Center,
                maxLines = 2,
                softWrap = true,
                modifier = Modifier
                    .wrapContentWidth(unbounded = true)
            )
        }
    }
}

/**
 * Localized tutorial navigation buttons that use string resources
 */
@Composable
fun LocalizedTutorialNavigationButtons(
    currentStep: Int,
    totalSteps: Int,
    onPrevious: () -> Unit,
    onNext: () -> Unit,
    onFinish: () -> Unit,
    modifier: Modifier = Modifier,
    customActionText: String? = null
) {
    val isFirstStep = currentStep == 0
    val isLastStep = currentStep == totalSteps - 1

    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Previous button (invisible spacer on first step for consistent layout)
        if (isFirstStep) {
            Spacer(modifier = Modifier.weight(1f))
        } else {
            StandardTutorialButton(
                text = stringResource(R.string.back),
                onClick = onPrevious,
                icon = Icons.AutoMirrored.Filled.ArrowBack,
                variant = TutorialButtonVariant.Secondary,
                modifier = Modifier.weight(1f)
            )
        }

        // Next/Finish button
        if (isLastStep) {
            StandardTutorialButton(
                text = customActionText ?: stringResource(R.string.action_finish),
                onClick = onFinish,
                icon = Icons.Default.Check,
                variant = TutorialButtonVariant.Success,
                modifier = Modifier.weight(1f)
            )
        } else {
            StandardTutorialButton(
                text = customActionText ?: stringResource(R.string.action_continue),
                onClick = onNext,
                icon = Icons.AutoMirrored.Filled.ArrowForward,
                variant = TutorialButtonVariant.Primary,
                modifier = Modifier.weight(1f)
            )
        }
    }
}
