  Manifest android  Activity android.app  Application android.app  Bundle android.app.Activity  Context android.app.Activity  
Configuration android.app.Application  Context android.app.Application  getSystemService android.app.Application  ClipData android.content  ClipboardManager android.content  Context android.content  Intent android.content  
AUDIO_SERVICE android.content.Context  Bundle android.content.Context  
Configuration android.content.Context  Context android.content.Context  VIBRATOR_MANAGER_SERVICE android.content.Context  VIBRATOR_SERVICE android.content.Context  	dataStore android.content.Context  getDATAStore android.content.Context  getDataStore android.content.Context  getSystemService android.content.Context  Bundle android.content.ContextWrapper  
Configuration android.content.ContextWrapper  Context android.content.ContextWrapper  getSystemService android.content.ContextWrapper  PackageManager android.content.pm  
Configuration android.content.res  Bitmap android.graphics  
BitmapFactory android.graphics  Canvas android.graphics  Color android.graphics  Paint android.graphics  PdfDocument android.graphics.pdf  AudioAttributes 
android.media  AudioManager 
android.media  MediaPlayer 
android.media  	SoundPool 
android.media  
ToneGenerator 
android.media  Uri android.net  Build 
android.os  Bundle 
android.os  Environment 
android.os  Handler 
android.os  Looper 
android.os  VibrationEffect 
android.os  Vibrator 
android.os  VibratorManager 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  defaultVibrator android.os.VibratorManager  getDEFAULTVibrator android.os.VibratorManager  getDefaultVibrator android.os.VibratorManager  setDefaultVibrator android.os.VibratorManager  
MediaStore android.provider  Log android.util  MotionEvent android.view  ScaleGestureDetector android.view  Bundle  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  Toast android.widget  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  ActivityResultContracts !androidx.activity.result.contract  OptIn androidx.annotation  	StringRes androidx.annotation  BarcodeScanning androidx.camera.core  Camera androidx.camera.core  
Composable androidx.camera.core  ExperimentalGetImage androidx.camera.core  
ImageAnalysis androidx.camera.core  
ImageProxy androidx.camera.core  Pair androidx.camera.core  Volatile androidx.camera.core  Analyzer "androidx.camera.core.ImageAnalysis  ProcessCameraProvider androidx.camera.lifecycle  PreviewView androidx.camera.view  AnimatedVisibility androidx.compose.animation  
Composable androidx.compose.animation  ExperimentalFoundationApi androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  animateColorAsState androidx.compose.animation  animateContentSize androidx.compose.animation  slideInVertically androidx.compose.animation  
AnimationSpec androidx.compose.animation.core  
CardColors androidx.compose.animation.core  
CardElevation androidx.compose.animation.core  ColumnScope androidx.compose.animation.core  
Composable androidx.compose.animation.core  DurationBasedAnimationSpec androidx.compose.animation.core  Easing androidx.compose.animation.core  ExperimentalComposeUiApi androidx.compose.animation.core  ExperimentalFoundationApi androidx.compose.animation.core  ExperimentalMaterial3Api androidx.compose.animation.core  ExperimentalPermissionsApi androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  Spring androidx.compose.animation.core  
SpringSpec androidx.compose.animation.core  State androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  android androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  com androidx.compose.animation.core  spring androidx.compose.animation.core  tween androidx.compose.animation.core  BorderStroke androidx.compose.foundation  Canvas androidx.compose.foundation  ExperimentalFoundationApi androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures  detectTransformGestures $androidx.compose.foundation.gestures  MutableInteractionSource 'androidx.compose.foundation.interaction  BarcodeScanning "androidx.compose.foundation.layout  
CardColors "androidx.compose.foundation.layout  
CardElevation "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalComposeUiApi "androidx.compose.foundation.layout  ExperimentalFoundationApi "androidx.compose.foundation.layout  ExperimentalGetImage "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExperimentalPermissionsApi "androidx.compose.foundation.layout  
ImageAnalysis "androidx.compose.foundation.layout  
ImageProxy "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  WindowInsets "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  
displayCutout "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  ime "androidx.compose.foundation.layout  navigationBars "androidx.compose.foundation.layout  safeDrawing "androidx.compose.foundation.layout  
statusBars "androidx.compose.foundation.layout  
systemBars "androidx.compose.foundation.layout  windowInsetsPadding "androidx.compose.foundation.layout  safeDrawing /androidx.compose.foundation.layout.WindowInsets  
statusBars /androidx.compose.foundation.layout.WindowInsets  getSAFEDrawing 9androidx.compose.foundation.layout.WindowInsets.Companion  
getSTATUSBars 9androidx.compose.foundation.layout.WindowInsets.Companion  getSafeDrawing 9androidx.compose.foundation.layout.WindowInsets.Companion  
getStatusBars 9androidx.compose.foundation.layout.WindowInsets.Companion  safeDrawing 9androidx.compose.foundation.layout.WindowInsets.Companion  
statusBars 9androidx.compose.foundation.layout.WindowInsets.Companion  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  
Composable 3androidx.compose.material.icons.automirrored.filled  ContactSupport 3androidx.compose.material.icons.automirrored.filled  Help 3androidx.compose.material.icons.automirrored.filled  KeyboardArrowRight 3androidx.compose.material.icons.automirrored.filled  Message 3androidx.compose.material.icons.automirrored.filled  	OpenInNew 3androidx.compose.material.icons.automirrored.filled  TextSnippet 3androidx.compose.material.icons.automirrored.filled  VolumeUp 3androidx.compose.material.icons.automirrored.filled  Check &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  ExperimentalComposeUiApi &androidx.compose.material.icons.filled  ExperimentalFoundationApi &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  ExperimentalPermissionsApi &androidx.compose.material.icons.filled  Language &androidx.compose.material.icons.filled  Security &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  BarcodeScanning androidx.compose.material3  
CardColors androidx.compose.material3  
CardElevation androidx.compose.material3  ColorScheme androidx.compose.material3  ColumnScope androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalComposeUiApi androidx.compose.material3  ExperimentalFoundationApi androidx.compose.material3  ExperimentalGetImage androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExperimentalPermissionsApi androidx.compose.material3  
ImageAnalysis androidx.compose.material3  
ImageProxy androidx.compose.material3  
MaterialTheme androidx.compose.material3  Surface androidx.compose.material3  TabRowDefaults androidx.compose.material3  
Typography androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  com androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  tabIndicatorOffset )androidx.compose.material3.TabRowDefaults  
AnimationSpec androidx.compose.runtime  BarcodeScanning androidx.compose.runtime  
CardColors androidx.compose.runtime  
CardElevation androidx.compose.runtime  ColumnScope androidx.compose.runtime  
Composable androidx.compose.runtime  DurationBasedAnimationSpec androidx.compose.runtime  Easing androidx.compose.runtime  ExperimentalComposeUiApi androidx.compose.runtime  ExperimentalFoundationApi androidx.compose.runtime  ExperimentalGetImage androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExperimentalPermissionsApi androidx.compose.runtime  
ImageAnalysis androidx.compose.runtime  
ImageProxy androidx.compose.runtime  InfiniteRepeatableSpec androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  PREF_GENERATOR_TUTORIAL_SHOWN androidx.compose.runtime  PREF_HISTORY_TUTORIAL_SHOWN androidx.compose.runtime  PREF_HOME_TUTORIAL_SHOWN androidx.compose.runtime  PREF_SCANNER_TUTORIAL_SHOWN androidx.compose.runtime  PREF_SETTINGS_TUTORIAL_SHOWN androidx.compose.runtime  
RepeatMode androidx.compose.runtime  
SpringSpec androidx.compose.runtime  State androidx.compose.runtime  	TweenSpec androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  getValue androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  	Alignment androidx.compose.ui  ExperimentalComposeUiApi androidx.compose.ui  Modifier androidx.compose.ui  alpha androidx.compose.ui.draw  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  shadow androidx.compose.ui.draw  CornerRadius androidx.compose.ui.geometry  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  invoke ,androidx.compose.ui.graphics.Color.Companion  Stroke &androidx.compose.ui.graphics.drawscope  ImageVector #androidx.compose.ui.graphics.vector  HapticFeedbackType "androidx.compose.ui.hapticfeedback  pointerInput !androidx.compose.ui.input.pointer  pointerInteropFilter !androidx.compose.ui.input.pointer  LocalContext androidx.compose.ui.platform  LocalHapticFeedback androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  Font androidx.compose.ui.text.font  
FontFamily androidx.compose.ui.text.font  	FontStyle androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  	SansSerif (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	SansSerif 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	ExtraBold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	ExtraBold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  Dialog androidx.compose.ui.window  DialogProperties androidx.compose.ui.window  Bundle #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  FileProvider androidx.core.content  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  Preferences #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  longPreferencesKey #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  AndroidViewModel androidx.lifecycle  	Lifecycle androidx.lifecycle  LifecycleEventObserver androidx.lifecycle  LifecycleOwner androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  AppSettings #androidx.lifecycle.AndroidViewModel  Application #androidx.lifecycle.AndroidViewModel  AudioManager #androidx.lifecycle.AndroidViewModel  Boolean #androidx.lifecycle.AndroidViewModel  Build #androidx.lifecycle.AndroidViewModel  Context #androidx.lifecycle.AndroidViewModel  List #androidx.lifecycle.AndroidViewModel  MutableStateFlow #androidx.lifecycle.AndroidViewModel  SettingsRepository #androidx.lifecycle.AndroidViewModel  	StateFlow #androidx.lifecycle.AndroidViewModel  String #androidx.lifecycle.AndroidViewModel  Suppress #androidx.lifecycle.AndroidViewModel  
ToneGenerator #androidx.lifecycle.AndroidViewModel  Unit #androidx.lifecycle.AndroidViewModel  Vibrator #androidx.lifecycle.AndroidViewModel  VibratorManager #androidx.lifecycle.AndroidViewModel  asStateFlow #androidx.lifecycle.AndroidViewModel  AppSettings androidx.lifecycle.ViewModel  Application androidx.lifecycle.ViewModel  AudioManager androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Build androidx.lifecycle.ViewModel  Context androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  GeneratorUiState androidx.lifecycle.ViewModel  HistoryUiState androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  List androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  PersistentQRRepository androidx.lifecycle.ViewModel  QRCodeCustomization androidx.lifecycle.ViewModel  
QRCodeData androidx.lifecycle.ViewModel  
QRCodeType androidx.lifecycle.ViewModel  ScannerUiState androidx.lifecycle.ViewModel  SettingsRepository androidx.lifecycle.ViewModel  SharingStarted androidx.lifecycle.ViewModel  SoundManager androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  Suppress androidx.lifecycle.ViewModel  
ToneGenerator androidx.lifecycle.ViewModel  Unit androidx.lifecycle.ViewModel  Vibrator androidx.lifecycle.ViewModel  VibratorManager androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  combine androidx.lifecycle.ViewModel  contains androidx.lifecycle.ViewModel  	emptyList androidx.lifecycle.ViewModel  filter androidx.lifecycle.ViewModel  invoke androidx.lifecycle.ViewModel  isBlank androidx.lifecycle.ViewModel  sortedByDescending androidx.lifecycle.ViewModel  stateIn androidx.lifecycle.ViewModel  viewModelScope androidx.lifecycle.ViewModel  LocalLifecycleOwner androidx.lifecycle.compose  	viewModel $androidx.lifecycle.viewmodel.compose  
NavController androidx.navigation  NavHostController androidx.navigation  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  Callback androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  	Migration androidx.room.RoomDatabase  	QRCodeDao androidx.room.RoomDatabase  QRSparkDatabase androidx.room.RoomDatabase  RoomDatabase androidx.room.RoomDatabase  SupportSQLiteDatabase androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  	qrCodeDao androidx.room.RoomDatabase  SupportSQLiteDatabase #androidx.room.RoomDatabase.Callback  	Migration androidx.room.migration  SupportSQLiteDatabase !androidx.room.migration.Migration  SupportSQLiteDatabase androidx.sqlite.db  MainActivity com.example.qr  
QRApplication com.example.qr  R com.example.qr  Bundle com.example.qr.MainActivity  Context com.example.qr.MainActivity  
Configuration com.example.qr.QRApplication  Context com.example.qr.QRApplication  string com.example.qr.R  nav_title_generator com.example.qr.R.string  nav_title_help com.example.qr.R.string  nav_title_history com.example.qr.R.string  nav_title_home com.example.qr.R.string  nav_title_scanner com.example.qr.R.string  nav_title_settings com.example.qr.R.string  Dao com.example.qr.data.dao  Delete com.example.qr.data.dao  Insert com.example.qr.data.dao  Int com.example.qr.data.dao  List com.example.qr.data.dao  Long com.example.qr.data.dao  OnConflictStrategy com.example.qr.data.dao  	QRCodeDao com.example.qr.data.dao  Query com.example.qr.data.dao  String com.example.qr.data.dao  Update com.example.qr.data.dao  Delete !com.example.qr.data.dao.QRCodeDao  Flow !com.example.qr.data.dao.QRCodeDao  Insert !com.example.qr.data.dao.QRCodeDao  Int !com.example.qr.data.dao.QRCodeDao  List !com.example.qr.data.dao.QRCodeDao  Long !com.example.qr.data.dao.QRCodeDao  OnConflictStrategy !com.example.qr.data.dao.QRCodeDao  QRCodeEntity !com.example.qr.data.dao.QRCodeDao  Query !com.example.qr.data.dao.QRCodeDao  String !com.example.qr.data.dao.QRCodeDao  Update !com.example.qr.data.dao.QRCodeDao  
getAllQRCodes !com.example.qr.data.dao.QRCodeDao  getFavoriteQRCodes !com.example.qr.data.dao.QRCodeDao  getGeneratedQRCodes !com.example.qr.data.dao.QRCodeDao  getScannedQRCodes !com.example.qr.data.dao.QRCodeDao  QRCodeConverters com.example.qr.data.database  QRCodeEntity com.example.qr.data.database  QRSparkDatabase com.example.qr.data.database  Volatile com.example.qr.data.database  Context ,com.example.qr.data.database.QRSparkDatabase  	Migration ,com.example.qr.data.database.QRSparkDatabase  	QRCodeDao ,com.example.qr.data.database.QRSparkDatabase  QRSparkDatabase ,com.example.qr.data.database.QRSparkDatabase  RoomDatabase ,com.example.qr.data.database.QRSparkDatabase  SupportSQLiteDatabase ,com.example.qr.data.database.QRSparkDatabase  Volatile ,com.example.qr.data.database.QRSparkDatabase  getDatabase ,com.example.qr.data.database.QRSparkDatabase  	qrCodeDao ,com.example.qr.data.database.QRSparkDatabase  Context 6com.example.qr.data.database.QRSparkDatabase.Companion  	Migration 6com.example.qr.data.database.QRSparkDatabase.Companion  	QRCodeDao 6com.example.qr.data.database.QRSparkDatabase.Companion  QRSparkDatabase 6com.example.qr.data.database.QRSparkDatabase.Companion  RoomDatabase 6com.example.qr.data.database.QRSparkDatabase.Companion  SupportSQLiteDatabase 6com.example.qr.data.database.QRSparkDatabase.Companion  Volatile 6com.example.qr.data.database.QRSparkDatabase.Companion  getDatabase 6com.example.qr.data.database.QRSparkDatabase.Companion  SupportSQLiteDatabase Gcom.example.qr.data.database.QRSparkDatabase.Companion.DatabaseCallback  Boolean com.example.qr.data.entity  Long com.example.qr.data.entity  QRCodeConverters com.example.qr.data.entity  QRCodeEntity com.example.qr.data.entity  String com.example.qr.data.entity  toQRCodeData com.example.qr.data.entity  toQRCodeEntity com.example.qr.data.entity  Date +com.example.qr.data.entity.QRCodeConverters  Long +com.example.qr.data.entity.QRCodeConverters  
QRCodeType +com.example.qr.data.entity.QRCodeConverters  String +com.example.qr.data.entity.QRCodeConverters  
TypeConverter +com.example.qr.data.entity.QRCodeConverters  Boolean 'com.example.qr.data.entity.QRCodeEntity  Date 'com.example.qr.data.entity.QRCodeEntity  Long 'com.example.qr.data.entity.QRCodeEntity  
PrimaryKey 'com.example.qr.data.entity.QRCodeEntity  
QRCodeType 'com.example.qr.data.entity.QRCodeEntity  String 'com.example.qr.data.entity.QRCodeEntity  getToQRCodeData 'com.example.qr.data.entity.QRCodeEntity  toQRCodeData 'com.example.qr.data.entity.QRCodeEntity  Boolean com.example.qr.data.model  Language com.example.qr.data.model  List com.example.qr.data.model  Long com.example.qr.data.model  
QRCodeData com.example.qr.data.model  
QRCodeType com.example.qr.data.model  String com.example.qr.data.model  Language "com.example.qr.data.model.Language  List "com.example.qr.data.model.Language  Locale "com.example.qr.data.model.Language  String "com.example.qr.data.model.Language  getSystemLanguage "com.example.qr.data.model.Language  Language ,com.example.qr.data.model.Language.Companion  List ,com.example.qr.data.model.Language.Companion  Locale ,com.example.qr.data.model.Language.Companion  String ,com.example.qr.data.model.Language.Companion  getSystemLanguage ,com.example.qr.data.model.Language.Companion  Boolean $com.example.qr.data.model.QRCodeData  Date $com.example.qr.data.model.QRCodeData  Long $com.example.qr.data.model.QRCodeData  
QRCodeType $com.example.qr.data.model.QRCodeData  String $com.example.qr.data.model.QRCodeData  content $com.example.qr.data.model.QRCodeData  	createdAt $com.example.qr.data.model.QRCodeData  displayName $com.example.qr.data.model.QRCodeData  
isFavorite $com.example.qr.data.model.QRCodeData  isGenerated $com.example.qr.data.model.QRCodeData  ANALYTICS_ENABLED com.example.qr.data.preferences  AUTO_OPEN_LINKS com.example.qr.data.preferences  AUTO_SAVE_GENERATED com.example.qr.data.preferences  AUTO_SAVE_SCANNED com.example.qr.data.preferences  AppSettings com.example.qr.data.preferences  Boolean com.example.qr.data.preferences  CONSENT_TIMESTAMP com.example.qr.data.preferences  CRASH_REPORTING_ENABLED com.example.qr.data.preferences  DEFAULT_QR_FORMAT com.example.qr.data.preferences  DEFAULT_QR_SIZE com.example.qr.data.preferences  ENABLE_ANIMATIONS com.example.qr.data.preferences  FIRST_TIME_LANGUAGE_SELECTION com.example.qr.data.preferences  FIRST_TIME_PRIVACY_SHOWN com.example.qr.data.preferences  Long com.example.qr.data.preferences  PLAY_SOUND_ON_SCAN com.example.qr.data.preferences  PRIVACY_POLICY_ACCEPTED com.example.qr.data.preferences  PRIVACY_POLICY_VERSION com.example.qr.data.preferences  SELECTED_LANGUAGE com.example.qr.data.preferences  SHOW_TUTORIALS com.example.qr.data.preferences  SettingsDataStore com.example.qr.data.preferences  String com.example.qr.data.preferences  
THEME_MODE com.example.qr.data.preferences  VIBRATE_ON_SCAN com.example.qr.data.preferences  booleanPreferencesKey com.example.qr.data.preferences  	dataStore com.example.qr.data.preferences  longPreferencesKey com.example.qr.data.preferences  map com.example.qr.data.preferences  provideDelegate com.example.qr.data.preferences  stringPreferencesKey com.example.qr.data.preferences  Boolean +com.example.qr.data.preferences.AppSettings  Long +com.example.qr.data.preferences.AppSettings  String +com.example.qr.data.preferences.AppSettings  ANALYTICS_ENABLED 1com.example.qr.data.preferences.SettingsDataStore  AUTO_OPEN_LINKS 1com.example.qr.data.preferences.SettingsDataStore  AUTO_SAVE_GENERATED 1com.example.qr.data.preferences.SettingsDataStore  AUTO_SAVE_SCANNED 1com.example.qr.data.preferences.SettingsDataStore  AppSettings 1com.example.qr.data.preferences.SettingsDataStore  Boolean 1com.example.qr.data.preferences.SettingsDataStore  CONSENT_TIMESTAMP 1com.example.qr.data.preferences.SettingsDataStore  CRASH_REPORTING_ENABLED 1com.example.qr.data.preferences.SettingsDataStore  Context 1com.example.qr.data.preferences.SettingsDataStore  DEFAULT_QR_FORMAT 1com.example.qr.data.preferences.SettingsDataStore  DEFAULT_QR_SIZE 1com.example.qr.data.preferences.SettingsDataStore  ENABLE_ANIMATIONS 1com.example.qr.data.preferences.SettingsDataStore  FIRST_TIME_LANGUAGE_SELECTION 1com.example.qr.data.preferences.SettingsDataStore  FIRST_TIME_PRIVACY_SHOWN 1com.example.qr.data.preferences.SettingsDataStore  Flow 1com.example.qr.data.preferences.SettingsDataStore  Long 1com.example.qr.data.preferences.SettingsDataStore  PLAY_SOUND_ON_SCAN 1com.example.qr.data.preferences.SettingsDataStore  PRIVACY_POLICY_ACCEPTED 1com.example.qr.data.preferences.SettingsDataStore  PRIVACY_POLICY_VERSION 1com.example.qr.data.preferences.SettingsDataStore  SELECTED_LANGUAGE 1com.example.qr.data.preferences.SettingsDataStore  SHOW_TUTORIALS 1com.example.qr.data.preferences.SettingsDataStore  String 1com.example.qr.data.preferences.SettingsDataStore  
THEME_MODE 1com.example.qr.data.preferences.SettingsDataStore  VIBRATE_ON_SCAN 1com.example.qr.data.preferences.SettingsDataStore  allSettings 1com.example.qr.data.preferences.SettingsDataStore  analyticsEnabled 1com.example.qr.data.preferences.SettingsDataStore  
autoOpenLinks 1com.example.qr.data.preferences.SettingsDataStore  autoSaveGenerated 1com.example.qr.data.preferences.SettingsDataStore  autoSaveScanned 1com.example.qr.data.preferences.SettingsDataStore  booleanPreferencesKey 1com.example.qr.data.preferences.SettingsDataStore  context 1com.example.qr.data.preferences.SettingsDataStore  crashReportingEnabled 1com.example.qr.data.preferences.SettingsDataStore  	dataStore 1com.example.qr.data.preferences.SettingsDataStore  defaultQRFormat 1com.example.qr.data.preferences.SettingsDataStore  
defaultQRSize 1com.example.qr.data.preferences.SettingsDataStore  enableAnimations 1com.example.qr.data.preferences.SettingsDataStore  getMAP 1com.example.qr.data.preferences.SettingsDataStore  getMap 1com.example.qr.data.preferences.SettingsDataStore  isFirstTimeLanguageSelection 1com.example.qr.data.preferences.SettingsDataStore  longPreferencesKey 1com.example.qr.data.preferences.SettingsDataStore  map 1com.example.qr.data.preferences.SettingsDataStore  playSoundOnScan 1com.example.qr.data.preferences.SettingsDataStore  selectedLanguage 1com.example.qr.data.preferences.SettingsDataStore  setAnalyticsEnabled 1com.example.qr.data.preferences.SettingsDataStore  setAutoOpenLinks 1com.example.qr.data.preferences.SettingsDataStore  setAutoSaveGenerated 1com.example.qr.data.preferences.SettingsDataStore  setAutoSaveScanned 1com.example.qr.data.preferences.SettingsDataStore  setCrashReportingEnabled 1com.example.qr.data.preferences.SettingsDataStore  setDefaultQRFormat 1com.example.qr.data.preferences.SettingsDataStore  setDefaultQRSize 1com.example.qr.data.preferences.SettingsDataStore  setEnableAnimations 1com.example.qr.data.preferences.SettingsDataStore  setFirstTimeLanguageSelection 1com.example.qr.data.preferences.SettingsDataStore  setPlaySoundOnScan 1com.example.qr.data.preferences.SettingsDataStore  setSelectedLanguage 1com.example.qr.data.preferences.SettingsDataStore  setShowTutorials 1com.example.qr.data.preferences.SettingsDataStore  setThemeMode 1com.example.qr.data.preferences.SettingsDataStore  setVibrateOnScan 1com.example.qr.data.preferences.SettingsDataStore  
showTutorials 1com.example.qr.data.preferences.SettingsDataStore  stringPreferencesKey 1com.example.qr.data.preferences.SettingsDataStore  	themeMode 1com.example.qr.data.preferences.SettingsDataStore  
vibrateOnScan 1com.example.qr.data.preferences.SettingsDataStore  ANALYTICS_ENABLED ;com.example.qr.data.preferences.SettingsDataStore.Companion  AUTO_OPEN_LINKS ;com.example.qr.data.preferences.SettingsDataStore.Companion  AUTO_SAVE_GENERATED ;com.example.qr.data.preferences.SettingsDataStore.Companion  AUTO_SAVE_SCANNED ;com.example.qr.data.preferences.SettingsDataStore.Companion  AppSettings ;com.example.qr.data.preferences.SettingsDataStore.Companion  Boolean ;com.example.qr.data.preferences.SettingsDataStore.Companion  CONSENT_TIMESTAMP ;com.example.qr.data.preferences.SettingsDataStore.Companion  CRASH_REPORTING_ENABLED ;com.example.qr.data.preferences.SettingsDataStore.Companion  Context ;com.example.qr.data.preferences.SettingsDataStore.Companion  DEFAULT_QR_FORMAT ;com.example.qr.data.preferences.SettingsDataStore.Companion  DEFAULT_QR_SIZE ;com.example.qr.data.preferences.SettingsDataStore.Companion  ENABLE_ANIMATIONS ;com.example.qr.data.preferences.SettingsDataStore.Companion  FIRST_TIME_LANGUAGE_SELECTION ;com.example.qr.data.preferences.SettingsDataStore.Companion  FIRST_TIME_PRIVACY_SHOWN ;com.example.qr.data.preferences.SettingsDataStore.Companion  Flow ;com.example.qr.data.preferences.SettingsDataStore.Companion  Long ;com.example.qr.data.preferences.SettingsDataStore.Companion  PLAY_SOUND_ON_SCAN ;com.example.qr.data.preferences.SettingsDataStore.Companion  PRIVACY_POLICY_ACCEPTED ;com.example.qr.data.preferences.SettingsDataStore.Companion  PRIVACY_POLICY_VERSION ;com.example.qr.data.preferences.SettingsDataStore.Companion  SELECTED_LANGUAGE ;com.example.qr.data.preferences.SettingsDataStore.Companion  SHOW_TUTORIALS ;com.example.qr.data.preferences.SettingsDataStore.Companion  String ;com.example.qr.data.preferences.SettingsDataStore.Companion  
THEME_MODE ;com.example.qr.data.preferences.SettingsDataStore.Companion  VIBRATE_ON_SCAN ;com.example.qr.data.preferences.SettingsDataStore.Companion  booleanPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  	dataStore ;com.example.qr.data.preferences.SettingsDataStore.Companion  getBOOLEANPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  getBooleanPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  getLONGPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  getLongPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  getMAP ;com.example.qr.data.preferences.SettingsDataStore.Companion  getMap ;com.example.qr.data.preferences.SettingsDataStore.Companion  getSTRINGPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  getStringPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  invoke ;com.example.qr.data.preferences.SettingsDataStore.Companion  longPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  map ;com.example.qr.data.preferences.SettingsDataStore.Companion  stringPreferencesKey ;com.example.qr.data.preferences.SettingsDataStore.Companion  Boolean com.example.qr.data.repository  InMemoryQRRepository com.example.qr.data.repository  Int com.example.qr.data.repository  List com.example.qr.data.repository  Long com.example.qr.data.repository  MutableStateFlow com.example.qr.data.repository  PersistentQRRepository com.example.qr.data.repository  QRSparkDatabase com.example.qr.data.repository  SettingsDataStore com.example.qr.data.repository  SettingsRepository com.example.qr.data.repository  String com.example.qr.data.repository  asStateFlow com.example.qr.data.repository  	emptyList com.example.qr.data.repository  getCurrentSettings com.example.qr.data.repository  getValue com.example.qr.data.repository  lazy com.example.qr.data.repository  map com.example.qr.data.repository  provideDelegate com.example.qr.data.repository  toQRCodeData com.example.qr.data.repository  List 3com.example.qr.data.repository.InMemoryQRRepository  Long 3com.example.qr.data.repository.InMemoryQRRepository  MutableStateFlow 3com.example.qr.data.repository.InMemoryQRRepository  
QRCodeData 3com.example.qr.data.repository.InMemoryQRRepository  	StateFlow 3com.example.qr.data.repository.InMemoryQRRepository  String 3com.example.qr.data.repository.InMemoryQRRepository  _qrCodes 3com.example.qr.data.repository.InMemoryQRRepository  asStateFlow 3com.example.qr.data.repository.InMemoryQRRepository  	emptyList 3com.example.qr.data.repository.InMemoryQRRepository  getASStateFlow 3com.example.qr.data.repository.InMemoryQRRepository  getAsStateFlow 3com.example.qr.data.repository.InMemoryQRRepository  getEMPTYList 3com.example.qr.data.repository.InMemoryQRRepository  getEmptyList 3com.example.qr.data.repository.InMemoryQRRepository  Context 5com.example.qr.data.repository.PersistentQRRepository  Flow 5com.example.qr.data.repository.PersistentQRRepository  Inject 5com.example.qr.data.repository.PersistentQRRepository  Int 5com.example.qr.data.repository.PersistentQRRepository  List 5com.example.qr.data.repository.PersistentQRRepository  Long 5com.example.qr.data.repository.PersistentQRRepository  	QRCodeDao 5com.example.qr.data.repository.PersistentQRRepository  
QRCodeData 5com.example.qr.data.repository.PersistentQRRepository  QRSparkDatabase 5com.example.qr.data.repository.PersistentQRRepository  String 5com.example.qr.data.repository.PersistentQRRepository  context 5com.example.qr.data.repository.PersistentQRRepository  database 5com.example.qr.data.repository.PersistentQRRepository  getGETValue 5com.example.qr.data.repository.PersistentQRRepository  getGetValue 5com.example.qr.data.repository.PersistentQRRepository  getLAZY 5com.example.qr.data.repository.PersistentQRRepository  getLazy 5com.example.qr.data.repository.PersistentQRRepository  getMAP 5com.example.qr.data.repository.PersistentQRRepository  getMap 5com.example.qr.data.repository.PersistentQRRepository  getPROVIDEDelegate 5com.example.qr.data.repository.PersistentQRRepository  getProvideDelegate 5com.example.qr.data.repository.PersistentQRRepository  getToQRCodeData 5com.example.qr.data.repository.PersistentQRRepository  getValue 5com.example.qr.data.repository.PersistentQRRepository  lazy 5com.example.qr.data.repository.PersistentQRRepository  map 5com.example.qr.data.repository.PersistentQRRepository  provideDelegate 5com.example.qr.data.repository.PersistentQRRepository  	qrCodeDao 5com.example.qr.data.repository.PersistentQRRepository  qrCodes 5com.example.qr.data.repository.PersistentQRRepository  toQRCodeData 5com.example.qr.data.repository.PersistentQRRepository  Context ?com.example.qr.data.repository.PersistentQRRepository.Companion  Flow ?com.example.qr.data.repository.PersistentQRRepository.Companion  Inject ?com.example.qr.data.repository.PersistentQRRepository.Companion  Int ?com.example.qr.data.repository.PersistentQRRepository.Companion  List ?com.example.qr.data.repository.PersistentQRRepository.Companion  Long ?com.example.qr.data.repository.PersistentQRRepository.Companion  	QRCodeDao ?com.example.qr.data.repository.PersistentQRRepository.Companion  
QRCodeData ?com.example.qr.data.repository.PersistentQRRepository.Companion  QRSparkDatabase ?com.example.qr.data.repository.PersistentQRRepository.Companion  String ?com.example.qr.data.repository.PersistentQRRepository.Companion  getGETValue ?com.example.qr.data.repository.PersistentQRRepository.Companion  getGetValue ?com.example.qr.data.repository.PersistentQRRepository.Companion  getLAZY ?com.example.qr.data.repository.PersistentQRRepository.Companion  getLazy ?com.example.qr.data.repository.PersistentQRRepository.Companion  getMAP ?com.example.qr.data.repository.PersistentQRRepository.Companion  getMap ?com.example.qr.data.repository.PersistentQRRepository.Companion  getPROVIDEDelegate ?com.example.qr.data.repository.PersistentQRRepository.Companion  getProvideDelegate ?com.example.qr.data.repository.PersistentQRRepository.Companion  getToQRCodeData ?com.example.qr.data.repository.PersistentQRRepository.Companion  getValue ?com.example.qr.data.repository.PersistentQRRepository.Companion  invoke ?com.example.qr.data.repository.PersistentQRRepository.Companion  lazy ?com.example.qr.data.repository.PersistentQRRepository.Companion  map ?com.example.qr.data.repository.PersistentQRRepository.Companion  provideDelegate ?com.example.qr.data.repository.PersistentQRRepository.Companion  toQRCodeData ?com.example.qr.data.repository.PersistentQRRepository.Companion  AppSettings 1com.example.qr.data.repository.SettingsRepository  Boolean 1com.example.qr.data.repository.SettingsRepository  Context 1com.example.qr.data.repository.SettingsRepository  Flow 1com.example.qr.data.repository.SettingsRepository  Inject 1com.example.qr.data.repository.SettingsRepository  Int 1com.example.qr.data.repository.SettingsRepository  SettingsDataStore 1com.example.qr.data.repository.SettingsRepository  String 1com.example.qr.data.repository.SettingsRepository  context 1com.example.qr.data.repository.SettingsRepository  invoke 1com.example.qr.data.repository.SettingsRepository  settingsDataStore 1com.example.qr.data.repository.SettingsRepository  BarcodeScanning "com.example.qr.presentation.camera  Boolean "com.example.qr.presentation.camera  Camera "com.example.qr.presentation.camera  
CameraManager "com.example.qr.presentation.camera  
CameraPreview "com.example.qr.presentation.camera  
Composable "com.example.qr.presentation.camera  ExperimentalGetImage "com.example.qr.presentation.camera  Float "com.example.qr.presentation.camera  
ImageAnalysis "com.example.qr.presentation.camera  
ImageProxy "com.example.qr.presentation.camera  Pair "com.example.qr.presentation.camera  QRCodeAnalyzer "com.example.qr.presentation.camera  ScanningOverlay "com.example.qr.presentation.camera  String "com.example.qr.presentation.camera  Unit "com.example.qr.presentation.camera  Volatile "com.example.qr.presentation.camera  Boolean 0com.example.qr.presentation.camera.CameraManager  Camera 0com.example.qr.presentation.camera.CameraManager  
CameraManager 0com.example.qr.presentation.camera.CameraManager  Context 0com.example.qr.presentation.camera.CameraManager  ExecutorService 0com.example.qr.presentation.camera.CameraManager  Float 0com.example.qr.presentation.camera.CameraManager  LifecycleOwner 0com.example.qr.presentation.camera.CameraManager  Pair 0com.example.qr.presentation.camera.CameraManager  PreviewView 0com.example.qr.presentation.camera.CameraManager  ProcessCameraProvider 0com.example.qr.presentation.camera.CameraManager  QRCodeAnalyzer 0com.example.qr.presentation.camera.CameraManager  String 0com.example.qr.presentation.camera.CameraManager  Unit 0com.example.qr.presentation.camera.CameraManager  Volatile 0com.example.qr.presentation.camera.CameraManager  Boolean :com.example.qr.presentation.camera.CameraManager.Companion  Camera :com.example.qr.presentation.camera.CameraManager.Companion  
CameraManager :com.example.qr.presentation.camera.CameraManager.Companion  Context :com.example.qr.presentation.camera.CameraManager.Companion  ExecutorService :com.example.qr.presentation.camera.CameraManager.Companion  Float :com.example.qr.presentation.camera.CameraManager.Companion  LifecycleOwner :com.example.qr.presentation.camera.CameraManager.Companion  Pair :com.example.qr.presentation.camera.CameraManager.Companion  PreviewView :com.example.qr.presentation.camera.CameraManager.Companion  ProcessCameraProvider :com.example.qr.presentation.camera.CameraManager.Companion  QRCodeAnalyzer :com.example.qr.presentation.camera.CameraManager.Companion  String :com.example.qr.presentation.camera.CameraManager.Companion  Unit :com.example.qr.presentation.camera.CameraManager.Companion  Volatile :com.example.qr.presentation.camera.CameraManager.Companion  BarcodeScanning 1com.example.qr.presentation.camera.QRCodeAnalyzer  ExperimentalGetImage 1com.example.qr.presentation.camera.QRCodeAnalyzer  
ImageProxy 1com.example.qr.presentation.camera.QRCodeAnalyzer  OptIn 1com.example.qr.presentation.camera.QRCodeAnalyzer  String 1com.example.qr.presentation.camera.QRCodeAnalyzer  Unit 1com.example.qr.presentation.camera.QRCodeAnalyzer  BarcodeScanning +com.example.qr.presentation.camera.analyzer  ExperimentalGetImage +com.example.qr.presentation.camera.analyzer  QRCodeAnalyzer +com.example.qr.presentation.camera.analyzer  String +com.example.qr.presentation.camera.analyzer  Unit +com.example.qr.presentation.camera.analyzer  BarcodeScanning :com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer  ExperimentalGetImage :com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer  
ImageProxy :com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer  OptIn :com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer  String :com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer  Unit :com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer  Int &com.example.qr.presentation.navigation  QRNavigation &com.example.qr.presentation.navigation  R &com.example.qr.presentation.navigation  Screen &com.example.qr.presentation.navigation  String &com.example.qr.presentation.navigation  Int -com.example.qr.presentation.navigation.Screen  R -com.example.qr.presentation.navigation.Screen  Screen -com.example.qr.presentation.navigation.Screen  String -com.example.qr.presentation.navigation.Screen  R 7com.example.qr.presentation.navigation.Screen.Generator  R 2com.example.qr.presentation.navigation.Screen.Help  R 5com.example.qr.presentation.navigation.Screen.History  R 2com.example.qr.presentation.navigation.Screen.Home  R 5com.example.qr.presentation.navigation.Screen.Scanner  R 6com.example.qr.presentation.navigation.Screen.Settings  Any -com.example.qr.presentation.screens.generator  Boolean -com.example.qr.presentation.screens.generator  
Composable -com.example.qr.presentation.screens.generator  
ContactQRForm -com.example.qr.presentation.screens.generator  Double -com.example.qr.presentation.screens.generator  EmailQRForm -com.example.qr.presentation.screens.generator  ExperimentalMaterial3Api -com.example.qr.presentation.screens.generator  GeneratorUiState -com.example.qr.presentation.screens.generator  List -com.example.qr.presentation.screens.generator  LocationQRForm -com.example.qr.presentation.screens.generator  Map -com.example.qr.presentation.screens.generator  MutableStateFlow -com.example.qr.presentation.screens.generator  OptIn -com.example.qr.presentation.screens.generator  PersistentQRRepository -com.example.qr.presentation.screens.generator  PhoneQRForm -com.example.qr.presentation.screens.generator  QRGeneratorForm -com.example.qr.presentation.screens.generator  QRGeneratorScreen -com.example.qr.presentation.screens.generator  QRGeneratorScreenContent -com.example.qr.presentation.screens.generator  
QRTypeInfo -com.example.qr.presentation.screens.generator  QRTypeSelectionScreen -com.example.qr.presentation.screens.generator  	SMSQRForm -com.example.qr.presentation.screens.generator  SettingsRepository -com.example.qr.presentation.screens.generator  SimpleGeneratorViewModel -com.example.qr.presentation.screens.generator  SparkQRTypeCard -com.example.qr.presentation.screens.generator  SparkTextQRForm -com.example.qr.presentation.screens.generator  String -com.example.qr.presentation.screens.generator  	URLQRForm -com.example.qr.presentation.screens.generator  Unit -com.example.qr.presentation.screens.generator  
WiFiQRForm -com.example.qr.presentation.screens.generator  asStateFlow -com.example.qr.presentation.screens.generator  getQRCodeTypes -com.example.qr.presentation.screens.generator  getQRCodeTypesStatic -com.example.qr.presentation.screens.generator  getQRTypeColor -com.example.qr.presentation.screens.generator  
getQRTypeInfo -com.example.qr.presentation.screens.generator  getQRTypeInfoStatic -com.example.qr.presentation.screens.generator  handleGeneration -com.example.qr.presentation.screens.generator  Bitmap >com.example.qr.presentation.screens.generator.GeneratorUiState  Boolean >com.example.qr.presentation.screens.generator.GeneratorUiState  
QRCodeData >com.example.qr.presentation.screens.generator.GeneratorUiState  String >com.example.qr.presentation.screens.generator.GeneratorUiState  ImageVector 8com.example.qr.presentation.screens.generator.QRTypeInfo  
QRCodeType 8com.example.qr.presentation.screens.generator.QRTypeInfo  String 8com.example.qr.presentation.screens.generator.QRTypeInfo  Boolean Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  Context Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  Double Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  GeneratorUiState Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  MutableStateFlow Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  PersistentQRRepository Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  QRCodeCustomization Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  
QRCodeData Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  
QRCodeType Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  SettingsRepository Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  	StateFlow Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  String Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  _uiState Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  asStateFlow Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  context Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  getASStateFlow Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  getAsStateFlow Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  invoke Fcom.example.qr.presentation.screens.generator.SimpleGeneratorViewModel  
Composable (com.example.qr.presentation.screens.help  ExperimentalMaterial3Api (com.example.qr.presentation.screens.help  FAQCard (com.example.qr.presentation.screens.help  FAQItem (com.example.qr.presentation.screens.help  
HelpScreen (com.example.qr.presentation.screens.help  OptIn (com.example.qr.presentation.screens.help  String (com.example.qr.presentation.screens.help  Color 0com.example.qr.presentation.screens.help.FAQItem  ImageVector 0com.example.qr.presentation.screens.help.FAQItem  String 0com.example.qr.presentation.screens.help.FAQItem  Boolean +com.example.qr.presentation.screens.history  
Composable +com.example.qr.presentation.screens.history  DeleteConfirmationDialog +com.example.qr.presentation.screens.history  ExperimentalFoundationApi +com.example.qr.presentation.screens.history  ExperimentalMaterial3Api +com.example.qr.presentation.screens.history  
HistoryScreen +com.example.qr.presentation.screens.history  HistoryUiState +com.example.qr.presentation.screens.history  Int +com.example.qr.presentation.screens.history  List +com.example.qr.presentation.screens.history  MutableStateFlow +com.example.qr.presentation.screens.history  OptIn +com.example.qr.presentation.screens.history  PersistentQRRepository +com.example.qr.presentation.screens.history  SharingStarted +com.example.qr.presentation.screens.history  SimpleHistoryViewModel +com.example.qr.presentation.screens.history  SparkQRCodeItem +com.example.qr.presentation.screens.history  SparkQRCodeList +com.example.qr.presentation.screens.history  SparkTabRow +com.example.qr.presentation.screens.history  SparkTopAppBar +com.example.qr.presentation.screens.history  	StateFlow +com.example.qr.presentation.screens.history  String +com.example.qr.presentation.screens.history  Unit +com.example.qr.presentation.screens.history  asStateFlow +com.example.qr.presentation.screens.history  combine +com.example.qr.presentation.screens.history  contains +com.example.qr.presentation.screens.history  	emptyList +com.example.qr.presentation.screens.history  filter +com.example.qr.presentation.screens.history  getTypeColor +com.example.qr.presentation.screens.history  getTypeIcon +com.example.qr.presentation.screens.history  isBlank +com.example.qr.presentation.screens.history  sortedByDescending +com.example.qr.presentation.screens.history  stateIn +com.example.qr.presentation.screens.history  viewModelScope +com.example.qr.presentation.screens.history  Boolean :com.example.qr.presentation.screens.history.HistoryUiState  String :com.example.qr.presentation.screens.history.HistoryUiState  Context Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  HistoryUiState Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  Int Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  List Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  MutableStateFlow Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  PersistentQRRepository Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
QRCodeData Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  SharingStarted Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  	StateFlow Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  String Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  _searchQuery Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  _selectedTab Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  _uiState Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  asStateFlow Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  combine Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  contains Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  context Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  	emptyList Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  filter Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getASStateFlow Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getAsStateFlow Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
getCOMBINE Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getCONTAINS Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
getCombine Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getContains Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getEMPTYList Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getEmptyList Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  	getFILTER Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  	getFilter Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
getISBlank Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
getIsBlank Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getSORTEDByDescending Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
getSTATEIn Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getSortedByDescending Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
getStateIn Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getVIEWModelScope Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  getViewModelScope Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  invoke Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  isBlank Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  qrCodes Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
repository Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  sortedByDescending Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  stateIn Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  viewModelScope Bcom.example.qr.presentation.screens.history.SimpleHistoryViewModel  
ActionCard (com.example.qr.presentation.screens.home  
Composable (com.example.qr.presentation.screens.home  ExperimentalMaterial3Api (com.example.qr.presentation.screens.home  
HomeScreen (com.example.qr.presentation.screens.home  OptIn (com.example.qr.presentation.screens.home  String (com.example.qr.presentation.screens.home  Unit (com.example.qr.presentation.screens.home  androidx (com.example.qr.presentation.screens.home  AnimatedActionButton +com.example.qr.presentation.screens.scanner  Boolean +com.example.qr.presentation.screens.scanner  Build +com.example.qr.presentation.screens.scanner  
Composable +com.example.qr.presentation.screens.scanner  Context +com.example.qr.presentation.screens.scanner  EnhancedCopyButton +com.example.qr.presentation.screens.scanner  EnhancedZoomCameraPreview +com.example.qr.presentation.screens.scanner  ExperimentalComposeUiApi +com.example.qr.presentation.screens.scanner  ExperimentalMaterial3Api +com.example.qr.presentation.screens.scanner  ExperimentalPermissionsApi +com.example.qr.presentation.screens.scanner  Float +com.example.qr.presentation.screens.scanner  InstructionalTextCard +com.example.qr.presentation.screens.scanner  MinimalistScanHeader +com.example.qr.presentation.screens.scanner  MinimalistScanningFrame +com.example.qr.presentation.screens.scanner  MinimalistUploadButton +com.example.qr.presentation.screens.scanner  MinimalistZoomSlider +com.example.qr.presentation.screens.scanner  MutableStateFlow +com.example.qr.presentation.screens.scanner  OptIn +com.example.qr.presentation.screens.scanner  PermissionDeniedContent +com.example.qr.presentation.screens.scanner  PermissionRationaleContent +com.example.qr.presentation.screens.scanner  PersistentQRRepository +com.example.qr.presentation.screens.scanner  QRCodeResultDialog +com.example.qr.presentation.screens.scanner  QRCodeResultDialogContent +com.example.qr.presentation.screens.scanner  QRScannerScreen +com.example.qr.presentation.screens.scanner  QRScannerScreenContent +com.example.qr.presentation.screens.scanner  ScannerUiState +com.example.qr.presentation.screens.scanner  
ScanningFrame +com.example.qr.presentation.screens.scanner  SettingsRepository +com.example.qr.presentation.screens.scanner  SimpleScannerViewModel +com.example.qr.presentation.screens.scanner  SmartScanControlIcon +com.example.qr.presentation.screens.scanner  SmartScanHeader +com.example.qr.presentation.screens.scanner  SmartScanInterface +com.example.qr.presentation.screens.scanner  SoundManager +com.example.qr.presentation.screens.scanner  String +com.example.qr.presentation.screens.scanner  Suppress +com.example.qr.presentation.screens.scanner  Unit +com.example.qr.presentation.screens.scanner  UploadQRButton +com.example.qr.presentation.screens.scanner  ZoomControlButton +com.example.qr.presentation.screens.scanner  ZoomControlsBar +com.example.qr.presentation.screens.scanner  android +com.example.qr.presentation.screens.scanner  androidx +com.example.qr.presentation.screens.scanner  asStateFlow +com.example.qr.presentation.screens.scanner  com +com.example.qr.presentation.screens.scanner  processImageFromGallery +com.example.qr.presentation.screens.scanner  Boolean :com.example.qr.presentation.screens.scanner.ScannerUiState  
QRCodeData :com.example.qr.presentation.screens.scanner.ScannerUiState  String :com.example.qr.presentation.screens.scanner.ScannerUiState  Build Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  Context Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  MutableStateFlow Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  PersistentQRRepository Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  
QRCodeData Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  ScannerUiState Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  SettingsRepository Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  SoundManager Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  	StateFlow Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  String Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  Suppress Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  Vibrator Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  VibratorManager Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  _uiState Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  asStateFlow Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  context Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  getASStateFlow Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  getAsStateFlow Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  invoke Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  settingsRepository Bcom.example.qr.presentation.screens.scanner.SimpleScannerViewModel  AppSettings ,com.example.qr.presentation.screens.settings  Boolean ,com.example.qr.presentation.screens.settings  Build ,com.example.qr.presentation.screens.settings  
Composable ,com.example.qr.presentation.screens.settings  Context ,com.example.qr.presentation.screens.settings  ExperimentalMaterial3Api ,com.example.qr.presentation.screens.settings  FormatSelectionDialog ,com.example.qr.presentation.screens.settings  InfoTooltipDialog ,com.example.qr.presentation.screens.settings  List ,com.example.qr.presentation.screens.settings  MutableStateFlow ,com.example.qr.presentation.screens.settings  OptIn ,com.example.qr.presentation.screens.settings  ResetSettingsDialog ,com.example.qr.presentation.screens.settings  SettingsItem ,com.example.qr.presentation.screens.settings  SettingsRepository ,com.example.qr.presentation.screens.settings  SettingsScreen ,com.example.qr.presentation.screens.settings  SettingsScreenContent ,com.example.qr.presentation.screens.settings  SettingsSection ,com.example.qr.presentation.screens.settings  SettingsViewModel ,com.example.qr.presentation.screens.settings  SizeSelectionDialog ,com.example.qr.presentation.screens.settings  String ,com.example.qr.presentation.screens.settings  Suppress ,com.example.qr.presentation.screens.settings  ThemeSelectionDialog ,com.example.qr.presentation.screens.settings  Unit ,com.example.qr.presentation.screens.settings  asStateFlow ,com.example.qr.presentation.screens.settings  AppSettings >com.example.qr.presentation.screens.settings.SettingsViewModel  Application >com.example.qr.presentation.screens.settings.SettingsViewModel  AudioManager >com.example.qr.presentation.screens.settings.SettingsViewModel  Boolean >com.example.qr.presentation.screens.settings.SettingsViewModel  Build >com.example.qr.presentation.screens.settings.SettingsViewModel  Context >com.example.qr.presentation.screens.settings.SettingsViewModel  List >com.example.qr.presentation.screens.settings.SettingsViewModel  MutableStateFlow >com.example.qr.presentation.screens.settings.SettingsViewModel  SettingsRepository >com.example.qr.presentation.screens.settings.SettingsViewModel  	StateFlow >com.example.qr.presentation.screens.settings.SettingsViewModel  String >com.example.qr.presentation.screens.settings.SettingsViewModel  Suppress >com.example.qr.presentation.screens.settings.SettingsViewModel  
ToneGenerator >com.example.qr.presentation.screens.settings.SettingsViewModel  Unit >com.example.qr.presentation.screens.settings.SettingsViewModel  Vibrator >com.example.qr.presentation.screens.settings.SettingsViewModel  VibratorManager >com.example.qr.presentation.screens.settings.SettingsViewModel  
_isLoading >com.example.qr.presentation.screens.settings.SettingsViewModel  	_settings >com.example.qr.presentation.screens.settings.SettingsViewModel  asStateFlow >com.example.qr.presentation.screens.settings.SettingsViewModel  getASStateFlow >com.example.qr.presentation.screens.settings.SettingsViewModel  getAsStateFlow >com.example.qr.presentation.screens.settings.SettingsViewModel  AnimatedActionButton com.example.qr.ui.components  AnimatedSaveButton com.example.qr.ui.components  Any com.example.qr.ui.components  Boolean com.example.qr.ui.components  ButtonDimensionTestPreview com.example.qr.ui.components  
CardColors com.example.qr.ui.components  
CardElevation com.example.qr.ui.components  ColumnScope com.example.qr.ui.components  CompleteTutorialDialogPreview com.example.qr.ui.components  
Composable com.example.qr.ui.components  DeleteConfirmationDialog com.example.qr.ui.components  EnhancedShareButton com.example.qr.ui.components  EnhancedToggleSwitch com.example.qr.ui.components  EnhancedTutorialButton com.example.qr.ui.components  EnhancedTutorialProgressBar com.example.qr.ui.components  ExperimentalMaterial3Api com.example.qr.ui.components   FirstTimeLanguageSelectionDialog com.example.qr.ui.components  FirstTimePrivacyButtons com.example.qr.ui.components  GenerateToggleSwitch com.example.qr.ui.components  GradientBackground com.example.qr.ui.components  
InfoDialog com.example.qr.ui.components  InfoToggleSwitch com.example.qr.ui.components  Int com.example.qr.ui.components  LanguageItem com.example.qr.ui.components  LanguageSelectionDialog com.example.qr.ui.components  List com.example.qr.ui.components  LocalizedContent com.example.qr.ui.components  LocalizedString com.example.qr.ui.components  "LocalizedTutorialNavigationButtons com.example.qr.ui.components  OptIn com.example.qr.ui.components  PrivacyPolicyContent com.example.qr.ui.components  PrivacyPolicyDialog com.example.qr.ui.components  PrivacySection com.example.qr.ui.components  QRActionButton com.example.qr.ui.components  QRDetailActionButtons com.example.qr.ui.components  QRDetailBottomSheet com.example.qr.ui.components  QRResultBottomSheet com.example.qr.ui.components  QRResultBottomSheetContent com.example.qr.ui.components  ReactiveLanguageContent com.example.qr.ui.components  ReactiveTutorialContent com.example.qr.ui.components  ReactiveTutorialData com.example.qr.ui.components  ScanToggleSwitch com.example.qr.ui.components  SettingInfo com.example.qr.ui.components  SettingInfoType com.example.qr.ui.components  SettingsInfoDialog com.example.qr.ui.components  SparkActionCard com.example.qr.ui.components  	SparkCard com.example.qr.ui.components  SparkGradientText com.example.qr.ui.components  StandardTutorialButton com.example.qr.ui.components  String com.example.qr.ui.components  SuccessToggleSwitch com.example.qr.ui.components  TestCard com.example.qr.ui.components  TutorialButtonColors com.example.qr.ui.components  TutorialButtonVariant com.example.qr.ui.components  TutorialNavigationButtons com.example.qr.ui.components   TutorialNavigationButtonsPreview com.example.qr.ui.components  TutorialOverlay com.example.qr.ui.components  TutorialOverlayContent com.example.qr.ui.components  TutorialPrimaryButton com.example.qr.ui.components  TutorialSecondaryButton com.example.qr.ui.components  TutorialSuccessButton com.example.qr.ui.components  TutorialTooltip com.example.qr.ui.components  TutorialTrigger com.example.qr.ui.components  Unit com.example.qr.ui.components  WarningToggleSwitch com.example.qr.ui.components  androidx com.example.qr.ui.components  copyToClipboard com.example.qr.ui.components  getLocalizedTutorialSteps com.example.qr.ui.components  getReactiveTutorialData com.example.qr.ui.components  getSettingInfo com.example.qr.ui.components  reactiveStringResource com.example.qr.ui.components  rememberCurrentLanguage com.example.qr.ui.components  rememberLanguageChanger com.example.qr.ui.components  saveQRCodeToDevice com.example.qr.ui.components  shareQRCode com.example.qr.ui.components  String 1com.example.qr.ui.components.ReactiveTutorialData  Color (com.example.qr.ui.components.SettingInfo  ImageVector (com.example.qr.ui.components.SettingInfo  String (com.example.qr.ui.components.SettingInfo  Color 1com.example.qr.ui.components.TutorialButtonColors  BottomActionRow com.example.qr.ui.screens  
Composable com.example.qr.ui.screens  ExperimentalMaterial3Api com.example.qr.ui.screens  LabeledActionButton com.example.qr.ui.screens  MainActionCards com.example.qr.ui.screens  OptIn com.example.qr.ui.screens  SparkBrandingSection com.example.qr.ui.screens  SparkHomeScreen com.example.qr.ui.screens  String com.example.qr.ui.screens  Unit com.example.qr.ui.screens  Boolean com.example.qr.ui.theme  BrandGradientEnd com.example.qr.ui.theme  BrandGradientMiddle com.example.qr.ui.theme  BrandGradientStart com.example.qr.ui.theme  CollectionColor com.example.qr.ui.theme  
Composable com.example.qr.ui.theme  DarkBackground com.example.qr.ui.theme  DarkColorScheme com.example.qr.ui.theme  
DarkOnSurface com.example.qr.ui.theme  DarkOnSurfaceVariant com.example.qr.ui.theme  DarkOutline com.example.qr.ui.theme  DarkSurface com.example.qr.ui.theme  DarkSurfaceVariant com.example.qr.ui.theme  ErrorRed com.example.qr.ui.theme  ExperimentalComposeUiApi com.example.qr.ui.theme  ExperimentalFoundationApi com.example.qr.ui.theme  ExperimentalMaterial3Api com.example.qr.ui.theme  ExperimentalPermissionsApi com.example.qr.ui.theme  FallbackFontFamily com.example.qr.ui.theme  
FontPreloader com.example.qr.ui.theme  	FontUtils com.example.qr.ui.theme  
FontWeight com.example.qr.ui.theme  
GenerateColor com.example.qr.ui.theme  InfoBlue com.example.qr.ui.theme  LightBackground com.example.qr.ui.theme  LightColorScheme com.example.qr.ui.theme  LightOnSurface com.example.qr.ui.theme  LightOnSurfaceVariant com.example.qr.ui.theme  LightOutline com.example.qr.ui.theme  LightSurface com.example.qr.ui.theme  LightSurfaceVariant com.example.qr.ui.theme  Pink40 com.example.qr.ui.theme  Pink80 com.example.qr.ui.theme  	PureBlack com.example.qr.ui.theme  	PureWhite com.example.qr.ui.theme  Purple40 com.example.qr.ui.theme  Purple80 com.example.qr.ui.theme  PurpleGrey40 com.example.qr.ui.theme  PurpleGrey80 com.example.qr.ui.theme  QRSparkTextStyles com.example.qr.ui.theme  QRSparkTextStylesData com.example.qr.ui.theme  QRSparkTypography com.example.qr.ui.theme  QRTheme com.example.qr.ui.theme  QRThemeWithSettings com.example.qr.ui.theme  SafeFontFamily com.example.qr.ui.theme  	ScanColor com.example.qr.ui.theme  	SparkBlue com.example.qr.ui.theme  
SparkGreen com.example.qr.ui.theme  	SparkPink com.example.qr.ui.theme  SparkRed com.example.qr.ui.theme  String com.example.qr.ui.theme  SuccessGreen com.example.qr.ui.theme  	TextStyle com.example.qr.ui.theme  
Typography com.example.qr.ui.theme  Unit com.example.qr.ui.theme  
WarningOrange com.example.qr.ui.theme  android com.example.qr.ui.theme  androidx com.example.qr.ui.theme  com com.example.qr.ui.theme  createFontFamilyWithFallback com.example.qr.ui.theme  rememberOptimalFontFamily com.example.qr.ui.theme  rememberQRSparkTextStyles com.example.qr.ui.theme  rememberQRSparkTypography com.example.qr.ui.theme  rememberSafePoppinsFontFamily com.example.qr.ui.theme  Boolean %com.example.qr.ui.theme.FontPreloader  Boolean !com.example.qr.ui.theme.FontUtils  
Composable !com.example.qr.ui.theme.FontUtils  
FontFamily !com.example.qr.ui.theme.FontUtils  FontLoadingStrategy !com.example.qr.ui.theme.FontUtils  
FontWeight !com.example.qr.ui.theme.FontUtils  
FontWeight )com.example.qr.ui.theme.QRSparkTextStyles  SafeFontFamily )com.example.qr.ui.theme.QRSparkTextStyles  	TextStyle )com.example.qr.ui.theme.QRSparkTextStyles  invoke )com.example.qr.ui.theme.QRSparkTextStyles  sp )com.example.qr.ui.theme.QRSparkTextStyles  	TextStyle -com.example.qr.ui.theme.QRSparkTextStylesData  AnimationManager com.example.qr.utils  
AnimationSpec com.example.qr.utils  Array com.example.qr.utils  	AudioType com.example.qr.utils  BatchQRGenerationResult com.example.qr.utils  Boolean com.example.qr.utils  Build com.example.qr.utils  
Composable com.example.qr.utils  Context com.example.qr.utils  Double com.example.qr.utils  DurationBasedAnimationSpec com.example.qr.utils  Easing com.example.qr.utils  FeedbackManager com.example.qr.utils  Float com.example.qr.utils  
HapticType com.example.qr.utils  InfiniteRepeatableSpec com.example.qr.utils  Int com.example.qr.utils  Language com.example.qr.utils  LanguageManager com.example.qr.utils  List com.example.qr.utils  Long com.example.qr.utils  Map com.example.qr.utils  MutableStateFlow com.example.qr.utils  PDFGenerator com.example.qr.utils  	PDFResult com.example.qr.utils  PREF_GENERATOR_TUTORIAL_SHOWN com.example.qr.utils  PREF_HISTORY_TUTORIAL_SHOWN com.example.qr.utils  PREF_HOME_TUTORIAL_SHOWN com.example.qr.utils  PREF_SCANNER_TUTORIAL_SHOWN com.example.qr.utils  PREF_SETTINGS_TUTORIAL_SHOWN com.example.qr.utils  
PaddingValues com.example.qr.utils  Pair com.example.qr.utils  Pattern com.example.qr.utils  PermissionManager com.example.qr.utils  PrivacyComplianceSummary com.example.qr.utils  PrivacyManager com.example.qr.utils  
PrivacyStatus com.example.qr.utils  QRCodeAnalysisResult com.example.qr.utils  QRCodeAnalyzer com.example.qr.utils  QRCodeCustomization com.example.qr.utils  QRCodeGenerator com.example.qr.utils  QRCodeLocalization com.example.qr.utils  QRFormatTestHelper com.example.qr.utils  QRGenerationResult com.example.qr.utils  QRGenerationService com.example.qr.utils  QRSizeTestHelper com.example.qr.utils  
RepeatMode com.example.qr.utils  
SafeAreaUtils com.example.qr.utils  SettingsDataStore com.example.qr.utils  SettingsTestHelper com.example.qr.utils  
ShareUtils com.example.qr.utils  SoundManager com.example.qr.utils  
SpringSpec com.example.qr.utils  State com.example.qr.utils  String com.example.qr.utils  Suppress com.example.qr.utils  TutorialContent com.example.qr.utils  TutorialManager com.example.qr.utils  TutorialStep com.example.qr.utils  	TweenSpec com.example.qr.utils  Unit com.example.qr.utils  WiFiSecurity com.example.qr.utils  WindowInsets com.example.qr.utils  android com.example.qr.utils  androidx com.example.qr.utils  asStateFlow com.example.qr.utils  dialogSafeArea com.example.qr.utils  extractCalendarEvent com.example.qr.utils  extractContactName com.example.qr.utils  
extractDomain com.example.qr.utils  extractLocationName com.example.qr.utils  extractSMSNumber com.example.qr.utils  extractWiFiSSID com.example.qr.utils  getLocalizedQRDisplayName com.example.qr.utils  getLocalizedQRStatus com.example.qr.utils  getLocalizedQRTypeName com.example.qr.utils  mapOf com.example.qr.utils  provideFeedbackIfEnabled com.example.qr.utils  safeDrawing com.example.qr.utils  screenSafeArea com.example.qr.utils  scrollableSafeArea com.example.qr.utils  
statusBars com.example.qr.utils  to com.example.qr.utils  
AnimationSpec %com.example.qr.utils.AnimationManager  Boolean %com.example.qr.utils.AnimationManager  
Composable %com.example.qr.utils.AnimationManager  DurationBasedAnimationSpec %com.example.qr.utils.AnimationManager  Easing %com.example.qr.utils.AnimationManager  Float %com.example.qr.utils.AnimationManager  InfiniteRepeatableSpec %com.example.qr.utils.AnimationManager  Int %com.example.qr.utils.AnimationManager  
RepeatMode %com.example.qr.utils.AnimationManager  SettingsRepository %com.example.qr.utils.AnimationManager  
SpringSpec %com.example.qr.utils.AnimationManager  State %com.example.qr.utils.AnimationManager  String %com.example.qr.utils.AnimationManager  	TweenSpec %com.example.qr.utils.AnimationManager  Unit %com.example.qr.utils.AnimationManager  androidx %com.example.qr.utils.AnimationManager  Int ,com.example.qr.utils.BatchQRGenerationResult  List ,com.example.qr.utils.BatchQRGenerationResult  QRGenerationResult ,com.example.qr.utils.BatchQRGenerationResult  String ,com.example.qr.utils.BatchQRGenerationResult  	AudioType $com.example.qr.utils.FeedbackManager  Build $com.example.qr.utils.FeedbackManager  Context $com.example.qr.utils.FeedbackManager  
HapticType $com.example.qr.utils.FeedbackManager  Inject $com.example.qr.utils.FeedbackManager  SettingsRepository $com.example.qr.utils.FeedbackManager  SoundManager $com.example.qr.utils.FeedbackManager  Suppress $com.example.qr.utils.FeedbackManager  
ToneGenerator $com.example.qr.utils.FeedbackManager  Vibrator $com.example.qr.utils.FeedbackManager  VibratorManager $com.example.qr.utils.FeedbackManager  context $com.example.qr.utils.FeedbackManager  Activity $com.example.qr.utils.LanguageManager  Boolean $com.example.qr.utils.LanguageManager  Context $com.example.qr.utils.LanguageManager  Language $com.example.qr.utils.LanguageManager  MutableStateFlow $com.example.qr.utils.LanguageManager  	StateFlow $com.example.qr.utils.LanguageManager  String $com.example.qr.utils.LanguageManager  _currentLanguage $com.example.qr.utils.LanguageManager  asStateFlow $com.example.qr.utils.LanguageManager  getASStateFlow $com.example.qr.utils.LanguageManager  getAsStateFlow $com.example.qr.utils.LanguageManager  Bitmap !com.example.qr.utils.PDFGenerator  Canvas !com.example.qr.utils.PDFGenerator  Context !com.example.qr.utils.PDFGenerator  File !com.example.qr.utils.PDFGenerator  Float !com.example.qr.utils.PDFGenerator  Int !com.example.qr.utils.PDFGenerator  	PDFResult !com.example.qr.utils.PDFGenerator  Paint !com.example.qr.utils.PDFGenerator  PdfDocument !com.example.qr.utils.PDFGenerator  String !com.example.qr.utils.PDFGenerator  	PDFResult com.example.qr.utils.PDFResult  String com.example.qr.utils.PDFResult  String $com.example.qr.utils.PDFResult.Error  String &com.example.qr.utils.PDFResult.Success  Array &com.example.qr.utils.PermissionManager  Boolean &com.example.qr.utils.PermissionManager  Context &com.example.qr.utils.PermissionManager  String &com.example.qr.utils.PermissionManager  Boolean -com.example.qr.utils.PrivacyComplianceSummary  String -com.example.qr.utils.PrivacyComplianceSummary  Boolean #com.example.qr.utils.PrivacyManager  Context #com.example.qr.utils.PrivacyManager  Map #com.example.qr.utils.PrivacyManager  PrivacyComplianceSummary #com.example.qr.utils.PrivacyManager  
PrivacyStatus #com.example.qr.utils.PrivacyManager  SettingsDataStore #com.example.qr.utils.PrivacyManager  String #com.example.qr.utils.PrivacyManager  context #com.example.qr.utils.PrivacyManager  invoke #com.example.qr.utils.PrivacyManager  mapOf #com.example.qr.utils.PrivacyManager  to #com.example.qr.utils.PrivacyManager  Boolean -com.example.qr.utils.PrivacyManager.Companion  Context -com.example.qr.utils.PrivacyManager.Companion  Map -com.example.qr.utils.PrivacyManager.Companion  PrivacyComplianceSummary -com.example.qr.utils.PrivacyManager.Companion  
PrivacyStatus -com.example.qr.utils.PrivacyManager.Companion  SettingsDataStore -com.example.qr.utils.PrivacyManager.Companion  String -com.example.qr.utils.PrivacyManager.Companion  getMAPOf -com.example.qr.utils.PrivacyManager.Companion  getMapOf -com.example.qr.utils.PrivacyManager.Companion  getTO -com.example.qr.utils.PrivacyManager.Companion  getTo -com.example.qr.utils.PrivacyManager.Companion  invoke -com.example.qr.utils.PrivacyManager.Companion  mapOf -com.example.qr.utils.PrivacyManager.Companion  to -com.example.qr.utils.PrivacyManager.Companion  Boolean "com.example.qr.utils.PrivacyStatus  Long "com.example.qr.utils.PrivacyStatus  String "com.example.qr.utils.PrivacyStatus  
QRCodeType )com.example.qr.utils.QRCodeAnalysisResult  String )com.example.qr.utils.QRCodeAnalysisResult  Context #com.example.qr.utils.QRCodeAnalyzer  Pattern #com.example.qr.utils.QRCodeAnalyzer  QRCodeAnalysisResult #com.example.qr.utils.QRCodeAnalyzer  String #com.example.qr.utils.QRCodeAnalyzer  ErrorCorrectionLevel (com.example.qr.utils.QRCodeCustomization  Float (com.example.qr.utils.QRCodeCustomization  Int (com.example.qr.utils.QRCodeCustomization  String (com.example.qr.utils.QRCodeCustomization  Bitmap $com.example.qr.utils.QRCodeGenerator  Boolean $com.example.qr.utils.QRCodeGenerator  Double $com.example.qr.utils.QRCodeGenerator  ErrorCorrectionLevel $com.example.qr.utils.QRCodeGenerator  Int $com.example.qr.utils.QRCodeGenerator  String $com.example.qr.utils.QRCodeGenerator  WiFiSecurity $com.example.qr.utils.QRCodeGenerator  Boolean 'com.example.qr.utils.QRCodeLocalization  Context 'com.example.qr.utils.QRCodeLocalization  
QRCodeData 'com.example.qr.utils.QRCodeLocalization  
QRCodeType 'com.example.qr.utils.QRCodeLocalization  String 'com.example.qr.utils.QRCodeLocalization  Context 'com.example.qr.utils.QRFormatTestHelper  String 'com.example.qr.utils.QRFormatTestHelper  Bitmap 'com.example.qr.utils.QRGenerationResult  Boolean 'com.example.qr.utils.QRGenerationResult  
QRCodeData 'com.example.qr.utils.QRGenerationResult  QRGenerationResult 'com.example.qr.utils.QRGenerationResult  String 'com.example.qr.utils.QRGenerationResult  String -com.example.qr.utils.QRGenerationResult.Error  Bitmap /com.example.qr.utils.QRGenerationResult.Success  Boolean /com.example.qr.utils.QRGenerationResult.Success  
QRCodeData /com.example.qr.utils.QRGenerationResult.Success  String /com.example.qr.utils.QRGenerationResult.Success  BatchQRGenerationResult (com.example.qr.utils.QRGenerationService  Bitmap (com.example.qr.utils.QRGenerationService  Context (com.example.qr.utils.QRGenerationService  List (com.example.qr.utils.QRGenerationService  Pair (com.example.qr.utils.QRGenerationService  QRCodeCustomization (com.example.qr.utils.QRGenerationService  
QRCodeData (com.example.qr.utils.QRGenerationService  
QRCodeType (com.example.qr.utils.QRGenerationService  QRGenerationResult (com.example.qr.utils.QRGenerationService  String (com.example.qr.utils.QRGenerationService  Context %com.example.qr.utils.QRSizeTestHelper  String %com.example.qr.utils.QRSizeTestHelper  
Composable "com.example.qr.utils.SafeAreaUtils  Modifier "com.example.qr.utils.SafeAreaUtils  
PaddingValues "com.example.qr.utils.SafeAreaUtils  WindowInsets "com.example.qr.utils.SafeAreaUtils  dp "com.example.qr.utils.SafeAreaUtils  safeDrawing "com.example.qr.utils.SafeAreaUtils  standardHorizontalPadding "com.example.qr.utils.SafeAreaUtils  standardVerticalPadding "com.example.qr.utils.SafeAreaUtils  
statusBars "com.example.qr.utils.SafeAreaUtils  Context 'com.example.qr.utils.SettingsTestHelper  SettingsRepository 'com.example.qr.utils.SettingsTestHelper  Bitmap com.example.qr.utils.ShareUtils  Context com.example.qr.utils.ShareUtils  File com.example.qr.utils.ShareUtils  
QRCodeData com.example.qr.utils.ShareUtils  
SaveResult com.example.qr.utils.ShareUtils  ShareResult com.example.qr.utils.ShareUtils  String com.example.qr.utils.ShareUtils  
SaveResult *com.example.qr.utils.ShareUtils.SaveResult  String *com.example.qr.utils.ShareUtils.SaveResult  String 0com.example.qr.utils.ShareUtils.SaveResult.Error  String 2com.example.qr.utils.ShareUtils.SaveResult.Success  ShareResult +com.example.qr.utils.ShareUtils.ShareResult  String +com.example.qr.utils.ShareUtils.ShareResult  String 1com.example.qr.utils.ShareUtils.ShareResult.Error  String 3com.example.qr.utils.ShareUtils.ShareResult.Success  AudioManager !com.example.qr.utils.SoundManager  Boolean !com.example.qr.utils.SoundManager  Context !com.example.qr.utils.SoundManager  Float !com.example.qr.utils.SoundManager  Inject !com.example.qr.utils.SoundManager  Int !com.example.qr.utils.SoundManager  MediaPlayer !com.example.qr.utils.SoundManager  SettingsRepository !com.example.qr.utils.SoundManager  	SoundPool !com.example.qr.utils.SoundManager  
ToneGenerator !com.example.qr.utils.SoundManager  context !com.example.qr.utils.SoundManager  AudioManager +com.example.qr.utils.SoundManager.Companion  Boolean +com.example.qr.utils.SoundManager.Companion  Context +com.example.qr.utils.SoundManager.Companion  Float +com.example.qr.utils.SoundManager.Companion  Inject +com.example.qr.utils.SoundManager.Companion  Int +com.example.qr.utils.SoundManager.Companion  MediaPlayer +com.example.qr.utils.SoundManager.Companion  SettingsRepository +com.example.qr.utils.SoundManager.Companion  	SoundPool +com.example.qr.utils.SoundManager.Companion  
ToneGenerator +com.example.qr.utils.SoundManager.Companion  invoke +com.example.qr.utils.SoundManager.Companion  List $com.example.qr.utils.TutorialContent  TutorialStep $com.example.qr.utils.TutorialContent  android $com.example.qr.utils.TutorialContent  Boolean $com.example.qr.utils.TutorialManager  
Composable $com.example.qr.utils.TutorialManager  Context $com.example.qr.utils.TutorialManager  PREF_GENERATOR_TUTORIAL_SHOWN $com.example.qr.utils.TutorialManager  PREF_HISTORY_TUTORIAL_SHOWN $com.example.qr.utils.TutorialManager  PREF_HOME_TUTORIAL_SHOWN $com.example.qr.utils.TutorialManager  PREF_SCANNER_TUTORIAL_SHOWN $com.example.qr.utils.TutorialManager  PREF_SETTINGS_TUTORIAL_SHOWN $com.example.qr.utils.TutorialManager  SettingsRepository $com.example.qr.utils.TutorialManager  String $com.example.qr.utils.TutorialManager  PREF_GENERATOR_TUTORIAL_SHOWN 1com.example.qr.utils.TutorialManager.TutorialKeys  PREF_HISTORY_TUTORIAL_SHOWN 1com.example.qr.utils.TutorialManager.TutorialKeys  PREF_HOME_TUTORIAL_SHOWN 1com.example.qr.utils.TutorialManager.TutorialKeys  PREF_SCANNER_TUTORIAL_SHOWN 1com.example.qr.utils.TutorialManager.TutorialKeys  PREF_SETTINGS_TUTORIAL_SHOWN 1com.example.qr.utils.TutorialManager.TutorialKeys  String !com.example.qr.utils.TutorialStep  String !com.example.qr.utils.WiFiSecurity  WiFiSecurity !com.example.qr.utils.WiFiSecurity  ExperimentalPermissionsApi "com.google.accompanist.permissions  	isGranted "com.google.accompanist.permissions  rememberPermissionState "com.google.accompanist.permissions  shouldShowRationale "com.google.accompanist.permissions  ConnectionResult com.google.android.gms.common  GoogleApiAvailability com.google.android.gms.common  BarcodeScanner com.google.mlkit.vision.barcode  BarcodeScanning com.google.mlkit.vision.barcode  	getClient /com.google.mlkit.vision.barcode.BarcodeScanning  Barcode &com.google.mlkit.vision.barcode.common  
InputImage com.google.mlkit.vision.common  
BarcodeFormat com.google.zxing  EncodeHintType com.google.zxing  WriterException com.google.zxing  	BitMatrix com.google.zxing.common  QRCodeWriter com.google.zxing.qrcode  ErrorCorrectionLevel com.google.zxing.qrcode.decoder  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  ANALYTICS_ENABLED 	java.lang  AUTO_OPEN_LINKS 	java.lang  AUTO_SAVE_GENERATED 	java.lang  AUTO_SAVE_SCANNED 	java.lang  AppSettings 	java.lang  BarcodeScanning 	java.lang  Build 	java.lang  CONSENT_TIMESTAMP 	java.lang  CRASH_REPORTING_ENABLED 	java.lang  Context 	java.lang  DEFAULT_QR_FORMAT 	java.lang  DEFAULT_QR_SIZE 	java.lang  ENABLE_ANIMATIONS 	java.lang  ExperimentalComposeUiApi 	java.lang  ExperimentalFoundationApi 	java.lang  ExperimentalGetImage 	java.lang  ExperimentalMaterial3Api 	java.lang  ExperimentalPermissionsApi 	java.lang  FIRST_TIME_LANGUAGE_SELECTION 	java.lang  FIRST_TIME_PRIVACY_SHOWN 	java.lang  
FontWeight 	java.lang  GeneratorUiState 	java.lang  HistoryUiState 	java.lang  Language 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  PLAY_SOUND_ON_SCAN 	java.lang  PREF_GENERATOR_TUTORIAL_SHOWN 	java.lang  PREF_HISTORY_TUTORIAL_SHOWN 	java.lang  PREF_HOME_TUTORIAL_SHOWN 	java.lang  PREF_SCANNER_TUTORIAL_SHOWN 	java.lang  PREF_SETTINGS_TUTORIAL_SHOWN 	java.lang  PRIVACY_POLICY_ACCEPTED 	java.lang  PRIVACY_POLICY_VERSION 	java.lang  
PaddingValues 	java.lang  Pattern 	java.lang  PersistentQRRepository 	java.lang  QRCodeConverters 	java.lang  QRCodeEntity 	java.lang  QRSparkDatabase 	java.lang  R 	java.lang  SELECTED_LANGUAGE 	java.lang  SHOW_TUTORIALS 	java.lang  SafeFontFamily 	java.lang  ScannerUiState 	java.lang  SettingsDataStore 	java.lang  SettingsRepository 	java.lang  SharingStarted 	java.lang  SoundManager 	java.lang  
THEME_MODE 	java.lang  	TextStyle 	java.lang  VIBRATE_ON_SCAN 	java.lang  WindowInsets 	java.lang  android 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  booleanPreferencesKey 	java.lang  com 	java.lang  combine 	java.lang  contains 	java.lang  	emptyList 	java.lang  filter 	java.lang  getValue 	java.lang  isBlank 	java.lang  lazy 	java.lang  longPreferencesKey 	java.lang  map 	java.lang  mapOf 	java.lang  provideDelegate 	java.lang  sortedByDescending 	java.lang  stateIn 	java.lang  stringPreferencesKey 	java.lang  to 	java.lang  toQRCodeData 	java.lang  SimpleDateFormat 	java.text  
Composable 	java.util  Date 	java.util  ExperimentalFoundationApi 	java.util  ExperimentalMaterial3Api 	java.util  Locale 	java.util  Pair 	java.util  SettingsDataStore 	java.util  UUID 	java.util  androidx 	java.util  mapOf 	java.util  to 	java.util  ExecutorService java.util.concurrent  	Executors java.util.concurrent  Pattern java.util.regex  CASE_INSENSITIVE java.util.regex.Pattern  compile java.util.regex.Pattern  Inject javax.inject  	Singleton javax.inject  ANALYTICS_ENABLED kotlin  AUTO_OPEN_LINKS kotlin  AUTO_SAVE_GENERATED kotlin  AUTO_SAVE_SCANNED kotlin  Any kotlin  AppSettings kotlin  Array kotlin  BarcodeScanning kotlin  Boolean kotlin  Build kotlin  CONSENT_TIMESTAMP kotlin  CRASH_REPORTING_ENABLED kotlin  Context kotlin  DEFAULT_QR_FORMAT kotlin  DEFAULT_QR_SIZE kotlin  Double kotlin  ENABLE_ANIMATIONS kotlin  ExperimentalComposeUiApi kotlin  ExperimentalFoundationApi kotlin  ExperimentalGetImage kotlin  ExperimentalMaterial3Api kotlin  ExperimentalPermissionsApi kotlin  FIRST_TIME_LANGUAGE_SELECTION kotlin  FIRST_TIME_PRIVACY_SHOWN kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  GeneratorUiState kotlin  HistoryUiState kotlin  Int kotlin  Language kotlin  Lazy kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  PLAY_SOUND_ON_SCAN kotlin  PREF_GENERATOR_TUTORIAL_SHOWN kotlin  PREF_HISTORY_TUTORIAL_SHOWN kotlin  PREF_HOME_TUTORIAL_SHOWN kotlin  PREF_SCANNER_TUTORIAL_SHOWN kotlin  PREF_SETTINGS_TUTORIAL_SHOWN kotlin  PRIVACY_POLICY_ACCEPTED kotlin  PRIVACY_POLICY_VERSION kotlin  
PaddingValues kotlin  Pair kotlin  Pattern kotlin  PersistentQRRepository kotlin  QRCodeConverters kotlin  QRCodeEntity kotlin  QRSparkDatabase kotlin  R kotlin  SELECTED_LANGUAGE kotlin  SHOW_TUTORIALS kotlin  SafeFontFamily kotlin  ScannerUiState kotlin  SettingsDataStore kotlin  SettingsRepository kotlin  SharingStarted kotlin  SoundManager kotlin  String kotlin  Suppress kotlin  
THEME_MODE kotlin  	TextStyle kotlin  Unit kotlin  VIBRATE_ON_SCAN kotlin  Volatile kotlin  WindowInsets kotlin  android kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  booleanPreferencesKey kotlin  com kotlin  combine kotlin  contains kotlin  	emptyList kotlin  filter kotlin  getValue kotlin  isBlank kotlin  lazy kotlin  longPreferencesKey kotlin  map kotlin  mapOf kotlin  provideDelegate kotlin  sortedByDescending kotlin  stateIn kotlin  stringPreferencesKey kotlin  to kotlin  toQRCodeData kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getCONTAINS 
kotlin.String  getContains 
kotlin.String  
getISBlank 
kotlin.String  
getIsBlank 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  isBlank 
kotlin.String  ANALYTICS_ENABLED kotlin.annotation  AUTO_OPEN_LINKS kotlin.annotation  AUTO_SAVE_GENERATED kotlin.annotation  AUTO_SAVE_SCANNED kotlin.annotation  AppSettings kotlin.annotation  BarcodeScanning kotlin.annotation  Build kotlin.annotation  CONSENT_TIMESTAMP kotlin.annotation  CRASH_REPORTING_ENABLED kotlin.annotation  Context kotlin.annotation  DEFAULT_QR_FORMAT kotlin.annotation  DEFAULT_QR_SIZE kotlin.annotation  ENABLE_ANIMATIONS kotlin.annotation  ExperimentalComposeUiApi kotlin.annotation  ExperimentalFoundationApi kotlin.annotation  ExperimentalGetImage kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  ExperimentalPermissionsApi kotlin.annotation  FIRST_TIME_LANGUAGE_SELECTION kotlin.annotation  FIRST_TIME_PRIVACY_SHOWN kotlin.annotation  
FontWeight kotlin.annotation  GeneratorUiState kotlin.annotation  HistoryUiState kotlin.annotation  Language kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  PLAY_SOUND_ON_SCAN kotlin.annotation  PREF_GENERATOR_TUTORIAL_SHOWN kotlin.annotation  PREF_HISTORY_TUTORIAL_SHOWN kotlin.annotation  PREF_HOME_TUTORIAL_SHOWN kotlin.annotation  PREF_SCANNER_TUTORIAL_SHOWN kotlin.annotation  PREF_SETTINGS_TUTORIAL_SHOWN kotlin.annotation  PRIVACY_POLICY_ACCEPTED kotlin.annotation  PRIVACY_POLICY_VERSION kotlin.annotation  
PaddingValues kotlin.annotation  Pair kotlin.annotation  Pattern kotlin.annotation  PersistentQRRepository kotlin.annotation  QRCodeConverters kotlin.annotation  QRCodeEntity kotlin.annotation  QRSparkDatabase kotlin.annotation  R kotlin.annotation  SELECTED_LANGUAGE kotlin.annotation  SHOW_TUTORIALS kotlin.annotation  SafeFontFamily kotlin.annotation  ScannerUiState kotlin.annotation  SettingsDataStore kotlin.annotation  SettingsRepository kotlin.annotation  SharingStarted kotlin.annotation  SoundManager kotlin.annotation  
THEME_MODE kotlin.annotation  	TextStyle kotlin.annotation  VIBRATE_ON_SCAN kotlin.annotation  Volatile kotlin.annotation  WindowInsets kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  booleanPreferencesKey kotlin.annotation  com kotlin.annotation  combine kotlin.annotation  contains kotlin.annotation  	emptyList kotlin.annotation  filter kotlin.annotation  getValue kotlin.annotation  isBlank kotlin.annotation  lazy kotlin.annotation  longPreferencesKey kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  provideDelegate kotlin.annotation  sortedByDescending kotlin.annotation  stateIn kotlin.annotation  stringPreferencesKey kotlin.annotation  to kotlin.annotation  toQRCodeData kotlin.annotation  ANALYTICS_ENABLED kotlin.collections  AUTO_OPEN_LINKS kotlin.collections  AUTO_SAVE_GENERATED kotlin.collections  AUTO_SAVE_SCANNED kotlin.collections  AppSettings kotlin.collections  BarcodeScanning kotlin.collections  Build kotlin.collections  CONSENT_TIMESTAMP kotlin.collections  CRASH_REPORTING_ENABLED kotlin.collections  Context kotlin.collections  DEFAULT_QR_FORMAT kotlin.collections  DEFAULT_QR_SIZE kotlin.collections  ENABLE_ANIMATIONS kotlin.collections  ExperimentalComposeUiApi kotlin.collections  ExperimentalFoundationApi kotlin.collections  ExperimentalGetImage kotlin.collections  ExperimentalMaterial3Api kotlin.collections  ExperimentalPermissionsApi kotlin.collections  FIRST_TIME_LANGUAGE_SELECTION kotlin.collections  FIRST_TIME_PRIVACY_SHOWN kotlin.collections  
FontWeight kotlin.collections  GeneratorUiState kotlin.collections  HistoryUiState kotlin.collections  Language kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  PLAY_SOUND_ON_SCAN kotlin.collections  PREF_GENERATOR_TUTORIAL_SHOWN kotlin.collections  PREF_HISTORY_TUTORIAL_SHOWN kotlin.collections  PREF_HOME_TUTORIAL_SHOWN kotlin.collections  PREF_SCANNER_TUTORIAL_SHOWN kotlin.collections  PREF_SETTINGS_TUTORIAL_SHOWN kotlin.collections  PRIVACY_POLICY_ACCEPTED kotlin.collections  PRIVACY_POLICY_VERSION kotlin.collections  
PaddingValues kotlin.collections  Pair kotlin.collections  Pattern kotlin.collections  PersistentQRRepository kotlin.collections  QRCodeConverters kotlin.collections  QRCodeEntity kotlin.collections  QRSparkDatabase kotlin.collections  R kotlin.collections  SELECTED_LANGUAGE kotlin.collections  SHOW_TUTORIALS kotlin.collections  SafeFontFamily kotlin.collections  ScannerUiState kotlin.collections  SettingsDataStore kotlin.collections  SettingsRepository kotlin.collections  SharingStarted kotlin.collections  SoundManager kotlin.collections  
THEME_MODE kotlin.collections  	TextStyle kotlin.collections  VIBRATE_ON_SCAN kotlin.collections  Volatile kotlin.collections  WindowInsets kotlin.collections  android kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  booleanPreferencesKey kotlin.collections  com kotlin.collections  combine kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  getValue kotlin.collections  isBlank kotlin.collections  lazy kotlin.collections  longPreferencesKey kotlin.collections  map kotlin.collections  mapOf kotlin.collections  provideDelegate kotlin.collections  sortedByDescending kotlin.collections  stateIn kotlin.collections  stringPreferencesKey kotlin.collections  to kotlin.collections  toQRCodeData kotlin.collections  	getFILTER kotlin.collections.List  	getFilter kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getSORTEDByDescending kotlin.collections.List  getSortedByDescending kotlin.collections.List  ANALYTICS_ENABLED kotlin.comparisons  AUTO_OPEN_LINKS kotlin.comparisons  AUTO_SAVE_GENERATED kotlin.comparisons  AUTO_SAVE_SCANNED kotlin.comparisons  AppSettings kotlin.comparisons  BarcodeScanning kotlin.comparisons  Build kotlin.comparisons  CONSENT_TIMESTAMP kotlin.comparisons  CRASH_REPORTING_ENABLED kotlin.comparisons  Context kotlin.comparisons  DEFAULT_QR_FORMAT kotlin.comparisons  DEFAULT_QR_SIZE kotlin.comparisons  ENABLE_ANIMATIONS kotlin.comparisons  ExperimentalComposeUiApi kotlin.comparisons  ExperimentalFoundationApi kotlin.comparisons  ExperimentalGetImage kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  ExperimentalPermissionsApi kotlin.comparisons  FIRST_TIME_LANGUAGE_SELECTION kotlin.comparisons  FIRST_TIME_PRIVACY_SHOWN kotlin.comparisons  
FontWeight kotlin.comparisons  GeneratorUiState kotlin.comparisons  HistoryUiState kotlin.comparisons  Language kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  PLAY_SOUND_ON_SCAN kotlin.comparisons  PREF_GENERATOR_TUTORIAL_SHOWN kotlin.comparisons  PREF_HISTORY_TUTORIAL_SHOWN kotlin.comparisons  PREF_HOME_TUTORIAL_SHOWN kotlin.comparisons  PREF_SCANNER_TUTORIAL_SHOWN kotlin.comparisons  PREF_SETTINGS_TUTORIAL_SHOWN kotlin.comparisons  PRIVACY_POLICY_ACCEPTED kotlin.comparisons  PRIVACY_POLICY_VERSION kotlin.comparisons  
PaddingValues kotlin.comparisons  Pair kotlin.comparisons  Pattern kotlin.comparisons  PersistentQRRepository kotlin.comparisons  QRCodeConverters kotlin.comparisons  QRCodeEntity kotlin.comparisons  QRSparkDatabase kotlin.comparisons  R kotlin.comparisons  SELECTED_LANGUAGE kotlin.comparisons  SHOW_TUTORIALS kotlin.comparisons  SafeFontFamily kotlin.comparisons  ScannerUiState kotlin.comparisons  SettingsDataStore kotlin.comparisons  SettingsRepository kotlin.comparisons  SharingStarted kotlin.comparisons  SoundManager kotlin.comparisons  
THEME_MODE kotlin.comparisons  	TextStyle kotlin.comparisons  VIBRATE_ON_SCAN kotlin.comparisons  Volatile kotlin.comparisons  WindowInsets kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  booleanPreferencesKey kotlin.comparisons  com kotlin.comparisons  combine kotlin.comparisons  contains kotlin.comparisons  	emptyList kotlin.comparisons  filter kotlin.comparisons  getValue kotlin.comparisons  isBlank kotlin.comparisons  lazy kotlin.comparisons  longPreferencesKey kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  provideDelegate kotlin.comparisons  sortedByDescending kotlin.comparisons  stateIn kotlin.comparisons  stringPreferencesKey kotlin.comparisons  to kotlin.comparisons  toQRCodeData kotlin.comparisons  SuspendFunction1 kotlin.coroutines  SuspendFunction3 kotlin.coroutines  ANALYTICS_ENABLED 	kotlin.io  AUTO_OPEN_LINKS 	kotlin.io  AUTO_SAVE_GENERATED 	kotlin.io  AUTO_SAVE_SCANNED 	kotlin.io  AppSettings 	kotlin.io  BarcodeScanning 	kotlin.io  Build 	kotlin.io  CONSENT_TIMESTAMP 	kotlin.io  CRASH_REPORTING_ENABLED 	kotlin.io  Context 	kotlin.io  DEFAULT_QR_FORMAT 	kotlin.io  DEFAULT_QR_SIZE 	kotlin.io  ENABLE_ANIMATIONS 	kotlin.io  ExperimentalComposeUiApi 	kotlin.io  ExperimentalFoundationApi 	kotlin.io  ExperimentalGetImage 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  ExperimentalPermissionsApi 	kotlin.io  FIRST_TIME_LANGUAGE_SELECTION 	kotlin.io  FIRST_TIME_PRIVACY_SHOWN 	kotlin.io  
FontWeight 	kotlin.io  GeneratorUiState 	kotlin.io  HistoryUiState 	kotlin.io  Language 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  PLAY_SOUND_ON_SCAN 	kotlin.io  PREF_GENERATOR_TUTORIAL_SHOWN 	kotlin.io  PREF_HISTORY_TUTORIAL_SHOWN 	kotlin.io  PREF_HOME_TUTORIAL_SHOWN 	kotlin.io  PREF_SCANNER_TUTORIAL_SHOWN 	kotlin.io  PREF_SETTINGS_TUTORIAL_SHOWN 	kotlin.io  PRIVACY_POLICY_ACCEPTED 	kotlin.io  PRIVACY_POLICY_VERSION 	kotlin.io  
PaddingValues 	kotlin.io  Pair 	kotlin.io  Pattern 	kotlin.io  PersistentQRRepository 	kotlin.io  QRCodeConverters 	kotlin.io  QRCodeEntity 	kotlin.io  QRSparkDatabase 	kotlin.io  R 	kotlin.io  SELECTED_LANGUAGE 	kotlin.io  SHOW_TUTORIALS 	kotlin.io  SafeFontFamily 	kotlin.io  ScannerUiState 	kotlin.io  SettingsDataStore 	kotlin.io  SettingsRepository 	kotlin.io  SharingStarted 	kotlin.io  SoundManager 	kotlin.io  
THEME_MODE 	kotlin.io  	TextStyle 	kotlin.io  VIBRATE_ON_SCAN 	kotlin.io  Volatile 	kotlin.io  WindowInsets 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  booleanPreferencesKey 	kotlin.io  com 	kotlin.io  combine 	kotlin.io  contains 	kotlin.io  	emptyList 	kotlin.io  filter 	kotlin.io  getValue 	kotlin.io  isBlank 	kotlin.io  lazy 	kotlin.io  longPreferencesKey 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  provideDelegate 	kotlin.io  sortedByDescending 	kotlin.io  stateIn 	kotlin.io  stringPreferencesKey 	kotlin.io  to 	kotlin.io  toQRCodeData 	kotlin.io  ANALYTICS_ENABLED 
kotlin.jvm  AUTO_OPEN_LINKS 
kotlin.jvm  AUTO_SAVE_GENERATED 
kotlin.jvm  AUTO_SAVE_SCANNED 
kotlin.jvm  AppSettings 
kotlin.jvm  BarcodeScanning 
kotlin.jvm  Build 
kotlin.jvm  CONSENT_TIMESTAMP 
kotlin.jvm  CRASH_REPORTING_ENABLED 
kotlin.jvm  Context 
kotlin.jvm  DEFAULT_QR_FORMAT 
kotlin.jvm  DEFAULT_QR_SIZE 
kotlin.jvm  ENABLE_ANIMATIONS 
kotlin.jvm  ExperimentalComposeUiApi 
kotlin.jvm  ExperimentalFoundationApi 
kotlin.jvm  ExperimentalGetImage 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  ExperimentalPermissionsApi 
kotlin.jvm  FIRST_TIME_LANGUAGE_SELECTION 
kotlin.jvm  FIRST_TIME_PRIVACY_SHOWN 
kotlin.jvm  
FontWeight 
kotlin.jvm  GeneratorUiState 
kotlin.jvm  HistoryUiState 
kotlin.jvm  Language 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  PLAY_SOUND_ON_SCAN 
kotlin.jvm  PREF_GENERATOR_TUTORIAL_SHOWN 
kotlin.jvm  PREF_HISTORY_TUTORIAL_SHOWN 
kotlin.jvm  PREF_HOME_TUTORIAL_SHOWN 
kotlin.jvm  PREF_SCANNER_TUTORIAL_SHOWN 
kotlin.jvm  PREF_SETTINGS_TUTORIAL_SHOWN 
kotlin.jvm  PRIVACY_POLICY_ACCEPTED 
kotlin.jvm  PRIVACY_POLICY_VERSION 
kotlin.jvm  
PaddingValues 
kotlin.jvm  Pair 
kotlin.jvm  Pattern 
kotlin.jvm  PersistentQRRepository 
kotlin.jvm  QRCodeConverters 
kotlin.jvm  QRCodeEntity 
kotlin.jvm  QRSparkDatabase 
kotlin.jvm  R 
kotlin.jvm  SELECTED_LANGUAGE 
kotlin.jvm  SHOW_TUTORIALS 
kotlin.jvm  SafeFontFamily 
kotlin.jvm  ScannerUiState 
kotlin.jvm  SettingsDataStore 
kotlin.jvm  SettingsRepository 
kotlin.jvm  SharingStarted 
kotlin.jvm  SoundManager 
kotlin.jvm  
THEME_MODE 
kotlin.jvm  	TextStyle 
kotlin.jvm  VIBRATE_ON_SCAN 
kotlin.jvm  Volatile 
kotlin.jvm  WindowInsets 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  booleanPreferencesKey 
kotlin.jvm  com 
kotlin.jvm  combine 
kotlin.jvm  contains 
kotlin.jvm  	emptyList 
kotlin.jvm  filter 
kotlin.jvm  getValue 
kotlin.jvm  isBlank 
kotlin.jvm  lazy 
kotlin.jvm  longPreferencesKey 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  provideDelegate 
kotlin.jvm  sortedByDescending 
kotlin.jvm  stateIn 
kotlin.jvm  stringPreferencesKey 
kotlin.jvm  to 
kotlin.jvm  toQRCodeData 
kotlin.jvm  abs kotlin.math  ReadOnlyProperty kotlin.properties  getPROVIDEDelegate "kotlin.properties.ReadOnlyProperty  getProvideDelegate "kotlin.properties.ReadOnlyProperty  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  ANALYTICS_ENABLED 
kotlin.ranges  AUTO_OPEN_LINKS 
kotlin.ranges  AUTO_SAVE_GENERATED 
kotlin.ranges  AUTO_SAVE_SCANNED 
kotlin.ranges  AppSettings 
kotlin.ranges  BarcodeScanning 
kotlin.ranges  Build 
kotlin.ranges  CONSENT_TIMESTAMP 
kotlin.ranges  CRASH_REPORTING_ENABLED 
kotlin.ranges  Context 
kotlin.ranges  DEFAULT_QR_FORMAT 
kotlin.ranges  DEFAULT_QR_SIZE 
kotlin.ranges  ENABLE_ANIMATIONS 
kotlin.ranges  ExperimentalComposeUiApi 
kotlin.ranges  ExperimentalFoundationApi 
kotlin.ranges  ExperimentalGetImage 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  ExperimentalPermissionsApi 
kotlin.ranges  FIRST_TIME_LANGUAGE_SELECTION 
kotlin.ranges  FIRST_TIME_PRIVACY_SHOWN 
kotlin.ranges  
FontWeight 
kotlin.ranges  GeneratorUiState 
kotlin.ranges  HistoryUiState 
kotlin.ranges  Language 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  PLAY_SOUND_ON_SCAN 
kotlin.ranges  PREF_GENERATOR_TUTORIAL_SHOWN 
kotlin.ranges  PREF_HISTORY_TUTORIAL_SHOWN 
kotlin.ranges  PREF_HOME_TUTORIAL_SHOWN 
kotlin.ranges  PREF_SCANNER_TUTORIAL_SHOWN 
kotlin.ranges  PREF_SETTINGS_TUTORIAL_SHOWN 
kotlin.ranges  PRIVACY_POLICY_ACCEPTED 
kotlin.ranges  PRIVACY_POLICY_VERSION 
kotlin.ranges  
PaddingValues 
kotlin.ranges  Pair 
kotlin.ranges  Pattern 
kotlin.ranges  PersistentQRRepository 
kotlin.ranges  QRCodeConverters 
kotlin.ranges  QRCodeEntity 
kotlin.ranges  QRSparkDatabase 
kotlin.ranges  R 
kotlin.ranges  SELECTED_LANGUAGE 
kotlin.ranges  SHOW_TUTORIALS 
kotlin.ranges  SafeFontFamily 
kotlin.ranges  ScannerUiState 
kotlin.ranges  SettingsDataStore 
kotlin.ranges  SettingsRepository 
kotlin.ranges  SharingStarted 
kotlin.ranges  SoundManager 
kotlin.ranges  
THEME_MODE 
kotlin.ranges  	TextStyle 
kotlin.ranges  VIBRATE_ON_SCAN 
kotlin.ranges  Volatile 
kotlin.ranges  WindowInsets 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  booleanPreferencesKey 
kotlin.ranges  com 
kotlin.ranges  combine 
kotlin.ranges  contains 
kotlin.ranges  	emptyList 
kotlin.ranges  filter 
kotlin.ranges  getValue 
kotlin.ranges  isBlank 
kotlin.ranges  lazy 
kotlin.ranges  longPreferencesKey 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  provideDelegate 
kotlin.ranges  sortedByDescending 
kotlin.ranges  stateIn 
kotlin.ranges  stringPreferencesKey 
kotlin.ranges  to 
kotlin.ranges  toQRCodeData 
kotlin.ranges  KClass kotlin.reflect  ANALYTICS_ENABLED kotlin.sequences  AUTO_OPEN_LINKS kotlin.sequences  AUTO_SAVE_GENERATED kotlin.sequences  AUTO_SAVE_SCANNED kotlin.sequences  AppSettings kotlin.sequences  BarcodeScanning kotlin.sequences  Build kotlin.sequences  CONSENT_TIMESTAMP kotlin.sequences  CRASH_REPORTING_ENABLED kotlin.sequences  Context kotlin.sequences  DEFAULT_QR_FORMAT kotlin.sequences  DEFAULT_QR_SIZE kotlin.sequences  ENABLE_ANIMATIONS kotlin.sequences  ExperimentalComposeUiApi kotlin.sequences  ExperimentalFoundationApi kotlin.sequences  ExperimentalGetImage kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  ExperimentalPermissionsApi kotlin.sequences  FIRST_TIME_LANGUAGE_SELECTION kotlin.sequences  FIRST_TIME_PRIVACY_SHOWN kotlin.sequences  
FontWeight kotlin.sequences  GeneratorUiState kotlin.sequences  HistoryUiState kotlin.sequences  Language kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  PLAY_SOUND_ON_SCAN kotlin.sequences  PREF_GENERATOR_TUTORIAL_SHOWN kotlin.sequences  PREF_HISTORY_TUTORIAL_SHOWN kotlin.sequences  PREF_HOME_TUTORIAL_SHOWN kotlin.sequences  PREF_SCANNER_TUTORIAL_SHOWN kotlin.sequences  PREF_SETTINGS_TUTORIAL_SHOWN kotlin.sequences  PRIVACY_POLICY_ACCEPTED kotlin.sequences  PRIVACY_POLICY_VERSION kotlin.sequences  
PaddingValues kotlin.sequences  Pair kotlin.sequences  Pattern kotlin.sequences  PersistentQRRepository kotlin.sequences  QRCodeConverters kotlin.sequences  QRCodeEntity kotlin.sequences  QRSparkDatabase kotlin.sequences  R kotlin.sequences  SELECTED_LANGUAGE kotlin.sequences  SHOW_TUTORIALS kotlin.sequences  SafeFontFamily kotlin.sequences  ScannerUiState kotlin.sequences  SettingsDataStore kotlin.sequences  SettingsRepository kotlin.sequences  SharingStarted kotlin.sequences  SoundManager kotlin.sequences  
THEME_MODE kotlin.sequences  	TextStyle kotlin.sequences  VIBRATE_ON_SCAN kotlin.sequences  Volatile kotlin.sequences  WindowInsets kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  booleanPreferencesKey kotlin.sequences  com kotlin.sequences  combine kotlin.sequences  contains kotlin.sequences  	emptyList kotlin.sequences  filter kotlin.sequences  getValue kotlin.sequences  isBlank kotlin.sequences  lazy kotlin.sequences  longPreferencesKey kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  provideDelegate kotlin.sequences  sortedByDescending kotlin.sequences  stateIn kotlin.sequences  stringPreferencesKey kotlin.sequences  to kotlin.sequences  toQRCodeData kotlin.sequences  ANALYTICS_ENABLED kotlin.text  AUTO_OPEN_LINKS kotlin.text  AUTO_SAVE_GENERATED kotlin.text  AUTO_SAVE_SCANNED kotlin.text  AppSettings kotlin.text  BarcodeScanning kotlin.text  Build kotlin.text  CONSENT_TIMESTAMP kotlin.text  CRASH_REPORTING_ENABLED kotlin.text  Context kotlin.text  DEFAULT_QR_FORMAT kotlin.text  DEFAULT_QR_SIZE kotlin.text  ENABLE_ANIMATIONS kotlin.text  ExperimentalComposeUiApi kotlin.text  ExperimentalFoundationApi kotlin.text  ExperimentalGetImage kotlin.text  ExperimentalMaterial3Api kotlin.text  ExperimentalPermissionsApi kotlin.text  FIRST_TIME_LANGUAGE_SELECTION kotlin.text  FIRST_TIME_PRIVACY_SHOWN kotlin.text  
FontWeight kotlin.text  GeneratorUiState kotlin.text  HistoryUiState kotlin.text  Language kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  PLAY_SOUND_ON_SCAN kotlin.text  PREF_GENERATOR_TUTORIAL_SHOWN kotlin.text  PREF_HISTORY_TUTORIAL_SHOWN kotlin.text  PREF_HOME_TUTORIAL_SHOWN kotlin.text  PREF_SCANNER_TUTORIAL_SHOWN kotlin.text  PREF_SETTINGS_TUTORIAL_SHOWN kotlin.text  PRIVACY_POLICY_ACCEPTED kotlin.text  PRIVACY_POLICY_VERSION kotlin.text  
PaddingValues kotlin.text  Pair kotlin.text  Pattern kotlin.text  PersistentQRRepository kotlin.text  QRCodeConverters kotlin.text  QRCodeEntity kotlin.text  QRSparkDatabase kotlin.text  R kotlin.text  SELECTED_LANGUAGE kotlin.text  SHOW_TUTORIALS kotlin.text  SafeFontFamily kotlin.text  ScannerUiState kotlin.text  SettingsDataStore kotlin.text  SettingsRepository kotlin.text  SharingStarted kotlin.text  SoundManager kotlin.text  
THEME_MODE kotlin.text  	TextStyle kotlin.text  VIBRATE_ON_SCAN kotlin.text  Volatile kotlin.text  WindowInsets kotlin.text  android kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  booleanPreferencesKey kotlin.text  com kotlin.text  combine kotlin.text  contains kotlin.text  	emptyList kotlin.text  filter kotlin.text  getValue kotlin.text  isBlank kotlin.text  lazy kotlin.text  longPreferencesKey kotlin.text  map kotlin.text  mapOf kotlin.text  provideDelegate kotlin.text  sortedByDescending kotlin.text  stateIn kotlin.text  stringPreferencesKey kotlin.text  to kotlin.text  toQRCodeData kotlin.text  CancellationException kotlinx.coroutines  CoroutineScope kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  runBlocking kotlinx.coroutines  Flow kotlinx.coroutines.flow  HistoryUiState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  PersistentQRRepository kotlinx.coroutines.flow  SharingStarted kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  combine kotlinx.coroutines.flow  contains kotlinx.coroutines.flow  	emptyList kotlinx.coroutines.flow  filter kotlinx.coroutines.flow  first kotlinx.coroutines.flow  isBlank kotlinx.coroutines.flow  launchIn kotlinx.coroutines.flow  map kotlinx.coroutines.flow  onEach kotlinx.coroutines.flow  sortedByDescending kotlinx.coroutines.flow  stateIn kotlinx.coroutines.flow  viewModelScope kotlinx.coroutines.flow  getMAP kotlinx.coroutines.flow.Flow  getMap kotlinx.coroutines.flow.Flow  
getSTATEIn kotlinx.coroutines.flow.Flow  
getStateIn kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  stateIn kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  WhileSubscribed &kotlinx.coroutines.flow.SharingStarted  WhileSubscribed 0kotlinx.coroutines.flow.SharingStarted.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           