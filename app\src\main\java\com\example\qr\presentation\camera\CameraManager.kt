package com.example.qr.presentation.camera

import android.content.Context
import android.util.Log
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.example.qr.presentation.camera.analyzer.QRCodeAnalyzer
import java.util.concurrent.ExecutorService

/**
 * Centralized camera management to prevent conflicts and ensure proper lifecycle handling
 */
class CameraManager private constructor() {

    companion object {
        @Volatile
        private var INSTANCE: CameraManager? = null

        fun getInstance(): CameraManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CameraManager().also { INSTANCE = it }
            }
        }
    }

    private var currentCameraProvider: ProcessCameraProvider? = null
    private var currentCamera: Camera? = null
    private var currentAnalyzer: QRCodeAnalyzer? = null
    private var isInitialized = false
    private var isBackCamera = true // Track current camera direction

    fun initializeCamera(
        context: Context,
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        cameraExecutor: ExecutorService,
        onQRCodeDetected: (String) -> Unit,
        onSuccess: (Camera) -> Unit,
        onError: (String) -> Unit,
        useBackCamera: Boolean = true
    ) {
        if (isInitialized) {
            Log.w("CameraManager", "Camera already initialized, cleaning up first")
            cleanup()
            // Add a small delay to ensure cleanup is complete
            Thread.sleep(100)
        }

        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            try {
                val cameraProvider = cameraProviderFuture.get()
                currentCameraProvider = cameraProvider

                Log.d("CameraManager", "ProcessCameraProvider obtained")

                // Update camera direction state
                isBackCamera = useBackCamera

                // Select camera based on parameter
                val cameraSelector = if (useBackCamera) {
                    CameraSelector.DEFAULT_BACK_CAMERA
                } else {
                    CameraSelector.DEFAULT_FRONT_CAMERA
                }

                // Check camera availability
                if (!cameraProvider.hasCamera(cameraSelector)) {
                    val cameraType = if (useBackCamera) "Back" else "Front"
                    onError("$cameraType camera not available")
                    return@addListener
                }

                // Unbind all previous use cases to prevent conflicts
                cameraProvider.unbindAll()
                Log.d("CameraManager", "Unbound all previous use cases")

                // Build use cases
                val preview = Preview.Builder()
                    .build()

                val imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .build()

                // Set up analyzer
                val qrAnalyzer = QRCodeAnalyzer(onQRCodeDetected)
                currentAnalyzer = qrAnalyzer
                imageAnalyzer.setAnalyzer(cameraExecutor, qrAnalyzer)

                // Bind use cases to lifecycle
                val camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageAnalyzer
                )

                currentCamera = camera

                // Connect preview to PreviewView
                preview.setSurfaceProvider(previewView.surfaceProvider)

                isInitialized = true
                Log.d("CameraManager", "Camera initialized successfully")
                onSuccess(camera)

            } catch (exc: Exception) {
                Log.e("CameraManager", "Camera initialization failed", exc)
                cleanup()
                onError("Camera initialization failed: ${exc.message}")
            }
        }, ContextCompat.getMainExecutor(context))
    }

    fun enableFlash(enabled: Boolean): Boolean {
        return try {
            currentCamera?.let { camera ->
                if (camera.cameraInfo.hasFlashUnit()) {
                    camera.cameraControl.enableTorch(enabled)
                    Log.d("CameraManager", "Flash ${if (enabled) "enabled" else "disabled"}")
                    true
                } else {
                    Log.w("CameraManager", "Flash unit not available")
                    false
                }
            } ?: false
        } catch (e: Exception) {
            Log.e("CameraManager", "Error controlling flash", e)
            false
        }
    }

    fun setZoomLevel(zoomRatio: Float): Boolean {
        return try {
            currentCamera?.let { camera ->
                val cameraInfo = camera.cameraInfo
                val minZoom = cameraInfo.zoomState.value?.minZoomRatio ?: 1f
                val maxZoom = cameraInfo.zoomState.value?.maxZoomRatio ?: 10f

                // Constrain zoom ratio to camera capabilities
                val constrainedZoom = zoomRatio.coerceIn(minZoom, maxZoom)

                camera.cameraControl.setZoomRatio(constrainedZoom)
                Log.d("CameraManager", "Zoom set to ${constrainedZoom}x (requested: ${zoomRatio}x, range: ${minZoom}x-${maxZoom}x)")
                true
            } ?: false
        } catch (e: Exception) {
            Log.e("CameraManager", "Error setting zoom level", e)
            false
        }
    }

    fun getZoomCapabilities(): Pair<Float, Float>? {
        return try {
            currentCamera?.let { camera ->
                val cameraInfo = camera.cameraInfo
                val minZoom = cameraInfo.zoomState.value?.minZoomRatio ?: 1f
                val maxZoom = cameraInfo.zoomState.value?.maxZoomRatio ?: 10f
                Pair(minZoom, maxZoom)
            }
        } catch (e: Exception) {
            Log.e("CameraManager", "Error getting zoom capabilities", e)
            null
        }
    }

    fun pauseCamera() {
        Log.d("CameraManager", "Pausing camera")
        try {
            // Clear analyzer first to stop processing
            currentAnalyzer?.cleanup()
            currentAnalyzer = null

            // Then unbind all use cases
            currentCameraProvider?.unbindAll()
            currentCamera = null

            Log.d("CameraManager", "Camera paused successfully")
        } catch (e: Exception) {
            Log.e("CameraManager", "Error pausing camera", e)
        }
    }

    fun cleanup() {
        Log.d("CameraManager", "Cleaning up camera resources")
        try {
            // Stop analyzer processing first
            currentAnalyzer?.cleanup()

            // Unbind all use cases from lifecycle
            currentCameraProvider?.unbindAll()

            // Clear all references
            currentCamera = null
            currentCameraProvider = null
            currentAnalyzer = null
            isInitialized = false

            Log.d("CameraManager", "Camera cleanup completed successfully")
        } catch (e: Exception) {
            Log.e("CameraManager", "Error during cleanup", e)
        }
    }

    fun isFlashAvailable(): Boolean {
        return currentCamera?.cameraInfo?.hasFlashUnit() ?: false
    }

    fun switchCamera(
        context: Context,
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        cameraExecutor: ExecutorService,
        onQRCodeDetected: (String) -> Unit,
        onSuccess: (Camera) -> Unit,
        onError: (String) -> Unit
    ) {
        val newCameraDirection = !isBackCamera
        Log.d("CameraManager", "Switching to ${if (newCameraDirection) "back" else "front"} camera")

        // Reinitialize camera with new direction
        initializeCamera(
            context = context,
            previewView = previewView,
            lifecycleOwner = lifecycleOwner,
            cameraExecutor = cameraExecutor,
            onQRCodeDetected = onQRCodeDetected,
            onSuccess = onSuccess,
            onError = onError,
            useBackCamera = newCameraDirection
        )
    }

    fun isUsingBackCamera(): Boolean {
        return isBackCamera
    }

    fun isFrontCameraAvailable(context: Context): Boolean {
        return try {
            val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
            val cameraProvider = cameraProviderFuture.get()
            cameraProvider.hasCamera(CameraSelector.DEFAULT_FRONT_CAMERA)
        } catch (e: Exception) {
            Log.e("CameraManager", "Error checking front camera availability", e)
            false
        }
    }

    fun getCameraState(): String {
        return when {
            !isInitialized -> "NOT_INITIALIZED"
            currentCamera == null -> "PAUSED"
            else -> "ACTIVE"
        }
    }

    /**
     * Force cleanup - used when app is being destroyed or surfaces are being destroyed
     */
    fun forceCleanup() {
        Log.d("CameraManager", "Force cleanup initiated")
        try {
            // Immediately stop all processing
            currentAnalyzer?.cleanup()

            // Force unbind without waiting
            currentCameraProvider?.unbindAll()

            // Clear all state immediately
            currentCamera = null
            currentCameraProvider = null
            currentAnalyzer = null
            isInitialized = false

            Log.d("CameraManager", "Force cleanup completed")
        } catch (e: Exception) {
            Log.e("CameraManager", "Error during force cleanup", e)
            // Even if there's an error, clear the state
            currentCamera = null
            currentCameraProvider = null
            currentAnalyzer = null
            isInitialized = false
        }
    }
}
