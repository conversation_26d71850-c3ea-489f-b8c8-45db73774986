package com.example.qr.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

// Extension property to create DataStore instance
private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "qr_spark_settings")

class SettingsDataStore(private val context: Context) {

    companion object {
        // Scanning preferences
        val AUTO_SAVE_SCANNED = booleanPreferencesKey("auto_save_scanned")
        val VIBRATE_ON_SCAN = booleanPreferencesKey("vibrate_on_scan")
        val PLAY_SOUND_ON_SCAN = booleanPreferencesKey("play_sound_on_scan")
        val AUTO_OPEN_LINKS = booleanPreferencesKey("auto_open_links")

        // Generation preferences
        val AUTO_SAVE_GENERATED = booleanPreferencesKey("auto_save_generated")
        val DEFAULT_QR_SIZE = stringPreferencesKey("default_qr_size")
        val DEFAULT_QR_FORMAT = stringPreferencesKey("default_qr_format")

        // UI preferences
        val THEME_MODE = stringPreferencesKey("theme_mode")
        val SHOW_TUTORIALS = booleanPreferencesKey("show_tutorials")
        val ENABLE_ANIMATIONS = booleanPreferencesKey("enable_animations")

        // Privacy preferences
        val ANALYTICS_ENABLED = booleanPreferencesKey("analytics_enabled")
        val CRASH_REPORTING_ENABLED = booleanPreferencesKey("crash_reporting_enabled")

        // Language preferences
        val SELECTED_LANGUAGE = stringPreferencesKey("selected_language")
        val FIRST_TIME_LANGUAGE_SELECTION = booleanPreferencesKey("first_time_language_selection")
    }

    // Scanning preferences
    val autoSaveScanned: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[AUTO_SAVE_SCANNED] ?: true
    }

    val vibrateOnScan: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[VIBRATE_ON_SCAN] ?: true
    }

    val playSoundOnScan: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[PLAY_SOUND_ON_SCAN] ?: false
    }

    val autoOpenLinks: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[AUTO_OPEN_LINKS] ?: true
    }

    // Generation preferences
    val autoSaveGenerated: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[AUTO_SAVE_GENERATED] ?: true
    }

    val defaultQRSize: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[DEFAULT_QR_SIZE] ?: "Medium"
    }

    val defaultQRFormat: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[DEFAULT_QR_FORMAT] ?: "PNG"
    }

    // UI preferences
    val themeMode: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[THEME_MODE] ?: "System"
    }

    val showTutorials: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[SHOW_TUTORIALS] ?: true
    }

    val enableAnimations: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[ENABLE_ANIMATIONS] ?: true
    }

    // Privacy preferences
    val analyticsEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[ANALYTICS_ENABLED] ?: false
    }

    val crashReportingEnabled: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[CRASH_REPORTING_ENABLED] ?: false
    }

    // Language preferences
    val selectedLanguage: Flow<String> = context.dataStore.data.map { preferences ->
        preferences[SELECTED_LANGUAGE] ?: "en" // Default to English
    }

    val isFirstTimeLanguageSelection: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[FIRST_TIME_LANGUAGE_SELECTION] ?: true
    }

    // Update functions
    suspend fun setAutoSaveScanned(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_SAVE_SCANNED] = enabled
        }
    }

    suspend fun setVibrateOnScan(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[VIBRATE_ON_SCAN] = enabled
        }
    }

    suspend fun setPlaySoundOnScan(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[PLAY_SOUND_ON_SCAN] = enabled
        }
    }

    suspend fun setAutoOpenLinks(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_OPEN_LINKS] = enabled
        }
    }

    suspend fun setAutoSaveGenerated(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[AUTO_SAVE_GENERATED] = enabled
        }
    }

    suspend fun setDefaultQRSize(size: String) {
        context.dataStore.edit { preferences ->
            preferences[DEFAULT_QR_SIZE] = size
        }
    }

    suspend fun setDefaultQRFormat(format: String) {
        context.dataStore.edit { preferences ->
            preferences[DEFAULT_QR_FORMAT] = format
        }
    }

    suspend fun setThemeMode(mode: String) {
        context.dataStore.edit { preferences ->
            preferences[THEME_MODE] = mode
        }
    }

    suspend fun setShowTutorials(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[SHOW_TUTORIALS] = enabled
        }
    }

    suspend fun setEnableAnimations(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[ENABLE_ANIMATIONS] = enabled
        }
    }

    suspend fun setAnalyticsEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[ANALYTICS_ENABLED] = enabled
        }
    }

    suspend fun setCrashReportingEnabled(enabled: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[CRASH_REPORTING_ENABLED] = enabled
        }
    }

    // Language update functions
    suspend fun setSelectedLanguage(languageCode: String) {
        context.dataStore.edit { preferences ->
            preferences[SELECTED_LANGUAGE] = languageCode
        }
        // Also save to SharedPreferences for immediate access in attachBaseContext
        val sharedPrefs = context.getSharedPreferences("datastore", Context.MODE_PRIVATE)
        sharedPrefs.edit().putString("selected_language", languageCode).apply()
    }

    suspend fun setFirstTimeLanguageSelection(isFirstTime: Boolean) {
        context.dataStore.edit { preferences ->
            preferences[FIRST_TIME_LANGUAGE_SELECTION] = isFirstTime
        }
    }

    // Get all settings as a single flow
    val allSettings: Flow<AppSettings> = context.dataStore.data.map { preferences ->
        AppSettings(
            autoSaveScanned = preferences[AUTO_SAVE_SCANNED] ?: true,
            vibrateOnScan = preferences[VIBRATE_ON_SCAN] ?: true,
            playSoundOnScan = preferences[PLAY_SOUND_ON_SCAN] ?: false,
            autoOpenLinks = preferences[AUTO_OPEN_LINKS] ?: true,
            autoSaveGenerated = preferences[AUTO_SAVE_GENERATED] ?: true,
            defaultQRSize = preferences[DEFAULT_QR_SIZE] ?: "Medium",
            defaultQRFormat = preferences[DEFAULT_QR_FORMAT] ?: "PNG",
            themeMode = preferences[THEME_MODE] ?: "System",
            showTutorials = preferences[SHOW_TUTORIALS] ?: true,
            enableAnimations = preferences[ENABLE_ANIMATIONS] ?: true,
            analyticsEnabled = preferences[ANALYTICS_ENABLED] ?: false,
            crashReportingEnabled = preferences[CRASH_REPORTING_ENABLED] ?: false,
            selectedLanguage = preferences[SELECTED_LANGUAGE] ?: "en",
            isFirstTimeLanguageSelection = preferences[FIRST_TIME_LANGUAGE_SELECTION] ?: true
        )
    }
}

data class AppSettings(
    val autoSaveScanned: Boolean = true,
    val vibrateOnScan: Boolean = true,
    val playSoundOnScan: Boolean = false,
    val autoOpenLinks: Boolean = true,
    val autoSaveGenerated: Boolean = true,
    val defaultQRSize: String = "Medium",
    val defaultQRFormat: String = "PNG",
    val themeMode: String = "System",
    val showTutorials: Boolean = true,
    val enableAnimations: Boolean = true,
    val analyticsEnabled: Boolean = false,
    val crashReportingEnabled: Boolean = false,
    val selectedLanguage: String = "en",
    val isFirstTimeLanguageSelection: Boolean = true
)
