package com.example.qr.data.repository

import android.content.Context
import android.util.Log
import com.example.qr.data.dao.QRCodeDao
import com.example.qr.data.database.QRSparkDatabase
import com.example.qr.data.entity.toQRCodeData
import com.example.qr.data.entity.toQRCodeEntity
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class PersistentQRRepository @Inject constructor(
    private val context: Context
) {
    private val database: QRSparkDatabase by lazy { QRSparkDatabase.getDatabase(context) }
    private val qrCodeDao: QRCodeDao by lazy { database.qrCodeDao() }
    
    companion object {
        private const val TAG = "PersistentQRRepository"
    }
    
    // Reactive flows for UI
    val qrCodes: Flow<List<QRCodeData>> = qrCodeDao.getAllQRCodes().map { entities ->
        entities.map { it.toQRCodeData() }
    }
    
    val scannedQRCodes: Flow<List<QRCodeData>> = qrCodeDao.getScannedQRCodes().map { entities ->
        entities.map { it.toQRCodeData() }
    }
    
    val generatedQRCodes: Flow<List<QRCodeData>> = qrCodeDao.getGeneratedQRCodes().map { entities ->
        entities.map { it.toQRCodeData() }
    }
    
    val favoriteQRCodes: Flow<List<QRCodeData>> = qrCodeDao.getFavoriteQRCodes().map { entities ->
        entities.map { it.toQRCodeData() }
    }
    
    // CRUD operations
    suspend fun addQRCode(qrCode: QRCodeData): Long {
        return try {
            val entity = qrCode.toQRCodeEntity()
            val id = qrCodeDao.insertQRCode(entity)
            Log.d(TAG, "QR Code added with ID: $id")
            id
        } catch (e: Exception) {
            Log.e(TAG, "Error adding QR code", e)
            throw e
        }
    }
    
    suspend fun updateQRCode(qrCode: QRCodeData) {
        try {
            val entity = qrCode.toQRCodeEntity()
            qrCodeDao.updateQRCode(entity)
            Log.d(TAG, "QR Code updated: ${qrCode.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating QR code", e)
            throw e
        }
    }
    
    suspend fun deleteQRCode(qrCode: QRCodeData) {
        try {
            val entity = qrCode.toQRCodeEntity()
            qrCodeDao.deleteQRCode(entity)
            Log.d(TAG, "QR Code deleted: ${qrCode.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting QR code", e)
            throw e
        }
    }
    
    suspend fun deleteQRCodeById(id: Long) {
        try {
            qrCodeDao.deleteQRCodeById(id)
            Log.d(TAG, "QR Code deleted by ID: $id")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting QR code by ID", e)
            throw e
        }
    }
    
    suspend fun toggleFavorite(id: Long) {
        try {
            qrCodeDao.toggleFavorite(id)
            Log.d(TAG, "Favorite toggled for QR Code: $id")
        } catch (e: Exception) {
            Log.e(TAG, "Error toggling favorite", e)
            throw e
        }
    }
    
    // Search functionality
    fun searchQRCodes(query: String): Flow<List<QRCodeData>> {
        return if (query.isBlank()) {
            qrCodes
        } else {
            qrCodeDao.searchQRCodes(query).map { entities ->
                entities.map { it.toQRCodeData() }
            }
        }
    }
    
    // Utility methods
    suspend fun getAllQRCodes(): List<QRCodeData> {
        return try {
            qrCodeDao.getAllQRCodesList().map { it.toQRCodeData() }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all QR codes", e)
            emptyList()
        }
    }
    
    suspend fun getQRCodeById(id: Long): QRCodeData? {
        return try {
            qrCodeDao.getQRCodeById(id)?.toQRCodeData()
        } catch (e: Exception) {
            Log.e(TAG, "Error getting QR code by ID", e)
            null
        }
    }
    
    suspend fun clearAll() {
        try {
            qrCodeDao.clearAll()
            Log.d(TAG, "All QR codes cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing all QR codes", e)
            throw e
        }
    }
    
    suspend fun deleteNonFavorites() {
        try {
            qrCodeDao.deleteNonFavorites()
            Log.d(TAG, "Non-favorite QR codes deleted")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting non-favorites", e)
            throw e
        }
    }
    
    // Statistics
    suspend fun getQRCodeCount(): Int = qrCodeDao.getQRCodeCount()
    suspend fun getFavoriteCount(): Int = qrCodeDao.getFavoriteCount()
    suspend fun getScannedCount(): Int = qrCodeDao.getScannedCount()
    suspend fun getGeneratedCount(): Int = qrCodeDao.getGeneratedCount()

    // Initialize with sample data (for first-time users)
    suspend fun initializeSampleData() {
        try {
            val count = getQRCodeCount()
            if (count == 0) {
                Log.d(TAG, "Initializing sample data...")

                val sampleData = listOf(
                    QRCodeData(
                        content = "https://www.google.com",
                        type = QRCodeType.URL,
                        format = "URL",
                        displayName = "Google",
                        createdAt = Date(),
                        isGenerated = false,
                        isFavorite = true
                    ),
                    QRCodeData(
                        content = "Hello World!",
                        type = QRCodeType.TEXT,
                        format = "TEXT",
                        displayName = "Hello World!",
                        createdAt = Date(System.currentTimeMillis() - 86400000),
                        isGenerated = true,
                        isFavorite = false
                    ),
                    QRCodeData(
                        content = "mailto:<EMAIL>",
                        type = QRCodeType.EMAIL,
                        format = "EMAIL",
                        displayName = "QR Spark Contact",
                        createdAt = Date(System.currentTimeMillis() - 172800000),
                        isGenerated = true,
                        isFavorite = true
                    )
                )

                sampleData.forEach { addQRCode(it) }
                Log.d(TAG, "Sample data initialized successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing sample data", e)
        }
    }
}
