package com.example.qr.ui.theme

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import com.google.android.gms.common.ConnectionResult
import com.google.android.gms.common.GoogleApiAvailability

/**
 * Font utilities for QR Spark
 * Provides smart font loading with fallbacks for offline scenarios
 */
object FontUtils {
    
    /**
     * Check if Google Play Services is available for font loading
     */
    @Composable
    fun isGooglePlayServicesAvailable(): Boolean {
        val context = LocalContext.current
        return remember {
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(context)
            resultCode == ConnectionResult.SUCCESS
        }
    }
    
    /**
     * Get the appropriate font family based on availability
     */
    @Composable
    fun getOptimalFontFamily(): FontFamily {
        // For now, always return safe font family to prevent crashes
        // TODO: Implement dynamic Poppins loading when Google Fonts are available
        return SafeFontFamily
    }
    
    /**
     * Get font weight with fallback for system fonts
     */
    fun getOptimalFontWeight(
        targetWeight: FontWeight,
        isGoogleFontsAvailable: Boolean
    ): FontWeight {
        return if (isGoogleFontsAvailable) {
            targetWeight
        } else {
            // Map to closest system font weights
            when (targetWeight) {
                FontWeight.Light -> FontWeight.Light
                FontWeight.Normal -> FontWeight.Normal
                FontWeight.Medium -> FontWeight.Medium
                FontWeight.SemiBold -> FontWeight.SemiBold
                FontWeight.Bold -> FontWeight.Bold
                FontWeight.ExtraBold -> FontWeight.Bold // Fallback to Bold
                else -> FontWeight.Normal
            }
        }
    }
    
    /**
     * Font loading strategy for different scenarios
     */
    enum class FontLoadingStrategy {
        GOOGLE_FONTS_PREFERRED,  // Try Google Fonts first, fallback to system
        SYSTEM_FONTS_ONLY,       // Use only system fonts
        AUTO_DETECT              // Automatically detect best option
    }
    
    /**
     * Apply font loading strategy
     */
    @Composable
    fun getFontFamilyForStrategy(strategy: FontLoadingStrategy): FontFamily {
        return when (strategy) {
            FontLoadingStrategy.GOOGLE_FONTS_PREFERRED -> SafeFontFamily
            FontLoadingStrategy.SYSTEM_FONTS_ONLY -> FallbackFontFamily
            FontLoadingStrategy.AUTO_DETECT -> getOptimalFontFamily()
        }
    }
}

/**
 * Composable extension for easy font family access
 */
@Composable
fun rememberOptimalFontFamily(): FontFamily {
    return FontUtils.getOptimalFontFamily()
}

/**
 * Font preloading utility for better performance
 */
object FontPreloader {
    
    /**
     * Preload fonts in background to improve first-time loading
     */
    fun preloadFonts() {
        // This would typically involve warming up the font cache
        // For Google Fonts, this happens automatically
        // For local fonts, we could preload them here
    }
    
    /**
     * Check if fonts are cached and ready
     */
    fun areFontsCached(): Boolean {
        // Implementation would check if fonts are available in cache
        return true // Simplified for now
    }
}
