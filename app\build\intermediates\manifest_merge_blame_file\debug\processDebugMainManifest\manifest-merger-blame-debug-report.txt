1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.qr.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Camera permission for QR code scanning -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:6:22-62
13
14    <!-- Vibration permission for haptic feedback -->
15    <uses-permission android:name="android.permission.VIBRATE" />
15-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:9:5-66
15-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:9:22-63
16
17    <!-- Camera hardware requirement -->
18    <uses-feature
18-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:12:5-84
19        android:name="android.hardware.camera"
19-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:12:19-57
20        android:required="true" />
20-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:12:58-81
21    <uses-feature
21-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:13:5-95
22        android:name="android.hardware.camera.autofocus"
22-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:13:19-67
23        android:required="false" />
23-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:13:68-92
24
25    <!-- Internet permission for URL handling -->
26    <uses-permission android:name="android.permission.INTERNET" />
26-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:16:5-67
26-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:16:22-64
27
28    <!-- Storage permissions for saving QR codes -->
29    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
29-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:19:5-80
29-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:19:22-77
30    <uses-permission
30-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:20:5-21:38
31        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
31-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:20:22-78
32        android:maxSdkVersion="28" />
32-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:21:9-35
33
34    <!-- For sharing QR codes -->
35    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
35-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:24:5-79
35-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:24:22-76
36
37    <permission
37-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960528b6fb019f37bcb262de24e0f0ad\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.example.qr.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960528b6fb019f37bcb262de24e0f0ad\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960528b6fb019f37bcb262de24e0f0ad\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.example.qr.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960528b6fb019f37bcb262de24e0f0ad\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960528b6fb019f37bcb262de24e0f0ad\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
42
43    <uses-feature
43-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:28:5-30:36
44        android:name="android.hardware.camera.front"
44-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:29:9-53
45        android:required="false" />
45-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:30:9-33
46    <uses-feature
46-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:36:5-38:36
47        android:name="android.hardware.camera.flash"
47-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:37:9-53
48        android:required="false" />
48-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:38:9-33
49    <uses-feature
49-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:39:5-41:36
50        android:name="android.hardware.screen.landscape"
50-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:40:9-57
51        android:required="false" />
51-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:41:9-33
52    <uses-feature
52-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:42:5-44:36
53        android:name="android.hardware.wifi"
53-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:43:9-45
54        android:required="false" />
54-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:44:9-33
55
56    <application
56-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:26:5-60:19
57        android:name="com.example.qr.QRApplication"
57-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:27:9-38
58        android:allowBackup="true"
58-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:28:9-35
59        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
59-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\960528b6fb019f37bcb262de24e0f0ad\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
60        android:dataExtractionRules="@xml/data_extraction_rules"
60-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:29:9-65
61        android:debuggable="true"
62        android:enableOnBackInvokedCallback="true"
62-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:36:9-51
63        android:extractNativeLibs="false"
64        android:fullBackupContent="@xml/backup_rules"
64-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:30:9-54
65        android:icon="@mipmap/ic_launcher"
65-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:31:9-43
66        android:label="@string/app_name"
66-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:32:9-41
67        android:roundIcon="@mipmap/ic_launcher_round"
67-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:33:9-54
68        android:supportsRtl="true"
68-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:34:9-35
69        android:testOnly="true"
70        android:theme="@style/Theme.QR" >
70-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:35:9-40
71        <activity
71-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:38:9-48:20
72            android:name="com.example.qr.MainActivity"
72-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:39:13-41
73            android:exported="true"
73-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:40:13-36
74            android:label="@string/app_name"
74-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:41:13-45
75            android:theme="@style/Theme.QR" >
75-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:42:13-44
76            <intent-filter>
76-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:43:13-47:29
77                <action android:name="android.intent.action.MAIN" />
77-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:44:17-69
77-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:44:25-66
78
79                <category android:name="android.intent.category.LAUNCHER" />
79-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:46:17-77
79-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:46:27-74
80            </intent-filter>
81        </activity>
82
83        <!-- FileProvider for sharing images -->
84        <provider
85            android:name="androidx.core.content.FileProvider"
85-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:52:13-62
86            android:authorities="com.example.qr.debug.fileprovider"
86-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:53:13-64
87            android:exported="false"
87-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:54:13-37
88            android:grantUriPermissions="true" >
88-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:55:13-47
89            <meta-data
89-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:56:13-58:54
90                android:name="android.support.FILE_PROVIDER_PATHS"
90-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:57:17-67
91                android:resource="@xml/file_paths" />
91-->C:\Users\<USER>\AndroidStudioProjects\QR\app\src\main\AndroidManifest.xml:58:17-51
92        </provider>
93
94        <service
94-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
95            android:name="androidx.camera.core.impl.MetadataHolderService"
95-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
96            android:enabled="false"
96-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
97            android:exported="false" >
97-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
98            <meta-data
98-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
99                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
99-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
100                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
100-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e22920afc5bf4bbcf5e7f876046ac78\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
101        </service>
102        <service
102-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcf9e24c99843fddc3881b1d66f420f2\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
103            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
103-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcf9e24c99843fddc3881b1d66f420f2\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
104            android:directBootAware="true"
104-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
105            android:exported="false" >
105-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcf9e24c99843fddc3881b1d66f420f2\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
106            <meta-data
106-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcf9e24c99843fddc3881b1d66f420f2\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
107                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
107-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcf9e24c99843fddc3881b1d66f420f2\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fcf9e24c99843fddc3881b1d66f420f2\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
109            <meta-data
109-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0345bb67b66f9821c40a5450826401\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
110                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
110-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0345bb67b66f9821c40a5450826401\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cc0345bb67b66f9821c40a5450826401\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
112            <meta-data
112-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
113                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
113-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
115        </service>
116
117        <provider
117-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
118            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
118-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
119            android:authorities="com.example.qr.debug.mlkitinitprovider"
119-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
120            android:exported="false"
120-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
121            android:initOrder="99" />
121-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f32ce1be52b8b7ebeebc7a68338ead1b\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
122
123        <activity
123-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5315b8dc79b8938d0c3fbf70f7121d3\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
124            android:name="com.google.android.gms.common.api.GoogleApiActivity"
124-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5315b8dc79b8938d0c3fbf70f7121d3\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
125            android:exported="false"
125-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5315b8dc79b8938d0c3fbf70f7121d3\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
126            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
126-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5315b8dc79b8938d0c3fbf70f7121d3\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
127
128        <meta-data
128-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e82f676cba4c5c8dbe66635de4eca18\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
129            android:name="com.google.android.gms.version"
129-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e82f676cba4c5c8dbe66635de4eca18\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
130            android:value="@integer/google_play_services_version" />
130-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0e82f676cba4c5c8dbe66635de4eca18\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
131
132        <activity
132-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06ebfcba0465ed60254ce34470bdd79f\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
133            android:name="androidx.compose.ui.tooling.PreviewActivity"
133-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06ebfcba0465ed60254ce34470bdd79f\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
134            android:exported="true" />
134-->[androidx.compose.ui:ui-tooling-android:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\06ebfcba0465ed60254ce34470bdd79f\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
135        <activity
135-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f786d1b931858c0f09ff349cd345e19\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:23:9-25:39
136            android:name="androidx.activity.ComponentActivity"
136-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f786d1b931858c0f09ff349cd345e19\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:24:13-63
137            android:exported="true" />
137-->[androidx.compose.ui:ui-test-manifest:1.7.8] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f786d1b931858c0f09ff349cd345e19\transformed\ui-test-manifest-1.7.8\AndroidManifest.xml:25:13-36
138
139        <provider
139-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
140            android:name="androidx.startup.InitializationProvider"
140-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
141            android:authorities="com.example.qr.debug.androidx-startup"
141-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
142            android:exported="false" >
142-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
143            <meta-data
143-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
144                android:name="androidx.emoji2.text.EmojiCompatInitializer"
144-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
145                android:value="androidx.startup" />
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b76da6d12c796cdac08083cbd15950e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
146            <meta-data
146-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29e27ae5efcad4a7de0551001ecf40d3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
147                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
147-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29e27ae5efcad4a7de0551001ecf40d3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
148                android:value="androidx.startup" />
148-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29e27ae5efcad4a7de0551001ecf40d3\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
149            <meta-data
149-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
150                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
150-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
151                android:value="androidx.startup" />
151-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
152        </provider>
153
154        <service
154-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c56c44833057a0a6542ab50af539d0d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
155            android:name="androidx.room.MultiInstanceInvalidationService"
155-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c56c44833057a0a6542ab50af539d0d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
156            android:directBootAware="true"
156-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c56c44833057a0a6542ab50af539d0d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
157            android:exported="false" />
157-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8c56c44833057a0a6542ab50af539d0d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
158
159        <receiver
159-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
160            android:name="androidx.profileinstaller.ProfileInstallReceiver"
160-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
161            android:directBootAware="false"
161-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
162            android:enabled="true"
162-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
163            android:exported="true"
163-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
164            android:permission="android.permission.DUMP" >
164-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
165            <intent-filter>
165-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
166                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
166-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
167            </intent-filter>
168            <intent-filter>
168-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
169                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
169-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
170            </intent-filter>
171            <intent-filter>
171-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
172                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
172-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
173            </intent-filter>
174            <intent-filter>
174-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
175                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
175-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\31f57b33051614bd67c1f1d26772a261\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
176            </intent-filter>
177        </receiver>
178
179        <service
179-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d991da6796abc1233ea498d02592962\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
180            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
180-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d991da6796abc1233ea498d02592962\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
181            android:exported="false" >
181-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d991da6796abc1233ea498d02592962\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
182            <meta-data
182-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d991da6796abc1233ea498d02592962\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
183                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
183-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d991da6796abc1233ea498d02592962\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
184                android:value="cct" />
184-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d991da6796abc1233ea498d02592962\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
185        </service>
186        <service
186-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
187            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
187-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
188            android:exported="false"
188-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
189            android:permission="android.permission.BIND_JOB_SERVICE" >
189-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
190        </service>
191
192        <receiver
192-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
193            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
193-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
194            android:exported="false" />
194-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8a69b068ba7117d7bb2339782679340e\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
195
196        <activity
196-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:47:9-53:63
197            android:name="com.journeyapps.barcodescanner.CaptureActivity"
197-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:48:13-74
198            android:clearTaskOnLaunch="true"
198-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:49:13-45
199            android:screenOrientation="sensorLandscape"
199-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:50:13-56
200            android:stateNotNeeded="true"
200-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:51:13-42
201            android:theme="@style/zxing_CaptureTheme"
201-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:52:13-54
202            android:windowSoftInputMode="stateAlwaysHidden" />
202-->[com.journeyapps:zxing-android-embedded:4.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64decd52adabcbfcda9e8791ac3c1aab\transformed\zxing-android-embedded-4.3.0\AndroidManifest.xml:53:13-60
203    </application>
204
205</manifest>
