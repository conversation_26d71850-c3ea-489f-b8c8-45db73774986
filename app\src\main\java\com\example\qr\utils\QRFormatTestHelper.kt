package com.example.qr.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import java.util.Date

/**
 * Test helper to verify QR format setting functionality
 */
object QRFormatTestHelper {
    
    private const val TAG = "QRFormatTestHelper"
    
    /**
     * Test the default QR format setting functionality
     */
    suspend fun testDefaultQRFormatSetting(context: Context) {
        Log.d(TAG, "=== Testing Default QR Format Setting ===")
        
        val settingsRepository = SettingsRepository(context)
        
        // Test 1: Check current default format
        val currentFormat = settingsRepository.defaultQRFormat.first()
        Log.d(TAG, "Current default format: $currentFormat")
        
        // Test 2: Test all format options
        val formatOptions = listOf("PNG", "JPEG", "PDF")
        formatOptions.forEach { format ->
            Log.d(TAG, "Format option available: $format")
        }
        
        // Test 3: Test QR generation with default format
        try {
            val testBitmap = QRGenerationService.generateQRBitmap(
                context = context,
                content = "Test QR Code for Format Testing",
                customization = null
            )
            
            if (testBitmap != null) {
                Log.d(TAG, "✅ QR bitmap generation successful")
                
                // Test saving with current format
                val testQRData = QRCodeData(
                    content = "Test QR Code for Format Testing",
                    type = QRCodeType.TEXT,
                    format = "TEXT",
                    displayName = "Test QR",
                    createdAt = Date(),
                    isGenerated = true,
                    isFavorite = false
                )
                
                val saveResult = ShareUtils.saveQRCodeInFormat(
                    context = context,
                    bitmap = testBitmap,
                    qrCodeData = testQRData,
                    format = currentFormat
                )
                
                when (saveResult) {
                    is ShareUtils.SaveResult.Success -> {
                        Log.d(TAG, "✅ QR saved successfully in $currentFormat format")
                        Log.d(TAG, "File: ${saveResult.fileName}")
                        Log.d(TAG, "Path: ${saveResult.path}")
                        
                        // Verify file extension matches format
                        val expectedExtension = when (currentFormat.uppercase()) {
                            "JPEG" -> ".jpg"
                            "PDF" -> ".pdf"
                            else -> ".png"
                        }
                        
                        if (saveResult.fileName.endsWith(expectedExtension, ignoreCase = true)) {
                            Log.d(TAG, "✅ File extension matches format: $expectedExtension")
                        } else {
                            Log.e(TAG, "❌ File extension mismatch! Expected: $expectedExtension, Got: ${saveResult.fileName}")
                        }
                    }
                    is ShareUtils.SaveResult.Error -> {
                        Log.e(TAG, "❌ QR save failed: ${saveResult.message}")
                    }
                }
            } else {
                Log.e(TAG, "❌ QR bitmap generation failed")
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error testing QR format", e)
        }
        
        Log.d(TAG, "=== QR Format Test Complete ===")
    }
    
    /**
     * Test changing the default format setting
     */
    suspend fun testFormatSettingChange(context: Context, newFormat: String) {
        Log.d(TAG, "=== Testing Format Setting Change to $newFormat ===")
        
        val settingsRepository = SettingsRepository(context)
        
        // Get current format
        val oldFormat = settingsRepository.defaultQRFormat.first()
        Log.d(TAG, "Old format: $oldFormat")
        
        // Change format
        settingsRepository.setDefaultQRFormat(newFormat)
        
        // Verify change
        val updatedFormat = settingsRepository.defaultQRFormat.first()
        Log.d(TAG, "Updated format: $updatedFormat")
        
        if (updatedFormat == newFormat) {
            Log.d(TAG, "✅ Format setting changed successfully!")
            
            // Test QR generation with new format
            testDefaultQRFormatSetting(context)
        } else {
            Log.e(TAG, "❌ Format setting change failed!")
        }
        
        Log.d(TAG, "=== Format Setting Change Test Complete ===")
    }
    
    /**
     * Test all format options
     */
    suspend fun testAllFormats(context: Context) {
        Log.d(TAG, "=== Testing All Format Options ===")
        
        val formats = listOf("PNG", "JPEG", "PDF")
        
        for (format in formats) {
            Log.d(TAG, "Testing format: $format")
            testFormatSettingChange(context, format)
            kotlinx.coroutines.delay(1000) // Small delay between tests
        }
        
        Log.d(TAG, "=== All Format Tests Complete ===")
    }
    
    /**
     * Test QRGenerationService format handling
     */
    suspend fun testQRGenerationServiceFormat(context: Context) {
        Log.d(TAG, "=== Testing QRGenerationService Format Handling ===")
        
        try {
            val result = QRGenerationService.generateCompleteQRCode(
                context = context,
                type = QRCodeType.TEXT,
                content = "Test QR for Service Format Test",
                customization = null,
                overrideFormat = null // Should use default format
            )
            
            when (result) {
                is QRGenerationResult.Success -> {
                    Log.d(TAG, "✅ QRGenerationService test successful")
                    Log.d(TAG, "Generated format: ${result.format}")
                    Log.d(TAG, "Saved to device: ${result.savedToDevice}")
                    Log.d(TAG, "File path: ${result.filePath}")
                    Log.d(TAG, "File name: ${result.fileName}")
                }
                is QRGenerationResult.Error -> {
                    Log.e(TAG, "❌ QRGenerationService test failed: ${result.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error testing QRGenerationService", e)
        }
        
        Log.d(TAG, "=== QRGenerationService Format Test Complete ===")
    }
}
