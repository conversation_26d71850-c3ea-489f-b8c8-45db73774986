<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">QR 扫描器 &amp; 生成器</string>

    <!-- Navigation -->
    <string name="nav_home">首页</string>
    <string name="nav_scanner">扫描器</string>
    <string name="nav_generator">生成器</string>
    <string name="nav_history">历史记录</string>

    <!-- Home Screen -->
    <string name="home_title">QR 应用</string>
    <string name="home_subtitle">您的终极 QR 伴侣</string>
    <string name="home_scan_title">扫描 QR</string>
    <string name="home_scan_description">即时解码 QR 码</string>
    <string name="home_generate_title">生成 QR</string>
    <string name="home_generate_description">创建您独特的代码</string>
    <string name="home_history_title">我的收藏</string>
    <string name="home_history_description">访问您的扫描和创作</string>

    <!-- Scanner Screen -->
    <string name="scanner_title">QR 扫描器</string>
    <string name="scanner_permission_title">需要相机权限</string>
    <string name="scanner_permission_message">此应用需要相机访问权限来扫描 QR 码。</string>
    <string name="scanner_grant_permission">授予权限</string>
    <string name="scanner_flash_on">闪光灯开启</string>
    <string name="scanner_flash_off">闪光灯关闭</string>

    <!-- Generator Screen -->
    <string name="generator_title">QR 生成器</string>
    <string name="generator_text_title">文本</string>
    <string name="generator_text_description">简单文本内容</string>
    <string name="generator_text_hint">在此输入您的文本...</string>
    <string name="generator_text_label">输入您的文本</string>
    <string name="generator_text_placeholder">写下您想分享的任何内容...</string>
    <string name="generator_text_content">文本内容</string>
    <string name="generator_url_title">网站</string>
    <string name="generator_url_description">URL 或网页链接</string>
    <string name="generator_url_hint">https://example.com</string>
    <string name="generator_wifi_title">WiFi</string>
    <string name="generator_wifi_description">WiFi 网络凭据</string>
    <string name="generator_email_title">电子邮件</string>
    <string name="generator_email_description">电子邮件地址</string>
    <string name="generator_email_hint"><EMAIL></string>
    <string name="generator_phone_title">电话</string>
    <string name="generator_phone_description">电话号码</string>
    <string name="generator_phone_hint">+86123456789</string>
    <string name="generator_sms_title">短信</string>
    <string name="generator_sms_description">文本消息</string>
    <string name="generator_contact_title">联系人</string>
    <string name="generator_contact_description">联系信息</string>
    <string name="generator_location_title">位置</string>
    <string name="generator_location_description">地理坐标</string>
    <string name="generator_generate_button">生成 QR 码</string>
    <string name="generator_select_type_desc">选择您要创建的 QR 码类型</string>
    <string name="generator_generating">正在生成 QR 码...</string>

    <!-- History Screen -->
    <string name="history_title">我的收藏</string>
    <string name="history_empty_title">还没有 QR 码</string>
    <string name="history_empty_message">扫描或生成 QR 码以在此查看</string>
    <string name="history_search_hint">搜索 QR 码...</string>
    <string name="history_tab_all">全部</string>
    <string name="history_tab_scanned">已扫描</string>
    <string name="history_tab_generated">已生成</string>
    <string name="history_tab_favorites">收藏</string>
    <string name="history_no_results">未找到结果</string>
    <string name="history_add_favorite">添加到收藏</string>

    <!-- Settings Screen -->
    <string name="settings_title">设置</string>
    <string name="settings_auto_save">自动保存</string>
    <string name="settings_auto_save_desc">自动保存扫描的 QR 码</string>
    <string name="settings_auto_save_generated">保存生成的代码</string>
    <string name="settings_auto_save_generated_desc">自动保存生成的 QR 码</string>
    <string name="settings_vibration">振动</string>
    <string name="settings_vibration_desc">扫描 QR 码时振动</string>
    <string name="settings_sound">声音</string>
    <string name="settings_sound_desc">扫描 QR 码时播放声音</string>
    <string name="settings_theme">主题</string>
    <string name="settings_theme_desc">选择应用主题</string>
    <string name="settings_language">语言</string>
    <string name="settings_language_desc">更改应用语言</string>
    <string name="settings_qr_size">QR 大小</string>
    <string name="settings_qr_size_desc">生成的 QR 码的默认大小</string>
    <string name="settings_qr_format">QR 格式</string>
    <string name="settings_qr_format_desc">保存的 QR 码的默认格式</string>

    <!-- QR Code Types -->
    <string name="qr_type_text">文本</string>
    <string name="qr_type_url">URL</string>
    <string name="qr_type_wifi">WiFi</string>
    <string name="qr_type_email">电子邮件</string>
    <string name="qr_type_phone">电话</string>
    <string name="qr_type_sms">短信</string>
    <string name="qr_type_contact">联系人</string>
    <string name="qr_type_location">位置</string>
    <string name="qr_type_other">其他</string>

    <!-- Common Actions -->
    <string name="action_copy">复制</string>
    <string name="action_share">分享</string>
    <string name="action_save">保存</string>
    <string name="action_delete">删除</string>
    <string name="action_edit">编辑</string>
    <string name="action_favorite">收藏</string>
    <string name="action_cancel">取消</string>
    <string name="action_ok">确定</string>
    <string name="action_continue">继续</string>
    <string name="action_apply">应用</string>
    <string name="action_close">关闭</string>
    <string name="action_done">完成</string>
    <string name="action_add_to_favorites">添加到收藏</string>
    <string name="action_favorited">已收藏</string>
    <string name="action_saving">保存中...</string>
    <string name="action_saved">已保存</string>
    <string name="action_failed">失败</string>
    <string name="action_sharing">分享中...</string>
    <string name="action_shared">已分享！</string>
    <string name="action_copying">复制中...</string>
    <string name="action_copied">已复制！</string>
    <string name="action_loading">加载中...</string>

    <!-- Messages -->
    <string name="message_copied">已复制到剪贴板</string>
    <string name="message_saved">保存成功</string>
    <string name="message_deleted">删除成功</string>
    <string name="message_error">发生错误</string>
    <string name="message_no_camera">相机不可用</string>
    <string name="message_invalid_qr">无效的 QR 码</string>
    <string name="message_url_copied">URL 已复制到剪贴板</string>
    <string name="message_text_copied">文本已复制到剪贴板</string>
    <string name="message_qr_type_not_supported">QR 码类型尚不支持</string>
    <string name="message_failed_qr_action">处理 QR 码操作失败：%s</string>
    <string name="message_content_copied">内容已复制到剪贴板</string>
    <string name="message_qr_saved_gallery">QR 码已保存到图片/QR Spark</string>
    <string name="message_failed_save_qr">保存 QR 码失败</string>

    <!-- Status -->
    <string name="status_generated">已生成</string>
    <string name="status_scanned">已扫描</string>
    <string name="status_saved">已保存到收藏</string>

    <!-- Permissions -->
    <string name="permission_camera_required">需要相机权限</string>
    <string name="permission_camera_denied">相机权限被拒绝</string>
    <string name="permission_camera_message">扫描 QR 码需要相机访问权限。请授予相机权限以继续。</string>
    <string name="permission_grant">授予权限</string>
    <string name="permission_try_again">重试</string>

    <!-- Language Selection -->
    <string name="language_welcome_title">欢迎使用 QR Spark！</string>
    <string name="language_welcome_subtitle">选择您的首选语言开始使用</string>
    <string name="language_select_title">选择语言</string>
    <string name="language_current">当前语言</string>

    <!-- Status Messages -->
    <string name="status_auto_saved">自动保存到收藏</string>
    <string name="status_favorited">已添加到收藏</string>
    <string name="status_not_saved">未保存（自动保存已禁用）</string>
    <string name="status_loading">加载中...</string>
    <string name="status_done">完成</string>
    <string name="status_failed">失败</string>

    <!-- Generator Forms Section -->
    <string name="form_wifi_network">WiFi网络</string>
    <string name="form_network_name">网络名称(SSID)</string>
    <string name="form_password">密码</string>
    <string name="form_security_type">安全类型</string>
    <string name="form_hidden_network">隐藏网络</string>
    <string name="form_email_message">邮件内容</string>
    <string name="form_email_address">邮件地址</string>
    <string name="form_email_placeholder"><EMAIL></string>
    <string name="form_subject">主题</string>
    <string name="form_message_body">消息正文</string>
    <string name="form_phone_number">电话号码</string>
    <string name="form_sms_message">短信内容</string>
    <string name="form_contact_info">联系人信息</string>
    <string name="form_first_name">名字</string>
    <string name="form_last_name">姓氏</string>
    <string name="form_organization">组织</string>
    <string name="form_location_coordinates">位置坐标</string>
    <string name="form_latitude">纬度</string>
    <string name="form_longitude">经度</string>
    <string name="form_url_address">URL地址</string>
    <string name="form_url_placeholder">https://example.com</string>

    <!-- Generator Result Dialog -->
    <string name="qr_generated_title">QR 码已生成！</string>
    <string name="qr_generated_success">您的 QR 码已成功生成</string>
    <string name="qr_generated_save_gallery">保存到相册</string>
    <string name="qr_generated_share_image">分享图片</string>
    <string name="qr_generated_regenerate">重新生成</string>
    <string name="qr_generated_customize">自定义</string>

    <!-- Scanner Result Dialog -->
    <string name="qr_scanned_title">QR 码已扫描！</string>
    <string name="qr_scanned_success">QR 码已成功扫描</string>

    <!-- Settings Screen Extended -->
    <string name="settings_scanning">扫描设置</string>
    <string name="settings_auto_save_scanned">自动保存扫描结果</string>
    <string name="settings_auto_save_scanned_desc">自动将扫描的二维码保存到收藏</string>
    <string name="settings_vibrate_on_scan">扫描时振动</string>
    <string name="settings_vibrate_on_scan_desc">检测到二维码时振动</string>
    <string name="settings_play_sound_on_scan">扫描时播放声音</string>
    <string name="settings_play_sound_on_scan_desc">检测到二维码时播放声音</string>
    <string name="settings_auto_open_links">自动打开链接</string>
    <string name="settings_auto_open_links_desc">自动在浏览器中打开扫描的URL</string>

    <string name="settings_generation">生成设置</string>
    <string name="settings_default_qr_size_desc">设置生成二维码的默认大小</string>
    <string name="settings_default_qr_format_desc">设置生成二维码的默认格式</string>

    <string name="settings_appearance">外观</string>
    <string name="settings_theme_mode">主题模式</string>
    <string name="settings_theme_mode_desc">选择浅色、深色或系统主题</string>
    <string name="settings_enable_animations">启用动画</string>
    <string name="settings_enable_animations_desc">在界面中显示动画</string>

    <string name="settings_privacy">隐私</string>
    <string name="settings_analytics">分析</string>
    <string name="settings_analytics_desc">通过分享匿名使用数据帮助改进应用</string>
    <string name="settings_crash_reporting">崩溃报告</string>
    <string name="settings_crash_reporting_desc">自动发送崩溃报告以帮助改进应用</string>

    <string name="settings_about">关于</string>
    <string name="settings_app_version">应用版本</string>
    <string name="settings_app_version_desc">QR Spark v1.0.0 • 点击复制</string>
    <string name="settings_share_app">分享QR Spark</string>
    <string name="settings_share_app_desc">向朋友推荐QR Spark</string>

    <!-- Additional Settings Options -->
    <string name="settings_show_tutorials">显示教程</string>
    <string name="settings_show_tutorials_desc">显示有用的提示和教程</string>
    <string name="settings_reset_all">恢复默认设置</string>
    <string name="settings_reset_all_desc">将所有设置恢复为默认值</string>
    <string name="settings_reset_dialog_title">恢复所有设置</string>
    <string name="settings_reset_dialog_message">这将恢复所有设置为默认值:</string>
    <string name="settings_reset_dialog_warning">此操作无法撤销。</string>
    <string name="settings_reset_items_1">• 自动保存偏好</string>
    <string name="settings_reset_items_2">• 振动和声音设置</string>
    <string name="settings_reset_items_3">• 默认二维码大小和格式</string>
    <string name="settings_reset_items_4">• 界面和动画偏好</string>
    <string name="settings_reset_confirm">恢复</string>
    <string name="settings_restore">恢复</string>
    <string name="settings_app_settings_title">应用设置</string>
    <string name="settings_app_settings_subtitle">个性化您的QR Spark体验</string>
    <string name="settings_rate_app">评价QR Spark</string>
    <string name="settings_rate_app_desc">通过反馈帮助我们改进</string>
    <string name="settings_size_dialog_title">选择二维码大小</string>
    <string name="settings_format_dialog_title">选择二维码格式</string>

    <!-- Size and Format Options -->
    <string name="size_small">小</string>
    <string name="size_medium">中</string>
    <string name="size_large">大</string>
    <string name="size_extra_large">特大</string>
    <string name="format_png">PNG</string>
    <string name="format_jpeg">JPEG</string>
    <string name="format_svg">SVG</string>

    <!-- Navigation Titles -->
    <string name="nav_title_home">QR Spark</string>
    <string name="nav_title_scanner">扫描 QR</string>
    <string name="nav_title_generator">生成 QR</string>
    <string name="nav_title_history">我的收藏</string>
    <string name="nav_title_settings">设置</string>
    <string name="nav_title_help">帮助 &amp; 常见问题</string>

    <!-- Help & FAQ Screen -->
    <string name="help_title">帮助 &amp; 常见问题</string>
    <string name="help_subtitle">您的终极 QR 伴侣</string>
    <string name="help_faq_title">常见问题</string>
    <string name="help_faq_subtitle">查找关于 QR Spark 的常见问题答案</string>
    <string name="help_need_more_title">需要更多帮助？</string>
    <string name="help_need_more_subtitle">如果您找不到所需的答案，请随时联系我们的支持团队。</string>
    <string name="help_contact_support">联系支持</string>

    <!-- FAQ Questions -->
    <string name="faq_scan_question">如何扫描 QR 码？</string>
    <string name="faq_scan_answer">在主屏幕上点击"扫描 QR"，将相机对准 QR 码，它将自动检测和处理。</string>

    <string name="faq_create_question">如何创建 QR 码？</string>
    <string name="faq_create_answer">在主屏幕上点击"生成 QR"，选择要创建的 QR 码类型，填写所需信息，然后点击"生成 QR 码"。</string>

    <string name="faq_saved_question">我的 QR 码保存在哪里？</string>
    <string name="faq_saved_answer">您所有扫描和生成的 QR 码都会自动保存在"我的收藏"中。您可以随时从主屏幕访问它们。</string>

    <string name="faq_favorites_question">如何将 QR 码标记为收藏？</string>
    <string name="faq_favorites_answer">查看 QR 码时，点击心形图标将其添加到收藏。收藏的 QR 码会出现在您收藏的顶部。</string>

    <string name="faq_share_question">我可以分享 QR 码吗？</string>
    <string name="faq_share_answer">是的！查看 QR 码时，点击分享按钮将 QR 码图片或其内容分享给其他应用。</string>

    <string name="faq_types_question">我可以创建哪些类型的 QR 码？</string>
    <string name="faq_types_answer">您可以为文本、URL、WiFi 凭据、电子邮件地址、电话号码、短信、联系人和位置创建 QR 码。</string>

    <!-- Security Options -->
    <string name="security_wpa">WPA</string>
    <string name="security_wpa2">WPA2</string>
    <string name="security_wep">WEP</string>
    <string name="security_none">无</string>

    <!-- Theme Options -->
    <string name="theme_light">浅色</string>
    <string name="theme_dark">深色</string>
    <string name="theme_system">系统</string>

    <!-- Camera Permission -->
    <string name="camera_permission_denied">相机权限被拒绝</string>
    <string name="camera_access_required">扫描 QR 码需要相机访问权限。请在应用设置中启用相机权限。</string>
    <string name="try_again">重试</string>

    <!-- Missing Common Actions -->
    <string name="back">返回</string>
    <string name="search">搜索</string>
    <string name="action_open_website">打开网站</string>
    <string name="action_connect_wifi">连接WiFi</string>
    <string name="action_send_email">发送邮件</string>
    <string name="action_call">拨打电话</string>
    <string name="action_send_sms">发送短信</string>
    <string name="action_add_contact">添加联系人</string>
    <string name="action_open_maps">在地图中打开</string>
    <string name="action_add_calendar">添加到日历</string>
    <string name="action_copy_text">复制文本</string>

    <!-- Generator Related Missing -->
    <string name="generate_text">生成文本</string>
    <string name="plain_text_content">纯文本内容</string>
    <string name="text_content">文本内容</string>
    <string name="enter_your_text">输入您的文本</string>
    <string name="type_anything_you_want_to_share">输入您想分享的任何内容...</string>
    <string name="select_the_type_of_qr_code">选择您要创建的二维码类型</string>
    <string name="web_url_or_link">网址或链接</string>
    <string name="wifi_network_credentials">WiFi网络凭据</string>
    <string name="email_address">电子邮件地址</string>
    <string name="phone_number">电话号码</string>
    <string name="text_message">短信</string>

    <!-- Scanner Related Missing -->
    <string name="scanner_try_again">重试</string>
    <string name="scanner_smart_scan">智能扫描</string>
    <string name="scanner_upload_qr_image">上传二维码图片</string>
    <string name="scanner_point_camera">将相机对准二维码进行扫描</string>
    <string name="scanner_qr_scanned">二维码已扫描！</string>
    <string name="scanner_website">网站</string>
    <string name="scanner_open_website">打开网站</string>
    <string name="scanner_add_to_favorites">添加到收藏</string>

    <!-- Generator Screen Extended Missing -->
    <string name="generator_qr_generated">二维码已生成！</string>
    <string name="generator_save_to_gallery">保存到相册</string>
    <string name="generator_add_to_favorites">添加到收藏</string>
    <string name="generator_close">关闭</string>
    <string name="generator_generate_text">生成文本</string>
    <string name="generator_plain_text_content">纯文本内容</string>

    <!-- QR Code Details Missing -->
    <string name="qr_characters">%d 个字符</string>
    <string name="qr_type_url_caps">URL</string>

    <!-- Error Messages -->
    <string name="error_content_empty">内容不能为空</string>
    <string name="error_failed_favorite">更新收藏失败: %s</string>
    <string name="error_invalid_email">请输入有效的电子邮件地址</string>
    <string name="error_invalid_phone">请输入有效的电话号码</string>
    <string name="error_invalid_url">请输入有效的URL</string>
    <string name="error_invalid_coordinates">请输入有效的坐标</string>

    <!-- History and Search -->
    <string name="bulk_actions">批量操作</string>
    <string name="delete_all">全部删除</string>
    <string name="delete_non_favorites">删除非收藏</string>

    <!-- QR Code Result Dialog -->
    <string name="qr_result_title">二维码结果</string>
    <string name="qr_result_content">内容</string>
    <string name="qr_result_type">类型</string>
    <string name="qr_result_created">创建时间</string>
    <string name="qr_result_auto_saved">自动保存</string>
    <string name="qr_result_not_saved">未保存</string>
    <string name="qr_result_favorite_added">已添加到收藏</string>
    <string name="qr_result_favorite_removed">已从收藏移除</string>

    <!-- Save and Share Dialogs -->
    <string name="save_dialog_title">保存二维码</string>
    <string name="save_dialog_message">您想将此二维码保存到相册吗？</string>
    <string name="save_dialog_success">二维码保存成功</string>
    <string name="save_dialog_error">保存二维码失败</string>

    <string name="share_dialog_title">分享二维码</string>
    <string name="share_dialog_image">分享为图片</string>
    <string name="share_dialog_text">分享为文本</string>
    <string name="share_dialog_both">两者都分享</string>



    <!-- Share and Support Messages -->
    <string name="share_app_subject">发现QR Spark！</string>
    <string name="share_app_message">🌟 QR Spark - 您的终极二维码伴侣！ 🌟\n\n我正在使用QR Spark，它非常棒！您可以：\n\n📱 即时扫描二维码\n✨ 创建个性化二维码\n💾 保存和组织您的收藏\n❤️ 标记收藏以便快速访问\n🎨 美观现代的设计\n\n下载QR Spark，体验二维码管理的未来！\n\n#QRSpark #二维码 #移动应用</string>
    <string name="share_qr_subject">从QR Spark分享</string>
    <string name="share_qr_message" formatted="false">📱 从QR Spark分享\n\n内容 %s:\n%s\n\n使用QR Spark生成 - 您的终极二维码伴侣！</string>
    <string name="share_qr_content">分享二维码内容</string>
    <string name="share_qr_image_message" formatted="false">🌟 来自QR Spark的二维码\n\n📱 类型: %s\n📄 内容: %s\n\n✨ 使用QR Spark生成 - 您的终极二维码伴侣！\n#QRSpark #二维码</string>
    <string name="contact_support">联系支持</string>
    <string name="support_request_subject">QR Spark支持请求</string>
    <string name="support_request_message" formatted="false">QR Spark支持请求\n\n我需要关于QR Spark应用的帮助。\n\n请联系: <EMAIL>\n\n应用版本: 1.0.0\n设备: %s\nAndroid版本: %s</string>

    <!-- Missing Essential Strings -->
    <string name="format_pdf">PDF</string>
    <string name="qr_type_calendar">日历</string>
    <string name="settings_privacy_policy">隐私政策</string>
    <string name="settings_privacy_policy_desc">查看我们的隐私政策</string>
    <string name="close">关闭</string>
    <string name="save">保存</string>
    <string name="cancel">取消</string>
    <string name="ok">确定</string>
    <string name="copy">复制</string>
    <string name="delete">删除</string>
    <string name="share">分享</string>
    <string name="more_options">更多选项</string>
    <string name="history_empty">还没有QR码</string>
    <string name="history_remove_favorite">从收藏中移除</string>
    <string name="scanner_permission_required">需要相机权限</string>
    <string name="scanner_permission_rationale">此应用需要相机权限来扫描二维码。请授予相机权限以继续。</string>
    <string name="scanner_permission_denied">相机权限被拒绝</string>
    <string name="scanner_permission_settings">扫描二维码需要相机权限。请在应用设置中启用相机权限。</string>
    <string name="generator_select_type">选择二维码类型</string>
    <string name="select_type">选择类型</string>
    <string name="text">文本</string>
    <string name="website">网站</string>
    <string name="wifi">WiFi</string>
    <string name="email">电子邮件</string>
    <string name="phone">电话</string>
    <string name="sms">短信</string>

    <!-- Additional Missing Settings Strings -->
    <string name="settings_default_qr_size">默认QR码大小</string>
    <string name="settings_default_qr_format">默认QR格式</string>
    <string name="search_qr_codes">搜索QR码...</string>

    <!-- Missing Action Strings -->
    <string name="action_finish">完成</string>
    <string name="action_get_started">开始</string>
    <string name="action_skip">跳过</string>

    <!-- Tutorial Strings -->
    <string name="tutorial_welcome_title">欢迎使用 QR Spark！</string>
    <string name="tutorial_welcome_description">您的一体化二维码解决方案。即时扫描，轻松生成，轻松管理。</string>
    <string name="tutorial_scanning_title">即时扫描</string>
    <string name="tutorial_scanning_description">用相机或从图片扫描二维码。即时识别节省时间。</string>
    <string name="tutorial_generation_title">轻松生成</string>
    <string name="tutorial_generation_description">为文本、网址、WiFi和联系人创建自定义二维码。轻松分享信息。</string>
    <string name="tutorial_management_title">智能管理</string>
    <string name="tutorial_management_description">访问您完整的二维码历史记录，包含收藏和搜索。永不丢失重要代码。</string>

    <!-- Scanner Tutorial Strings -->
    <string name="tutorial_scanner_camera_title">即时相机扫描</string>
    <string name="tutorial_scanner_camera_description">将相机对准任何二维码即可即时识别。无需按钮。</string>
    <string name="tutorial_scanner_upload_title">从图片扫描</string>
    <string name="tutorial_scanner_upload_description">从相册上传照片以扫描保存图片中的二维码。</string>
    <string name="tutorial_scanner_controls_title">智能控制</string>
    <string name="tutorial_scanner_controls_description">使用闪光灯、缩放和相机切换在任何条件下完美扫描。</string>

    <!-- Generator Tutorial Strings -->
    <string name="tutorial_generator_type_title">选择您的类型</string>
    <string name="tutorial_generator_type_description">从文本、网址、WiFi、联系人等中选择。每种类型都针对其用途进行了优化。</string>
    <string name="tutorial_generator_content_title">添加您的内容</string>
    <string name="tutorial_generator_content_description">填写您的信息。智能表单使其快速且无错误。</string>
    <string name="tutorial_generator_share_title">生成和分享</string>
    <string name="tutorial_generator_share_description">即时创建您的二维码。一键保存、分享或打印。</string>

    <!-- History Tutorial Strings -->
    <string name="tutorial_history_collection_title">您的二维码收藏</string>
    <string name="tutorial_history_collection_description">所有扫描和生成的代码都在一个地方。再也不会丢失重要信息。</string>
    <string name="tutorial_history_organization_title">智能组织</string>
    <string name="tutorial_history_organization_description">标记收藏、按内容搜索、按类型筛选以即时访问。</string>
    <string name="tutorial_history_actions_title">快速操作</string>
    <string name="tutorial_history_actions_description">点击任何代码进行即时操作：分享、复制或直接打开链接。</string>

    <!-- Content Descriptions -->
    <string name="content_description_more_info">更多信息</string>

    <!-- Format Descriptions -->
    <string name="format_png_description">高质量，无损压缩</string>
    <string name="format_jpeg_description">文件大小更小，适合分享</string>
    <string name="format_svg_description">矢量格式，可缩放</string>
    <string name="format_pdf_description">文档格式，可打印</string>

    <!-- Settings Info Dialog Titles -->
    <string name="info_auto_save_scanned_title">自动保存扫描的二维码</string>
    <string name="info_sound_feedback_title">声音反馈</string>
    <string name="info_tutorial_guidance_title">教程指导</string>
    <string name="info_ui_animations_title">界面动画</string>
    <string name="info_haptic_feedback_title">触觉反馈</string>
    <string name="info_auto_open_links_title">自动打开链接</string>
    <string name="info_auto_save_generated_title">自动保存生成的二维码</string>

    <!-- Settings Info Dialog Descriptions -->
    <string name="info_auto_save_scanned_description">启用后，您扫描的所有二维码将自动保存到您的收藏中，以便稍后轻松访问。您始终可以在历史记录部分管理已保存的代码。</string>
    <string name="info_sound_feedback_description">成功检测到二维码时播放悦耳的声音。这提供了扫描成功的听觉确认。声音遵循您设备的音量设置。</string>
    <string name="info_tutorial_guidance_description">首次使用功能时显示有用的教程覆盖和入门指南。这些教程解释如何有效使用应用程序，可以随时跳过。</string>
    <string name="info_ui_animations_description">控制整个应用程序中的平滑过渡、加载动画和视觉效果。禁用动画可以提高旧设备的性能，并帮助偏好减少运动的用户。</string>
    <string name="info_haptic_feedback_description">成功扫描二维码时提供轻微振动。这给您触觉确认您的扫描已被检测到，在嘈杂环境中特别有用。</string>
    <string name="info_auto_open_links_description">启用后，二维码中找到的网址将自动在您的浏览器中打开。禁用时，您将首先看到预览，可以选择是否打开链接以获得更好的安全性。</string>
    <string name="info_auto_save_generated_description">自动保存您创建的二维码到您的设备和收藏中。这确保您不会丢失生成的代码，可以稍后轻松访问以进行分享或打印。</string>

    <!-- Action Buttons -->
    <string name="action_test_sound">测试声音</string>

    <!-- Tab Names -->
    <string name="tab_all">全部</string>
    <string name="tab_scanned">已扫描</string>
    <string name="tab_generated">已生成</string>
    <string name="tab_favorites">收藏</string>

    <!-- Shorter Tab Names for Better Multilingual Support -->
    <string name="tab_all_short">全部</string>
    <string name="tab_scanned_short">扫描</string>
    <string name="tab_generated_short">生成</string>
    <string name="tab_favorites_short">收藏</string>

    <!-- History Screen Headers -->
    <string name="history_title_my">我的</string>
    <string name="history_title_collection">收藏</string>

    <!-- Bulk Actions -->
    <string name="bulk_action_delete_all">删除全部</string>
    <string name="bulk_action_delete_non_favorites">删除非收藏</string>
    <string name="bulk_action_more_options">更多选项</string>

    <!-- Search -->
    <string name="search_placeholder">搜索二维码...</string>
    <string name="search_close">关闭搜索</string>
    <string name="search_open">搜索</string>

    <!-- Content Descriptions -->
    <string name="cd_back">返回</string>
    <string name="cd_search">搜索</string>
    <string name="cd_close_search">关闭搜索</string>
    <string name="cd_more_options">更多选项</string>
    <string name="cd_add_to_favorites">添加到收藏</string>
    <string name="cd_remove_from_favorites">从收藏中移除</string>
    <string name="cd_delete_qr_code">删除二维码</string>

    <!-- Scanner Screen -->
    <string name="scanner_instruction">将相机对准二维码进行扫描</string>
    <string name="scanner_upload_image">上传二维码图片</string>
    <string name="cd_upload_qr_image">上传二维码图片</string>

</resources>
