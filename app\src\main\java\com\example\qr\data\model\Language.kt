package com.example.qr.data.model

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Language
import androidx.compose.ui.graphics.vector.ImageVector
import java.util.Locale

data class Language(
    val code: String,
    val name: String,
    val nativeName: String,
    val flag: String,
    val locale: Locale
) {
    companion object {
        fun getSupportedLanguages(): List<Language> = listOf(
            Language(
                code = "en",
                name = "English",
                nativeName = "English",
                flag = "🇺🇸",
                locale = Locale.ENGLISH
            ),
            Language(
                code = "es",
                name = "Spanish",
                nativeName = "Español",
                flag = "🇪🇸",
                locale = Locale("es", "ES")
            ),
            Language(
                code = "fr",
                name = "French",
                nativeName = "Français",
                flag = "🇫🇷",
                locale = Locale.FRANCE
            ),
            Language(
                code = "de",
                name = "German",
                nativeName = "Deutsch",
                flag = "🇩🇪",
                locale = Locale.GERMANY
            ),
            Language(
                code = "it",
                name = "Italian",
                nativeName = "Italiano",
                flag = "🇮🇹",
                locale = Locale.ITALY
            ),
            Language(
                code = "pt",
                name = "Portuguese",
                nativeName = "Português",
                flag = "🇵🇹",
                locale = Locale("pt", "PT")
            ),
            Language(
                code = "ru",
                name = "Russian",
                nativeName = "Русский",
                flag = "🇷🇺",
                locale = Locale("ru", "RU")
            ),
            Language(
                code = "zh",
                name = "Chinese",
                nativeName = "中文",
                flag = "🇨🇳",
                locale = Locale.CHINESE
            ),
            Language(
                code = "ja",
                name = "Japanese",
                nativeName = "日本語",
                flag = "🇯🇵",
                locale = Locale.JAPANESE
            ),
            Language(
                code = "ko",
                name = "Korean",
                nativeName = "한국어",
                flag = "🇰🇷",
                locale = Locale.KOREAN
            ),
            Language(
                code = "ar",
                name = "Arabic",
                nativeName = "العربية",
                flag = "🇸🇦",
                locale = Locale("ar", "SA")
            ),
            Language(
                code = "hi",
                name = "Hindi",
                nativeName = "हिन्दी",
                flag = "🇮🇳",
                locale = Locale("hi", "IN")
            )
        )

        fun getLanguageByCode(code: String): Language? {
            return getSupportedLanguages().find { it.code == code }
        }

        fun getSystemLanguage(): Language {
            val systemLocale = Locale.getDefault()
            return getSupportedLanguages().find { 
                it.locale.language == systemLocale.language 
            } ?: getSupportedLanguages().first() // Default to English
        }
    }
}
