package com.example.qr.utils

import android.content.Context
import android.util.Log
import com.example.qr.data.preferences.SettingsDataStore
import kotlinx.coroutines.flow.first
import java.text.SimpleDateFormat
import java.util.*

/**
 * Manages privacy consent and policy compliance for QR Spark
 */
class PrivacyManager(private val context: Context) {
    
    private val settingsDataStore = SettingsDataStore(context)
    
    companion object {
        private const val TAG = "PrivacyManager"
        const val CURRENT_PRIVACY_POLICY_VERSION = "1.0"
        
        // Privacy policy version history
        private val PRIVACY_POLICY_VERSIONS = mapOf(
            "1.0" to "January 2025 - Initial privacy policy"
        )
    }
    
    /**
     * Check if user needs to see privacy policy (first time or version update)
     */
    suspend fun shouldShowPrivacyPolicy(): Boolean {
        return try {
            val isFirstTime = settingsDataStore.isFirstTimePrivacyShown.first()
            val currentVersion = settingsDataStore.privacyPolicyVersion.first()
            val isAccepted = settingsDataStore.privacyPolicyAccepted.first()
            
            Log.d(TAG, "Privacy check - First time: $isFirstTime, Current version: $currentVersion, Accepted: $isAccepted")
            
            // Show if first time OR version changed OR not accepted
            isFirstTime || currentVersion != CURRENT_PRIVACY_POLICY_VERSION || !isAccepted
        } catch (e: Exception) {
            Log.e(TAG, "Error checking privacy policy status", e)
            true // Show by default if error
        }
    }
    
    /**
     * Accept privacy policy with current version
     */
    suspend fun acceptPrivacyPolicy(): Boolean {
        return try {
            settingsDataStore.setPrivacyPolicyAccepted(
                accepted = true,
                version = CURRENT_PRIVACY_POLICY_VERSION
            )
            Log.d(TAG, "Privacy policy accepted for version $CURRENT_PRIVACY_POLICY_VERSION")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error accepting privacy policy", e)
            false
        }
    }
    
    /**
     * Decline privacy policy
     */
    suspend fun declinePrivacyPolicy(): Boolean {
        return try {
            settingsDataStore.setPrivacyPolicyAccepted(
                accepted = false,
                version = CURRENT_PRIVACY_POLICY_VERSION
            )
            // Also disable optional features when declining
            settingsDataStore.setAnalyticsEnabled(false)
            settingsDataStore.setCrashReportingEnabled(false)
            
            Log.d(TAG, "Privacy policy declined, optional features disabled")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error declining privacy policy", e)
            false
        }
    }
    
    /**
     * Withdraw consent and disable all optional data collection
     */
    suspend fun withdrawConsent(): Boolean {
        return try {
            settingsDataStore.withdrawPrivacyConsent()
            Log.d(TAG, "Privacy consent withdrawn")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error withdrawing consent", e)
            false
        }
    }
    
    /**
     * Get privacy policy acceptance status
     */
    suspend fun getPrivacyStatus(): PrivacyStatus {
        return try {
            val isAccepted = settingsDataStore.privacyPolicyAccepted.first()
            val version = settingsDataStore.privacyPolicyVersion.first()
            val timestamp = settingsDataStore.consentTimestamp.first()
            val analyticsEnabled = settingsDataStore.analyticsEnabled.first()
            val crashReportingEnabled = settingsDataStore.crashReportingEnabled.first()
            
            PrivacyStatus(
                isAccepted = isAccepted,
                version = version,
                consentTimestamp = timestamp,
                analyticsEnabled = analyticsEnabled,
                crashReportingEnabled = crashReportingEnabled,
                isCurrentVersion = version == CURRENT_PRIVACY_POLICY_VERSION
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting privacy status", e)
            PrivacyStatus() // Return default status
        }
    }
    
    /**
     * Check if analytics can be enabled (requires privacy consent)
     */
    suspend fun canEnableAnalytics(): Boolean {
        val status = getPrivacyStatus()
        return status.isAccepted && status.isCurrentVersion
    }
    
    /**
     * Check if crash reporting can be enabled (requires privacy consent)
     */
    suspend fun canEnableCrashReporting(): Boolean {
        val status = getPrivacyStatus()
        return status.isAccepted && status.isCurrentVersion
    }
    
    /**
     * Get formatted consent date
     */
    suspend fun getConsentDateFormatted(): String {
        return try {
            val timestamp = settingsDataStore.consentTimestamp.first()
            if (timestamp > 0) {
                val date = Date(timestamp)
                val formatter = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault())
                formatter.format(date)
            } else {
                "Not provided"
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error formatting consent date", e)
            "Unknown"
        }
    }
    
    /**
     * Get privacy policy version history
     */
    fun getVersionHistory(): Map<String, String> {
        return PRIVACY_POLICY_VERSIONS
    }
    
    /**
     * Check if there's a newer privacy policy version
     */
    suspend fun hasNewerVersion(): Boolean {
        val currentVersion = settingsDataStore.privacyPolicyVersion.first()
        return currentVersion != CURRENT_PRIVACY_POLICY_VERSION
    }
    
    /**
     * Get privacy policy compliance summary
     */
    suspend fun getComplianceSummary(): PrivacyComplianceSummary {
        val status = getPrivacyStatus()
        
        return PrivacyComplianceSummary(
            isCompliant = status.isAccepted && status.isCurrentVersion,
            hasValidConsent = status.isAccepted,
            isCurrentVersion = status.isCurrentVersion,
            consentDate = getConsentDateFormatted(),
            optionalFeaturesEnabled = status.analyticsEnabled || status.crashReportingEnabled,
            dataCollectionMinimal = !status.analyticsEnabled && !status.crashReportingEnabled
        )
    }
}

/**
 * Data class representing privacy policy acceptance status
 */
data class PrivacyStatus(
    val isAccepted: Boolean = false,
    val version: String = "1.0",
    val consentTimestamp: Long = 0L,
    val analyticsEnabled: Boolean = false,
    val crashReportingEnabled: Boolean = false,
    val isCurrentVersion: Boolean = false
)

/**
 * Data class representing privacy compliance summary
 */
data class PrivacyComplianceSummary(
    val isCompliant: Boolean = false,
    val hasValidConsent: Boolean = false,
    val isCurrentVersion: Boolean = false,
    val consentDate: String = "Not provided",
    val optionalFeaturesEnabled: Boolean = false,
    val dataCollectionMinimal: Boolean = true
)
