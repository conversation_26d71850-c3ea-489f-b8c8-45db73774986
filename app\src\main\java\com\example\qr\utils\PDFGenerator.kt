package com.example.qr.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.pdf.PdfDocument
import android.os.Environment
import android.util.Log
import androidx.core.content.ContextCompat
import com.example.qr.R
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.*

/**
 * Utility class for generating PDF documents containing QR codes
 */
object PDFGenerator {
    
    private const val TAG = "PDFGenerator"
    
    // PDF page dimensions (A4 size in points: 1 point = 1/72 inch)
    private const val PAGE_WIDTH = 595  // 8.27 inches * 72
    private const val PAGE_HEIGHT = 842 // 11.69 inches * 72
    
    // Margins and spacing
    private const val MARGIN = 72 // 1 inch margin
    private const val QR_SIZE = 300 // QR code size in points
    private const val TEXT_SIZE = 14f
    private const val TITLE_SIZE = 18f
    
    /**
     * Generate a PDF document containing a QR code
     */
    fun generateQRCodePDF(
        context: Context,
        qrBitmap: Bitmap,
        content: String,
        qrType: String = "QR Code",
        filename: String? = null
    ): PDFResult {
        return try {
            val document = PdfDocument()
            
            // Create page info
            val pageInfo = PdfDocument.PageInfo.Builder(PAGE_WIDTH, PAGE_HEIGHT, 1).create()
            val page = document.startPage(pageInfo)
            val canvas = page.canvas
            
            // Draw content on the page
            drawPDFContent(canvas, qrBitmap, content, qrType, context)
            
            // Finish the page
            document.finishPage(page)
            
            // Save the document
            val file = savePDFToFile(context, document, filename)
            document.close()
            
            if (file != null) {
                PDFResult.Success(file.absolutePath, file.name)
            } else {
                PDFResult.Error("Failed to save PDF file")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating PDF", e)
            PDFResult.Error("Error generating PDF: ${e.message}")
        }
    }
    
    /**
     * Draw the PDF content including QR code, title, and content text
     */
    private fun drawPDFContent(
        canvas: Canvas,
        qrBitmap: Bitmap,
        content: String,
        qrType: String,
        context: Context
    ) {
        val paint = Paint().apply {
            isAntiAlias = true
            color = ContextCompat.getColor(context, R.color.black)
        }
        
        var yPosition = MARGIN.toFloat()
        
        // Draw title
        paint.textSize = TITLE_SIZE
        paint.isFakeBoldText = true
        val title = "QR Spark - $qrType"
        canvas.drawText(title, MARGIN.toFloat(), yPosition, paint)
        yPosition += 40
        
        // Draw timestamp
        paint.textSize = TEXT_SIZE
        paint.isFakeBoldText = false
        val timestamp = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault()).format(Date())
        canvas.drawText("Generated on $timestamp", MARGIN.toFloat(), yPosition, paint)
        yPosition += 60
        
        // Draw QR code (centered horizontally)
        val qrLeft = (PAGE_WIDTH - QR_SIZE) / 2f
        val scaledBitmap = Bitmap.createScaledBitmap(qrBitmap, QR_SIZE, QR_SIZE, true)
        canvas.drawBitmap(scaledBitmap, qrLeft, yPosition, null)
        yPosition += QR_SIZE + 40
        
        // Draw content label
        paint.isFakeBoldText = true
        canvas.drawText("Content:", MARGIN.toFloat(), yPosition, paint)
        yPosition += 30
        
        // Draw content text (with word wrapping)
        paint.isFakeBoldText = false
        drawWrappedText(canvas, content, MARGIN.toFloat(), yPosition, PAGE_WIDTH - 2 * MARGIN, paint)
    }
    
    /**
     * Draw text with word wrapping
     */
    private fun drawWrappedText(
        canvas: Canvas,
        text: String,
        x: Float,
        startY: Float,
        maxWidth: Int,
        paint: Paint
    ) {
        val words = text.split(" ")
        var currentLine = ""
        var y = startY
        val lineHeight = paint.textSize + 8
        
        for (word in words) {
            val testLine = if (currentLine.isEmpty()) word else "$currentLine $word"
            val textWidth = paint.measureText(testLine)
            
            if (textWidth <= maxWidth) {
                currentLine = testLine
            } else {
                if (currentLine.isNotEmpty()) {
                    canvas.drawText(currentLine, x, y, paint)
                    y += lineHeight
                }
                currentLine = word
            }
        }
        
        // Draw the last line
        if (currentLine.isNotEmpty()) {
            canvas.drawText(currentLine, x, y, paint)
        }
    }
    
    /**
     * Save the PDF document to a file
     */
    private fun savePDFToFile(
        context: Context,
        document: PdfDocument,
        filename: String?
    ): File? {
        return try {
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = filename ?: "QRSpark_$timestamp.pdf"
            
            // Create the QR Spark directory in Documents
            val documentsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            val qrSparkDir = File(documentsDir, "QR Spark")
            if (!qrSparkDir.exists()) {
                qrSparkDir.mkdirs()
            }
            
            val file = File(qrSparkDir, fileName)
            val outputStream = FileOutputStream(file)
            
            document.writeTo(outputStream)
            outputStream.close()
            
            Log.d(TAG, "PDF saved successfully: ${file.absolutePath}")
            file
        } catch (e: IOException) {
            Log.e(TAG, "Error saving PDF", e)
            null
        }
    }
    
    /**
     * Generate a PDF document to a specific file location
     */
    fun generateQRCodePDFToFile(
        context: Context,
        qrBitmap: Bitmap,
        content: String,
        qrType: String = "QR Code",
        outputFile: File
    ): PDFResult {
        return try {
            val document = PdfDocument()

            // Create page info
            val pageInfo = PdfDocument.PageInfo.Builder(PAGE_WIDTH, PAGE_HEIGHT, 1).create()
            val page = document.startPage(pageInfo)
            val canvas = page.canvas

            // Draw content on the page
            drawPDFContent(canvas, qrBitmap, content, qrType, context)

            // Finish the page
            document.finishPage(page)

            // Save to the specified file
            val outputStream = FileOutputStream(outputFile)
            document.writeTo(outputStream)
            outputStream.close()
            document.close()

            Log.d(TAG, "PDF saved successfully to: ${outputFile.absolutePath}")
            PDFResult.Success(outputFile.absolutePath, outputFile.name)
        } catch (e: Exception) {
            Log.e(TAG, "Error generating PDF to file", e)
            PDFResult.Error("Error generating PDF: ${e.message}")
        }
    }

    /**
     * Generate a simple QR code PDF with minimal content
     */
    fun generateSimpleQRCodePDF(
        context: Context,
        qrBitmap: Bitmap,
        content: String
    ): PDFResult {
        return generateQRCodePDF(context, qrBitmap, content, "QR Code")
    }
}

/**
 * Result class for PDF generation operations
 */
sealed class PDFResult {
    data class Success(val filePath: String, val fileName: String) : PDFResult()
    data class Error(val message: String) : PDFResult()
}
