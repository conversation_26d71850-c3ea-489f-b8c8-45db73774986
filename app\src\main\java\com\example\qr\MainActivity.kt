package com.example.qr

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.rememberNavController
import com.example.qr.data.model.Language
import com.example.qr.data.repository.SettingsRepository
import com.example.qr.presentation.navigation.QRNavigation
import com.example.qr.presentation.screens.settings.SettingsViewModel
import com.example.qr.ui.components.FirstTimeLanguageSelectionDialog
import com.example.qr.ui.components.LocalizedContent
import com.example.qr.ui.theme.QRThemeWithSettings
import com.example.qr.utils.LanguageManager
import com.example.qr.utils.SettingsTestHelper
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

class MainActivity : ComponentActivity() {

    override fun attachBaseContext(newBase: Context?) {
        // Apply saved language before activity creation
        val context = newBase ?: return super.attachBaseContext(newBase)

        try {
            val sharedPrefs = context.getSharedPreferences("datastore", Context.MODE_PRIVATE)
            val languageCode = sharedPrefs.getString("selected_language", "en") ?: "en"
            val language = Language.getLanguageByCode(languageCode) ?: Language.getSystemLanguage()
            val updatedContext = LanguageManager.setLanguage(context, language)
            super.attachBaseContext(updatedContext)
        } catch (e: Exception) {
            // Fallback to default context if language application fails
            super.attachBaseContext(newBase)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Test settings integration (for debugging)
        SettingsTestHelper.logCurrentSettings(this)

        // Test QR size setting functionality (for debugging)
        lifecycleScope.launch {
            try {
                com.example.qr.utils.QRSizeTestHelper.testDefaultQRSizeSetting(this@MainActivity)
            } catch (e: Exception) {
                Log.e("MainActivity", "Error testing QR size setting", e)
            }
        }

        // Test QR format setting functionality (for debugging)
        lifecycleScope.launch {
            try {
                com.example.qr.utils.QRFormatTestHelper.testDefaultQRFormatSetting(this@MainActivity)
            } catch (e: Exception) {
                Log.e("MainActivity", "Error testing QR format setting", e)
            }
        }

        setContent {
            val settingsViewModel: SettingsViewModel = viewModel()
            val settings by settingsViewModel.settings.collectAsState()

            // Initialize language manager with saved language
            LaunchedEffect(settings.selectedLanguage) {
                val savedLanguage = Language.getLanguageByCode(settings.selectedLanguage) ?: Language.getSystemLanguage()
                LanguageManager.initializeLanguage(this@MainActivity, savedLanguage.code)
            }

            QRThemeWithSettings(
                themeMode = settings.themeMode
            ) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // Create NavController outside LocalizedContent to prevent recreation on language changes
                    val navController = rememberNavController()

                    // Use LocalizedContent for immediate language switching
                    LocalizedContent { localizedContext ->
                        // Show first-time language selection dialog if needed
                        if (settings.isFirstTimeLanguageSelection) {
                            FirstTimeLanguageSelectionDialog(
                                onLanguageSelected = { language ->
                                    // Save language preference first
                                    settingsViewModel.setSelectedLanguage(language.code)
                                    settingsViewModel.setFirstTimeLanguageSelection(false)
                                    // Apply language immediately without recreating activity
                                    LanguageManager.updateLanguageImmediately(localizedContext, language)
                                }
                            )
                        } else {
                            // Navigation with stable NavController that doesn't recreate on language changes
                            QRNavigation(navController = navController)
                        }
                    }
                }
            }
        }
    }
}