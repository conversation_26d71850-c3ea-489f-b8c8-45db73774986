package com.example.qr

import android.content.Context
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.compose.rememberNavController
import com.example.qr.BuildConfig
import com.example.qr.data.model.Language
import com.example.qr.data.repository.SettingsRepository
import com.example.qr.presentation.navigation.QRNavigation
import com.example.qr.presentation.screens.settings.SettingsViewModel
import com.example.qr.ui.components.FirstTimeLanguageSelectionDialog
import com.example.qr.ui.components.LocalizedContent
import com.example.qr.ui.components.PrivacyPolicyDialog
import com.example.qr.ui.theme.QRThemeWithSettings
import com.example.qr.utils.LanguageManager
import com.example.qr.utils.PrivacyManager
import com.example.qr.utils.SettingsTestHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext

class MainActivity : ComponentActivity() {

    override fun attachBaseContext(newBase: Context?) {
        // Apply saved language before activity creation
        val context = newBase ?: return super.attachBaseContext(newBase)

        try {
            val sharedPrefs = context.getSharedPreferences("datastore", Context.MODE_PRIVATE)
            val languageCode = sharedPrefs.getString("selected_language", "en") ?: "en"
            val language = Language.getLanguageByCode(languageCode) ?: Language.getSystemLanguage()
            val updatedContext = LanguageManager.setLanguage(context, language)
            super.attachBaseContext(updatedContext)
        } catch (e: Exception) {
            // Fallback to default context if language application fails
            super.attachBaseContext(newBase)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Remove debug testing to improve performance
        // Only log settings in debug builds
        if (BuildConfig.DEBUG) {
            SettingsTestHelper.logCurrentSettings(this)
        }

        setContent {
            val settingsViewModel: SettingsViewModel = viewModel()
            val settings by settingsViewModel.settings.collectAsState()

            // Privacy management
            val privacyManager = remember { PrivacyManager(this@MainActivity) }
            var showPrivacyDialog by remember { mutableStateOf(false) }
            var privacyDialogChecked by remember { mutableStateOf(false) }

            // Initialize language manager with saved language
            LaunchedEffect(settings.selectedLanguage) {
                val savedLanguage = Language.getLanguageByCode(settings.selectedLanguage) ?: Language.getSystemLanguage()
                LanguageManager.initializeLanguage(this@MainActivity, savedLanguage.code)
            }

            // Check privacy policy status in background to avoid blocking main thread
            LaunchedEffect(Unit) {
                if (!privacyDialogChecked) {
                    // Run privacy check in background
                    kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.IO) {
                        val shouldShow = privacyManager.shouldShowPrivacyPolicy()
                        kotlinx.coroutines.withContext(kotlinx.coroutines.Dispatchers.Main) {
                            showPrivacyDialog = shouldShow
                            privacyDialogChecked = true
                        }
                    }
                }
            }

            QRThemeWithSettings(
                themeMode = settings.themeMode
            ) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    // Create NavController outside LocalizedContent to prevent recreation on language changes
                    val navController = rememberNavController()

                    // Use LocalizedContent for immediate language switching
                    LocalizedContent { localizedContext ->
                        // Show privacy policy dialog first (highest priority)
                        if (showPrivacyDialog) {
                            PrivacyPolicyDialog(
                                onAccept = {
                                    lifecycleScope.launch {
                                        withContext(Dispatchers.IO) {
                                            val success = privacyManager.acceptPrivacyPolicy()
                                            withContext(Dispatchers.Main) {
                                                if (success) {
                                                    showPrivacyDialog = false
                                                    Log.d("MainActivity", "Privacy policy accepted")
                                                }
                                            }
                                        }
                                    }
                                },
                                onDecline = {
                                    lifecycleScope.launch {
                                        withContext(Dispatchers.IO) {
                                            val success = privacyManager.declinePrivacyPolicy()
                                            withContext(Dispatchers.Main) {
                                                if (success) {
                                                    showPrivacyDialog = false
                                                    Log.d("MainActivity", "Privacy policy declined")
                                                }
                                            }
                                        }
                                    }
                                },
                                isFirstTime = !settings.privacyPolicyAccepted
                            )
                        }
                        // Show first-time language selection dialog if needed (after privacy)
                        else if (settings.isFirstTimeLanguageSelection) {
                            FirstTimeLanguageSelectionDialog(
                                onLanguageSelected = { language ->
                                    // Save language preference first
                                    settingsViewModel.setSelectedLanguage(language.code)
                                    settingsViewModel.setFirstTimeLanguageSelection(false)
                                    // Apply language immediately without recreating activity
                                    LanguageManager.updateLanguageImmediately(localizedContext, language)
                                }
                            )
                        } else {
                            // Navigation with stable NavController that doesn't recreate on language changes
                            QRNavigation(navController = navController)
                        }
                    }
                }
            }
        }
    }
}