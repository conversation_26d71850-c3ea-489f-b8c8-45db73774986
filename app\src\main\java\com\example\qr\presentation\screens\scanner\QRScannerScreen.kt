@file:OptIn(com.google.accompanist.permissions.ExperimentalPermissionsApi::class)

package com.example.qr.presentation.screens.scanner

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTransformGestures
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.ExperimentalComposeUiApi
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import kotlinx.coroutines.launch
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.presentation.camera.CameraPreview
import com.example.qr.presentation.navigation.Screen
import com.example.qr.ui.theme.*
import com.example.qr.ui.components.GradientBackground
import com.example.qr.utils.SafeAreaUtils
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.accompanist.permissions.isGranted
import com.google.accompanist.permissions.rememberPermissionState
import com.google.accompanist.permissions.shouldShowRationale
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.common.InputImage
import java.io.InputStream
import java.util.UUID
import kotlin.math.abs
import com.example.qr.ui.components.ReactiveLanguageContent
import com.example.qr.ui.components.reactiveStringResource
import com.example.qr.ui.components.TutorialTrigger
import com.example.qr.utils.TutorialManager
import com.example.qr.utils.TutorialContent
import com.example.qr.utils.LanguageManager

@OptIn(ExperimentalMaterial3Api::class, ExperimentalPermissionsApi::class)
@Composable
fun QRScannerScreen(
    navController: NavController
) {
    ReactiveLanguageContent {
        QRScannerScreenContent(navController)
    }
}

@Composable
private fun QRScannerScreenContent(
    navController: NavController
) {
    val context = LocalContext.current
    val viewModel: SimpleScannerViewModel = viewModel { SimpleScannerViewModel(context) }
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    key(currentLanguage) {

    // Ensure we have an Activity context for permissions
    val activityContext = remember {
        var ctx = context
        while (ctx is android.content.ContextWrapper) {
            if (ctx is androidx.activity.ComponentActivity) {
                break
            }
            ctx = ctx.baseContext
        }
        ctx
    }

    val cameraPermissionState = rememberPermissionState(Manifest.permission.CAMERA)

    // Camera controls state
    var isFlashOn by remember { mutableStateOf(false) }
    var isBackCamera by remember { mutableStateOf(true) }
    var zoomLevel by remember { mutableStateOf(1f) }
    var showZoomControls by remember { mutableStateOf(false) }

    // Image upload state
    var isProcessingImage by remember { mutableStateOf(false) }

    val uiState by viewModel.uiState.collectAsState()

    // Image picker launcher
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            processImageFromGallery(context, it, viewModel) { isProcessing ->
                isProcessingImage = isProcessing
            }
        }
    }

    LaunchedEffect(Unit) {
        if (!cameraPermissionState.status.isGranted) {
            cameraPermissionState.launchPermissionRequest()
        }
    }

    // Show error snackbar
    LaunchedEffect(uiState.error) {
        uiState.error?.let {
            Log.e("QRScannerScreen", "Error: $it")
            // Show snackbar or toast
            viewModel.clearError()
        }
    }

    // Debug camera permission status
    LaunchedEffect(cameraPermissionState.status.isGranted) {
        Log.d("QRScannerScreen", "Camera permission granted: ${cameraPermissionState.status.isGranted}")
    }

    GradientBackground(applyScreenSafeArea = false) {
        Scaffold(
            modifier = Modifier
                .fillMaxSize()
                .windowInsetsPadding(WindowInsets.displayCutout),
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            stringResource(R.string.scanner_title),
                            color = MaterialTheme.colorScheme.onSurface,
                            fontWeight = FontWeight.SemiBold
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = {
                            navController.navigate(Screen.Home.route) {
                                popUpTo(Screen.Home.route) { inclusive = true }
                            }
                        }) {
                            Icon(
                                Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    },
                    actions = {
                        // Flash control removed - now handled in SmartScanHeader
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = Color.Transparent
                    ),
                    windowInsets = WindowInsets.statusBars
                )
            },
            containerColor = Color.Transparent,
            contentWindowInsets = WindowInsets.safeDrawing
        ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            when {
                cameraPermissionState.status.isGranted -> {
                    Log.d("QRScannerScreen", "Showing camera preview")
                    // Smart Scan Interface inspired by the design
                    SmartScanInterface(
                        isFlashOn = isFlashOn,
                        isBackCamera = isBackCamera,
                        zoomLevel = zoomLevel,
                        showZoomControls = showZoomControls,
                        isProcessingImage = isProcessingImage,
                        onQRCodeDetected = { qrCode ->
                            Log.d("QRScannerScreen", "QR Code detected: $qrCode")
                            viewModel.onQRCodeDetected(qrCode)
                        },
                        onFlashToggle = { isFlashOn = !isFlashOn },
                        onCameraSwitch = {
                            // Switch camera and reinitialize
                            isBackCamera = !isBackCamera
                        },
                        onZoomChange = { newZoom ->
                            zoomLevel = newZoom.coerceIn(1f, 10f)
                            showZoomControls = true
                        },
                        onZoomIn = {
                            zoomLevel = (zoomLevel + 0.5f).coerceAtMost(10f)
                            showZoomControls = true
                        },
                        onZoomOut = {
                            zoomLevel = (zoomLevel - 0.5f).coerceAtLeast(1f)
                            showZoomControls = true
                        },
                        onImageUpload = {
                            imagePickerLauncher.launch("image/*")
                        }
                    )

                    // Hide zoom controls after extended delay for better UX
                    LaunchedEffect(showZoomControls) {
                        if (showZoomControls) {
                            kotlinx.coroutines.delay(4500) // Extended to 4.5 seconds
                            showZoomControls = false
                        }
                    }
                }

                cameraPermissionState.status.shouldShowRationale -> {
                    PermissionRationaleContent(
                        onRequestPermission = { cameraPermissionState.launchPermissionRequest() }
                    )
                }

                else -> {
                    PermissionDeniedContent(
                        onRequestPermission = { cameraPermissionState.launchPermissionRequest() }
                    )
                }
            }
        }
        }

        // Tutorial overlay for scanner features
        TutorialTrigger(
            tutorialKey = TutorialManager.TutorialKeys.SCANNER_SCREEN,
            steps = TutorialContent.getScannerTutorials(context)
        )
    }

    // Show QR code result dialog
    if (uiState.showResult && uiState.scannedQRCode != null) {
        QRCodeResultDialog(
            qrCode = uiState.scannedQRCode!!,
            autoSaved = uiState.autoSaved,
            onDismiss = { viewModel.dismissResult() },
            onAction = { viewModel.handleQRCodeAction(context, uiState.scannedQRCode!!) },
            onToggleFavorite = { viewModel.toggleFavorite(uiState.scannedQRCode!!) },
            onNavigateHome = {
                navController.navigate(Screen.Home.route) {
                    popUpTo(Screen.Home.route) { inclusive = true }
                }
            }
        )
    }
    } // Close key block
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRCodeResultDialog(
    qrCode: QRCodeData,
    autoSaved: Boolean,
    onDismiss: () -> Unit,
    onAction: () -> Unit,
    onToggleFavorite: () -> Unit,
    onNavigateHome: () -> Unit
) {
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()
    val resetKey = remember { mutableStateOf(UUID.randomUUID()) }

    LaunchedEffect(currentLanguage) {
        resetKey.value = UUID.randomUUID() // Force reset on language change
    }

    key(resetKey.value) {
        QRCodeResultDialogContent(
            qrCode = qrCode,
            autoSaved = autoSaved,
            onDismiss = onDismiss,
            onAction = onAction,
            onToggleFavorite = onToggleFavorite,
            onNavigateHome = onNavigateHome
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun QRCodeResultDialogContent(
    qrCode: QRCodeData,
    autoSaved: Boolean,
    onDismiss: () -> Unit,
    onAction: () -> Unit,
    onToggleFavorite: () -> Unit,
    onNavigateHome: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    // Reactive string resources that update with language changes
    val messageQrContent = stringResource(R.string.message_qr_content)
    val messageContentCopied = stringResource(R.string.message_content_copied)

    // Action button states for enhanced feedback - matching generator pattern
    var isActionLoading by remember { mutableStateOf(false) }
    var actionSuccess by remember { mutableStateOf(false) }
    var isCopyLoading by remember { mutableStateOf(false) }
    var copySuccess by remember { mutableStateOf(false) }

    // Get action text based on QR type - using string resources for localization
    val actionText = when (qrCode.type) {
        com.example.qr.data.model.QRCodeType.URL -> stringResource(R.string.action_open_website)
        com.example.qr.data.model.QRCodeType.EMAIL -> stringResource(R.string.action_send_email)
        com.example.qr.data.model.QRCodeType.PHONE -> stringResource(R.string.action_call_phone)
        com.example.qr.data.model.QRCodeType.SMS -> stringResource(R.string.action_send_sms)
        com.example.qr.data.model.QRCodeType.WIFI -> stringResource(R.string.action_connect_wifi)
        com.example.qr.data.model.QRCodeType.CONTACT -> stringResource(R.string.action_add_contact)
        com.example.qr.data.model.QRCodeType.LOCATION -> stringResource(R.string.action_open_maps)
        com.example.qr.data.model.QRCodeType.CALENDAR -> stringResource(R.string.action_add_calendar)
        else -> stringResource(R.string.action_copy_text)
    }

    // Get action icon based on QR type - no longer dependent on text content
    val actionIcon = when (qrCode.type) {
        com.example.qr.data.model.QRCodeType.URL -> Icons.Default.OpenInBrowser
        com.example.qr.data.model.QRCodeType.EMAIL -> Icons.Default.Email
        com.example.qr.data.model.QRCodeType.PHONE -> Icons.Default.Phone
        com.example.qr.data.model.QRCodeType.SMS -> Icons.Default.Sms
        com.example.qr.data.model.QRCodeType.WIFI -> Icons.Default.Wifi
        com.example.qr.data.model.QRCodeType.CONTACT -> Icons.Default.PersonAdd
        com.example.qr.data.model.QRCodeType.LOCATION -> Icons.Default.LocationOn
        com.example.qr.data.model.QRCodeType.CALENDAR -> Icons.Default.Event
        else -> Icons.Default.ContentCopy
    }

    // Enhanced action handler with feedback - matching generator pattern
    fun handleAction() {
        if (isActionLoading) return

        scope.launch {
            isActionLoading = true
            try {
                onAction()
                actionSuccess = true

                // Reset success state after brief display
                kotlinx.coroutines.delay(1500)
                actionSuccess = false
            } catch (e: Exception) {
                // Handle error silently for now
            } finally {
                isActionLoading = false
            }
        }
    }

    // Enhanced copy handler with feedback - matching generator pattern
    fun handleCopy() {
        if (isCopyLoading) return

        scope.launch {
            isCopyLoading = true
            try {
                val clipboard = context.getSystemService(android.content.Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                val clip = android.content.ClipData.newPlainText(messageQrContent, qrCode.content)
                clipboard.setPrimaryClip(clip)

                copySuccess = true
                snackbarHostState.showSnackbar(
                    message = messageContentCopied,
                    duration = SnackbarDuration.Short
                )

                // Reset success state after brief display
                kotlinx.coroutines.delay(1500)
                copySuccess = false
            } catch (e: Exception) {
                // Handle error silently
            } finally {
                isCopyLoading = false
            }
        }
    }

    // EXACT MATCH TO GENERATOR: ModalBottomSheet with identical styling and safe area handling
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState,
        containerColor = MaterialTheme.colorScheme.surface,
        contentColor = MaterialTheme.colorScheme.onSurface,
        shape = RoundedCornerShape(topStart = 28.dp, topEnd = 28.dp),
        dragHandle = {
            Surface(
                modifier = Modifier.padding(vertical = 12.dp),
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                shape = RoundedCornerShape(16.dp)
            ) {
                Box(
                    modifier = Modifier.size(width = 32.dp, height = 4.dp)
                )
            }
        }
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .windowInsetsPadding(WindowInsets.navigationBars)
                .windowInsetsPadding(WindowInsets.ime)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp)
                    .padding(bottom = 32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // EXACT MATCH TO GENERATOR: Success Header with identical styling
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(bottom = 16.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                color = ScanColor.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(24.dp)
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CameraAlt,
                            contentDescription = null,
                            tint = ScanColor,
                            modifier = Modifier.size(28.dp)
                        )
                    }

                    Spacer(modifier = Modifier.width(16.dp))

                    Column {
                        Text(
                            text = stringResource(R.string.qr_scanned_title),
                            style = QRSparkTextStyles.cardTitle,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                        Text(
                            text = qrCode.displayName,
                            style = QRSparkTextStyles.cardSubtitle,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // QR Content Preview - matching generator pattern
                        if (qrCode.content.isNotBlank()) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = if (qrCode.content.length > 50)
                                    "${qrCode.content.take(50)}..."
                                else qrCode.content,
                                style = QRSparkTextStyles.qrContent,
                                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                            )
                        }
                    }
                }

                // EXACT MATCH TO GENERATOR: Auto-save status with identical styling
                val isSaved = qrCode.id != 0L
                when {
                    autoSaved -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.CloudDone,
                                contentDescription = null,
                                tint = SuccessGreen,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = stringResource(R.string.status_auto_saved),
                                style = QRSparkTextStyles.statusSuccess,
                                color = SuccessGreen
                            )
                        }
                    }
                    isSaved -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Favorite,
                                contentDescription = null,
                                tint = SuccessGreen,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = stringResource(R.string.status_saved),
                                style = QRSparkTextStyles.statusSuccess,
                                color = SuccessGreen
                            )
                        }
                    }
                    else -> {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Info,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = stringResource(R.string.status_not_saved),
                                style = QRSparkTextStyles.statusError,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }

                // EXACT MATCH TO GENERATOR: QR Code Display Card with identical styling
                Card(
                    modifier = Modifier.size(200.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(20.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.QrCode,
                            contentDescription = "Scanned QR Code",
                            modifier = Modifier.size(64.dp),
                            tint = ScanColor
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = qrCode.type.name,
                            style = QRSparkTextStyles.cardSubtitle,
                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = stringResource(R.string.qr_characters, qrCode.content.length),
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                            textAlign = TextAlign.Center
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // EXACT MATCH TO GENERATOR: Action Buttons Grid with identical styling
                Column(
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // First Row: Primary Action & Copy (matching generator's Save & Share pattern)
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        // Enhanced Primary Action Button (matching generator's AnimatedSaveButton pattern)
                        AnimatedActionButton(
                            onClick = { handleAction() },
                            isLoading = isActionLoading,
                            isSuccess = actionSuccess,
                            isError = false,
                            enabled = true,
                            actionText = actionText,
                            actionIcon = actionIcon,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp)
                        )

                        // Enhanced Copy Button (matching generator's EnhancedShareButton pattern)
                        EnhancedCopyButton(
                            onClick = { handleCopy() },
                            isLoading = isCopyLoading,
                            isSuccess = copySuccess,
                            enabled = true,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp)
                        )
                    }

                    // Second Row: Add to Favorites (matching generator pattern)
                    OutlinedButton(
                        onClick = onToggleFavorite,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        shape = RoundedCornerShape(16.dp),
                        border = androidx.compose.foundation.BorderStroke(
                            1.5.dp,
                            if (qrCode.isFavorite) SparkPink else MaterialTheme.colorScheme.outline
                        ),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = if (qrCode.isFavorite) SparkPink else MaterialTheme.colorScheme.onSurface
                        )
                    ) {
                        Icon(
                            imageVector = if (qrCode.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            if (qrCode.isFavorite) stringResource(R.string.action_favorited) else stringResource(R.string.action_add_to_favorites),
                            style = QRSparkTextStyles.buttonMedium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // EXACT MATCH TO GENERATOR: Close Button with identical styling
                TextButton(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        stringResource(R.string.action_close),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp
                    )
                }
            }

            // EXACT MATCH TO GENERATOR: Snackbar Host with identical styling
            SnackbarHost(
                hostState = snackbarHostState,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

// EXACT MATCH TO GENERATOR: AnimatedSaveButton pattern adapted for actions
@Composable
private fun AnimatedActionButton(
    onClick: () -> Unit,
    isLoading: Boolean,
    isSuccess: Boolean,
    isError: Boolean,
    enabled: Boolean,
    actionText: String,
    actionIcon: androidx.compose.ui.graphics.vector.ImageVector,
    modifier: Modifier = Modifier
) {
    // Simple color states - matching generator pattern
    val borderColor = when {
        isSuccess -> SuccessGreen
        isError -> MaterialTheme.colorScheme.error
        isLoading -> ScanColor.copy(alpha = 0.6f)
        !enabled -> MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
        else -> ScanColor
    }

    val contentColor = when {
        isSuccess -> SuccessGreen
        isError -> MaterialTheme.colorScheme.error
        isLoading -> ScanColor.copy(alpha = 0.6f)
        !enabled -> MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
        else -> ScanColor
    }

    OutlinedButton(
        onClick = onClick,
        enabled = enabled && !isLoading,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        border = androidx.compose.foundation.BorderStroke(
            width = if (isSuccess) 2.dp else 1.5.dp,
            color = borderColor
        ),
        colors = ButtonDefaults.outlinedButtonColors(
            contentColor = contentColor,
            disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        // Simple content - matching generator pattern
        when {
            isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = contentColor
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.action_loading), style = QRSparkTextStyles.buttonMedium)
            }
            isSuccess -> {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Action completed",
                    tint = SuccessGreen,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = stringResource(R.string.action_done),
                    style = QRSparkTextStyles.buttonMedium,
                    color = SuccessGreen
                )
            }
            isError -> {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(stringResource(R.string.action_failed), style = QRSparkTextStyles.buttonMedium)
            }
            else -> {
                Icon(
                    imageVector = actionIcon,
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(actionText, style = QRSparkTextStyles.buttonMedium)
            }
        }
    }
}

// EXACT MATCH TO GENERATOR: EnhancedShareButton pattern adapted for copy
@Composable
private fun EnhancedCopyButton(
    onClick: () -> Unit,
    isLoading: Boolean,
    isSuccess: Boolean,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onClick,
        enabled = enabled && !isLoading,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (isSuccess) SuccessGreen else SparkPink,
            disabledContainerColor = SparkPink.copy(alpha = 0.6f)
        )
    ) {
        when {
            isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = Color.White
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_copying),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
            isSuccess -> {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Copied successfully",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_copied),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
            else -> {
                Icon(
                    imageVector = Icons.Default.ContentCopy,
                    contentDescription = "Copy content",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_copy),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
        }
    }
}

@Composable
fun PermissionRationaleContent(
    onRequestPermission: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.permission_camera_required),
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Text(
            text = stringResource(R.string.permission_camera_message),
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        Button(onClick = onRequestPermission) {
            Text(stringResource(R.string.permission_grant))
        }
    }
}

@Composable
fun PermissionDeniedContent(
    onRequestPermission: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.permission_camera_denied),
            style = MaterialTheme.typography.headlineSmall,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        Text(
            text = stringResource(R.string.camera_access_required),
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.padding(bottom = 24.dp)
        )

        Button(onClick = onRequestPermission) {
            Text(stringResource(R.string.permission_try_again))
        }
    }
}

// Smart Scan Interface inspired by the design image
@Composable
fun SmartScanInterface(
    isFlashOn: Boolean,
    isBackCamera: Boolean,
    zoomLevel: Float,
    showZoomControls: Boolean,
    isProcessingImage: Boolean,
    onQRCodeDetected: (String) -> Unit,
    onFlashToggle: () -> Unit,
    onCameraSwitch: () -> Unit,
    onZoomChange: (Float) -> Unit,
    onZoomIn: () -> Unit,
    onZoomOut: () -> Unit,
    onImageUpload: () -> Unit,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // Camera Preview with sophisticated ScaleGestureDetector pinch-to-zoom
        EnhancedZoomCameraPreview(
            isFlashOn = isFlashOn,
            onQRCodeDetected = onQRCodeDetected,
            isBackCamera = isBackCamera,
            zoomLevel = zoomLevel,
            onCameraSwitch = onCameraSwitch,
            onZoomChange = onZoomChange,
            modifier = Modifier.fillMaxSize()
        ) {
            CameraPreview(
                isFlashOn = isFlashOn,
                onQRCodeDetected = onQRCodeDetected,
                isBackCamera = isBackCamera,
                zoomLevel = zoomLevel,
                onCameraSwitch = onCameraSwitch,
                modifier = Modifier.fillMaxSize()
            )
        }

        // Minimalist Smart Scan Header inspired by the design
        MinimalistScanHeader(
            isFlashOn = isFlashOn,
            isBackCamera = isBackCamera,
            onFlashToggle = onFlashToggle,
            onCameraSwitch = onCameraSwitch,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 24.dp)
        )

        // Minimalist Scanning Frame inspired by the design
        MinimalistScanningFrame(
            modifier = Modifier.align(Alignment.Center)
        )

        // Minimalist Upload Button inspired by the design
        MinimalistUploadButton(
            isProcessingImage = isProcessingImage,
            onImageUpload = onImageUpload,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 140.dp)
        )

        // Minimalist Zoom Slider inspired by the design
        MinimalistZoomSlider(
            zoomLevel = zoomLevel,
            showZoomControls = showZoomControls,
            onZoomChange = onZoomChange,
            onZoomIn = onZoomIn,
            onZoomOut = onZoomOut,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 100.dp)
        )

        // Instructional Text Card at Bottom
        InstructionalTextCard(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(horizontal = 20.dp, vertical = 20.dp)
        )
    }
}

// Camera Controls Header with Flash, Camera Switch, and QR Icon
@Composable
fun SmartScanHeader(
    isFlashOn: Boolean,
    isBackCamera: Boolean,
    onFlashToggle: () -> Unit,
    onCameraSwitch: () -> Unit,
    modifier: Modifier = Modifier
) {
    // Control Icons Row - aligned to the right
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp),
        horizontalArrangement = Arrangement.End, // Align controls to the right
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Flash Toggle
        SmartScanControlIcon(
            icon = if (isFlashOn) Icons.Default.FlashOn else Icons.Default.FlashOff,
            contentDescription = if (isFlashOn) "Turn off flash" else "Turn on flash",
            isActive = isFlashOn,
            onClick = onFlashToggle
        )

        Spacer(modifier = Modifier.width(24.dp))

        // Camera Switch with visual feedback for current camera
        SmartScanControlIcon(
            icon = Icons.Default.FlipCameraAndroid,
            contentDescription = if (isBackCamera) "Switch to front camera" else "Switch to back camera",
            isActive = !isBackCamera, // Highlight when using front camera
            onClick = onCameraSwitch
        )
    }
}

// Smart Scan Control Icon Component
@Composable
fun SmartScanControlIcon(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    contentDescription: String,
    isActive: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        modifier = modifier
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = if (isActive) ScanColor else Color.White,
            modifier = Modifier.size(24.dp)
        )
    }
}

// Enhanced Scanning Frame with animations
@Composable
fun ScanningFrame(
    modifier: Modifier = Modifier
) {
    // Scanning line animation
    val infiniteTransition = rememberInfiniteTransition(label = "scanning_animation")
    val scanLinePosition by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "scan_line_position"
    )

    // Corner pulse animation
    val cornerPulse by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "corner_pulse"
    )

    Box(
        modifier = modifier
            .size(280.dp)
            .border(
                width = 3.dp,
                color = ScanColor,
                shape = RoundedCornerShape(24.dp)
            )
    ) {
        // Animated scanning line
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(2.dp)
                .offset(y = (260.dp * scanLinePosition)) // Move from top to bottom within frame
                .padding(horizontal = 12.dp)
                .background(
                    brush = androidx.compose.ui.graphics.Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            ScanColor.copy(alpha = 0.8f),
                            ScanColor,
                            ScanColor.copy(alpha = 0.8f),
                            Color.Transparent
                        )
                    ),
                    shape = RoundedCornerShape(1.dp)
                )
        )

        // Corner indicators with pulse effect
        val cornerSize = 24.dp
        val cornerThickness = 4.dp
        val cornerColor = ScanColor.copy(alpha = cornerPulse)

        // Top-left corner
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(cornerSize, cornerThickness)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .size(cornerThickness, cornerSize)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
        }

        // Top-right corner
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .size(cornerSize, cornerThickness)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(cornerThickness, cornerSize)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
        }

        // Bottom-left corner
        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(cornerSize, cornerThickness)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .size(cornerThickness, cornerSize)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
        }

        // Bottom-right corner
        Box(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(8.dp)
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerSize, cornerThickness)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerThickness, cornerSize)
                    .background(cornerColor, RoundedCornerShape(2.dp))
            )
        }
    }
}

// Upload QR Button inspired by the design
@Composable
fun UploadQRButton(
    isProcessingImage: Boolean,
    onImageUpload: () -> Unit,
    modifier: Modifier = Modifier
) {
    Button(
        onClick = onImageUpload,
        enabled = !isProcessingImage,
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 32.dp)
            .height(56.dp),
        shape = RoundedCornerShape(16.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
            contentColor = MaterialTheme.colorScheme.onSurface,
            disabledContainerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.5f)
        )
    ) {
        if (isProcessingImage) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                strokeWidth = 2.dp,
                color = ScanColor
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                "Processing...",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        } else {
            Icon(
                imageVector = Icons.Default.PhotoLibrary,
                contentDescription = reactiveStringResource(R.string.cd_upload_qr_image),
                modifier = Modifier.size(20.dp)
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                reactiveStringResource(R.string.scanner_upload_image),
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

// Enhanced Zoom Controls Bar with better visibility
@Composable
fun ZoomControlsBar(
    zoomLevel: Float,
    showZoomControls: Boolean,
    onZoomIn: () -> Unit,
    onZoomOut: () -> Unit,
    modifier: Modifier = Modifier
) {
    androidx.compose.animation.AnimatedVisibility(
        visible = showZoomControls,
        enter = androidx.compose.animation.fadeIn(
            animationSpec = tween(durationMillis = 300)
        ) + androidx.compose.animation.slideInVertically(
            animationSpec = tween(durationMillis = 300)
        ),
        exit = androidx.compose.animation.fadeOut(
            animationSpec = tween(durationMillis = 300)
        ) + androidx.compose.animation.slideOutVertically(
            animationSpec = tween(durationMillis = 300)
        ),
        modifier = modifier
    ) {
        Row(
            modifier = Modifier
                .background(
                    color = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
                    shape = RoundedCornerShape(24.dp)
                )
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Zoom Out Button
            ZoomControlButton(
                icon = Icons.Default.ZoomOut,
                contentDescription = "Zoom Out",
                enabled = zoomLevel > 1f,
                onClick = onZoomOut
            )

            // Zoom Level Indicator with Slider-like appearance
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // Zoom level line (like a slider track)
                Box(
                    modifier = Modifier
                        .width(100.dp)
                        .height(4.dp)
                        .background(
                            color = ScanColor.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(2.dp)
                        )
                ) {
                    // Zoom level indicator (like a slider thumb)
                    val progress = ((zoomLevel - 1f) / 9f).coerceIn(0f, 1f)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .fillMaxWidth(progress)
                            .background(
                                color = ScanColor,
                                shape = RoundedCornerShape(2.dp)
                            )
                    )

                    // Thumb indicator
                    Box(
                        modifier = Modifier
                            .size(12.dp)
                            .offset(x = (88.dp * progress))
                            .background(
                                color = Color.White,
                                shape = CircleShape
                            )
                            .border(
                                width = 2.dp,
                                color = ScanColor,
                                shape = CircleShape
                            )
                            .align(Alignment.CenterStart)
                    )
                }

                // Enhanced zoom level text with better typography
                Card(
                    shape = RoundedCornerShape(12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = ScanColor.copy(alpha = 0.15f)
                    ),
                    modifier = Modifier.padding(horizontal = 4.dp)
                ) {
                    Text(
                        text = "${String.format("%.1f", zoomLevel)}x",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Bold,
                        color = ScanColor,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }

            // Zoom In Button
            ZoomControlButton(
                icon = Icons.Default.ZoomIn,
                contentDescription = "Zoom In",
                enabled = zoomLevel < 10f,
                onClick = onZoomIn
            )
        }
    }
}

// Zoom Control Button Component
@Composable
fun ZoomControlButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    contentDescription: String,
    enabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier.size(32.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = if (enabled) ScanColor else MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f),
            modifier = Modifier.size(20.dp)
        )
    }
}

// Image processing function
fun processImageFromGallery(
    context: android.content.Context,
    uri: Uri,
    viewModel: SimpleScannerViewModel,
    onProcessingChange: (Boolean) -> Unit
) {
    onProcessingChange(true)

    try {
        val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
        val bitmap: Bitmap? = BitmapFactory.decodeStream(inputStream)
        inputStream?.close()

        if (bitmap != null) {
            val image = InputImage.fromBitmap(bitmap, 0)
            val scanner = BarcodeScanning.getClient()

            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    onProcessingChange(false)
                    if (barcodes.isNotEmpty()) {
                        barcodes.firstOrNull()?.rawValue?.let { qrContent ->
                            Log.d("ImageQRScanner", "QR Code found in image: $qrContent")
                            viewModel.onQRCodeDetected(qrContent)
                        }
                    } else {
                        Toast.makeText(context, "No QR code found in the selected image", Toast.LENGTH_SHORT).show()
                    }
                }
                .addOnFailureListener { exception ->
                    onProcessingChange(false)
                    Log.e("ImageQRScanner", "Failed to process image", exception)
                    Toast.makeText(context, "Failed to process image", Toast.LENGTH_SHORT).show()
                }
        } else {
            onProcessingChange(false)
            Toast.makeText(context, "Failed to load image", Toast.LENGTH_SHORT).show()
        }
    } catch (e: Exception) {
        onProcessingChange(false)
        Log.e("ImageQRScanner", "Error processing image from gallery", e)
        Toast.makeText(context, "Error processing image", Toast.LENGTH_SHORT).show()
    }
}

// Enhanced Camera Preview with ScaleGestureDetector for sophisticated pinch-to-zoom
@OptIn(ExperimentalComposeUiApi::class)
@Composable
fun EnhancedZoomCameraPreview(
    isFlashOn: Boolean,
    onQRCodeDetected: (String) -> Unit,
    isBackCamera: Boolean,
    zoomLevel: Float,
    onCameraSwitch: () -> Unit,
    onZoomChange: (Float) -> Unit,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    val context = LocalContext.current

    // Zoom gesture state management
    var gestureZoomLevel by remember { mutableStateOf(1f) }
    var isGestureActive by remember { mutableStateOf(false) }
    var gestureStartZoom by remember { mutableStateOf(1f) }

    // Create ScaleGestureDetector with sophisticated handling
    val scaleGestureDetector = remember {
        ScaleGestureDetector(context, object : ScaleGestureDetector.OnScaleGestureListener {
            override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
                Log.d("EnhancedZoom", "Scale gesture begin - Current zoom: $zoomLevel")
                gestureStartZoom = zoomLevel
                isGestureActive = true
                return true
            }

            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                Log.d("EnhancedZoom", "Scale factor: $scaleFactor")

                // Apply sophisticated scaling with velocity damping
                val dampingFactor = 0.3f // Reduce sensitivity for smoother control
                val adjustedScaleFactor = 1f + (scaleFactor - 1f) * dampingFactor

                // Calculate new zoom level based on gesture start zoom
                val newZoom = (gestureStartZoom * adjustedScaleFactor).coerceIn(1f, 10f)

                // Apply smooth interpolation to prevent jerky movements
                val smoothedZoom = if (abs(newZoom - gestureZoomLevel) > 0.1f) {
                    gestureZoomLevel + (newZoom - gestureZoomLevel) * 0.7f
                } else {
                    newZoom
                }

                gestureZoomLevel = smoothedZoom
                onZoomChange(smoothedZoom)

                Log.d("EnhancedZoom", "Applied zoom: $smoothedZoom")
                return true
            }

            override fun onScaleEnd(detector: ScaleGestureDetector) {
                Log.d("EnhancedZoom", "Scale gesture end - Final zoom: $gestureZoomLevel")
                isGestureActive = false

                // Apply final zoom level with slight momentum
                val finalZoom = gestureZoomLevel.coerceIn(1f, 10f)
                onZoomChange(finalZoom)
            }
        })
    }

    Box(
        modifier = modifier
            .pointerInteropFilter { event ->
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        // Reset gesture state on new touch
                        if (!isGestureActive) {
                            gestureZoomLevel = zoomLevel
                        }
                        scaleGestureDetector.onTouchEvent(event)
                    }
                    MotionEvent.ACTION_MOVE -> {
                        scaleGestureDetector.onTouchEvent(event)
                    }
                    MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                        scaleGestureDetector.onTouchEvent(event)
                        // Ensure gesture state is reset
                        if (!scaleGestureDetector.isInProgress) {
                            isGestureActive = false
                        }
                    }
                    else -> scaleGestureDetector.onTouchEvent(event)
                }
                true // Consume all touch events
            }
    ) {
        content()
    }
}

// Minimalist Smart Scan Header inspired by the design image
@Composable
fun MinimalistScanHeader(
    isFlashOn: Boolean,
    isBackCamera: Boolean,
    onFlashToggle: () -> Unit,
    onCameraSwitch: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 24.dp),
        horizontalArrangement = Arrangement.End, // Align controls to the right
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Control Icons Row
        Row(
            horizontalArrangement = Arrangement.spacedBy(24.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Flash Toggle
            IconButton(
                onClick = onFlashToggle,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = if (isFlashOn) Icons.Default.FlashOn else Icons.Default.FlashOff,
                    contentDescription = if (isFlashOn) "Turn off flash" else "Turn on flash",
                    tint = if (isFlashOn) ScanColor else Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }

            // Camera Switch
            IconButton(
                onClick = onCameraSwitch,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Cameraswitch,
                    contentDescription = "Switch camera",
                    tint = Color.White,
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}

// Minimalist Scanning Frame inspired by the design image
@Composable
fun MinimalistScanningFrame(
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition(label = "scanning_animation")

    // Continuous up and down scanning line animation
    val scanLinePosition by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 2500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scan_line_position"
    )

    // Scanning line opacity animation for breathing effect
    val scanLineOpacity by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = 1500, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "scan_line_opacity"
    )

    Box(
        modifier = modifier.size(280.dp)
    ) {
        // Simple green rounded rectangle frame
        Box(
            modifier = Modifier
                .fillMaxSize()
                .border(
                    width = 3.dp,
                    color = ScanColor,
                    shape = RoundedCornerShape(20.dp)
                )
        )

        // Enhanced continuous scanning line with glow effect
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(3.dp)
                .offset(y = (260.dp * scanLinePosition) + 10.dp)
                .padding(horizontal = 16.dp)
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            ScanColor.copy(alpha = scanLineOpacity * 0.3f),
                            ScanColor.copy(alpha = scanLineOpacity),
                            ScanColor.copy(alpha = scanLineOpacity),
                            ScanColor.copy(alpha = scanLineOpacity * 0.3f),
                            Color.Transparent
                        )
                    ),
                    shape = RoundedCornerShape(2.dp)
                )
        )

        // Additional glow effect for the scanning line
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(6.dp)
                .offset(y = (260.dp * scanLinePosition) + 8.5.dp)
                .padding(horizontal = 14.dp)
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            ScanColor.copy(alpha = scanLineOpacity * 0.1f),
                            ScanColor.copy(alpha = scanLineOpacity * 0.2f),
                            ScanColor.copy(alpha = scanLineOpacity * 0.1f),
                            Color.Transparent
                        )
                    ),
                    shape = RoundedCornerShape(3.dp)
                )
        )

        // Simple corner indicators
        val cornerSize = 20.dp
        val cornerThickness = 3.dp

        // Top-left corner
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(8.dp)
        ) {
            // Horizontal line
            Box(
                modifier = Modifier
                    .size(cornerSize, cornerThickness)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
            // Vertical line
            Box(
                modifier = Modifier
                    .size(cornerThickness, cornerSize)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
        }

        // Top-right corner
        Box(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(8.dp)
        ) {
            // Horizontal line
            Box(
                modifier = Modifier
                    .size(cornerSize, cornerThickness)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
            // Vertical line
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(cornerThickness, cornerSize)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
        }

        // Bottom-left corner
        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(8.dp)
        ) {
            // Horizontal line
            Box(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .size(cornerSize, cornerThickness)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
            // Vertical line
            Box(
                modifier = Modifier
                    .size(cornerThickness, cornerSize)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
        }

        // Bottom-right corner
        Box(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(8.dp)
        ) {
            // Horizontal line
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerSize, cornerThickness)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
            // Vertical line
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .size(cornerThickness, cornerSize)
                    .background(ScanColor, RoundedCornerShape(2.dp))
            )
        }
    }
}

// Minimalist Upload Button inspired by the design image
@Composable
fun MinimalistUploadButton(
    isProcessingImage: Boolean,
    onImageUpload: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .clickable(enabled = !isProcessingImage) { onImageUpload() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        if (isProcessingImage) {
            CircularProgressIndicator(
                modifier = Modifier.size(20.dp),
                strokeWidth = 2.dp,
                color = Color.White
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                "Processing...",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
        } else {
            Icon(
                imageVector = Icons.Default.PhotoLibrary,
                contentDescription = reactiveStringResource(R.string.cd_upload_qr_image),
                modifier = Modifier.size(20.dp),
                tint = Color.White
            )
            Spacer(modifier = Modifier.width(12.dp))
            Text(
                reactiveStringResource(R.string.scanner_upload_image),
                style = MaterialTheme.typography.bodyMedium,
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

// Minimalist Zoom Slider inspired by the design image
@Composable
fun InstructionalTextCard(
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth(),
        shape = RoundedCornerShape(28.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.95f)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 8.dp
        )
    ) {
        Text(
            text = reactiveStringResource(R.string.scanner_instruction),
            style = QRSparkTextStyles.cardSubtitle.copy(
                fontSize = 16.sp,
                color = Color.Black.copy(alpha = 0.8f),
                textAlign = TextAlign.Center
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp, horizontal = 20.dp)
        )
    }
}

@Composable
fun MinimalistZoomSlider(
    zoomLevel: Float,
    showZoomControls: Boolean,
    onZoomChange: (Float) -> Unit,
    onZoomIn: () -> Unit,
    onZoomOut: () -> Unit,
    modifier: Modifier = Modifier
) {
    androidx.compose.animation.AnimatedVisibility(
        visible = showZoomControls,
        enter = androidx.compose.animation.fadeIn(
            animationSpec = tween(durationMillis = 300)
        ),
        exit = androidx.compose.animation.fadeOut(
            animationSpec = tween(durationMillis = 300)
        ),
        modifier = modifier
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 40.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Zoom Out Button
            IconButton(
                onClick = onZoomOut,
                enabled = zoomLevel > 1f,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ZoomOut,
                    contentDescription = "Zoom Out",
                    tint = if (zoomLevel > 1f) ScanColor else Color.White.copy(alpha = 0.5f),
                    modifier = Modifier.size(24.dp)
                )
            }

            // Enhanced Interactive Zoom Slider with proper thumb positioning
            BoxWithConstraints(
                modifier = Modifier
                    .weight(1f)
                    .height(24.dp) // Increased height for better touch target
                    .pointerInput(Unit) {
                        detectDragGestures { change, _ ->
                            val newProgress = (change.position.x / size.width).coerceIn(0f, 1f)
                            val newZoomLevel = 1f + (newProgress * 9f)
                            onZoomChange(newZoomLevel.coerceIn(1f, 10f))
                        }
                    }
            ) {
                // Background track
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(4.dp)
                        .align(Alignment.Center)
                        .background(
                            color = Color.White.copy(alpha = 0.3f),
                            shape = RoundedCornerShape(2.dp)
                        )
                )

                // Progress indicator
                val progress = ((zoomLevel - 1f) / 9f).coerceIn(0f, 1f)
                Box(
                    modifier = Modifier
                        .fillMaxWidth(progress)
                        .height(4.dp)
                        .align(Alignment.CenterStart)
                        .background(
                            color = ScanColor,
                            shape = RoundedCornerShape(2.dp)
                        )
                )

                // Properly positioned thumb indicator with shadow
                val thumbPosition = (maxWidth - 16.dp) * progress
                Box(
                    modifier = Modifier
                        .size(16.dp)
                        .offset(x = thumbPosition)
                        .background(
                            color = Color.White,
                            shape = CircleShape
                        )
                        .border(
                            width = 2.dp,
                            color = ScanColor,
                            shape = CircleShape
                        )
                        .align(Alignment.CenterStart)
                        .shadow(
                            elevation = 4.dp,
                            shape = CircleShape
                        )
                )
            }

            // Zoom In Button
            IconButton(
                onClick = onZoomIn,
                enabled = zoomLevel < 10f,
                modifier = Modifier.size(40.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.ZoomIn,
                    contentDescription = "Zoom In",
                    tint = if (zoomLevel < 10f) ScanColor else Color.White.copy(alpha = 0.5f),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }
}