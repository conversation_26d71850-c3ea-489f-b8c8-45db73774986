package com.example.qr.utils

import android.content.Context
import android.graphics.Bitmap
import android.util.Log
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import java.util.*

/**
 * Enhanced QR generation service that respects user's default format settings
 */
object QRGenerationService {
    
    private const val TAG = "QRGenerationService"
    
    /**
     * Generate QR code with automatic format handling based on user settings
     */
    suspend fun generateQRCodeWithFormat(
        context: Context,
        qrCodeData: QRCodeData,
        bitmap: Bitmap,
        customization: QRCodeCustomization = QRCodeCustomization(size = null),
        overrideFormat: String? = null
    ): QRGenerationResult {
        return try {
            val settingsRepository = SettingsRepository(context)
            
            // Get user's preferred format or use override
            val targetFormat = overrideFormat ?: settingsRepository.defaultQRFormat.first()
            val autoSaveEnabled = settingsRepository.autoSaveGenerated.first()
            
            Log.d(TAG, "Generating QR code in format: $targetFormat, auto-save: $autoSaveEnabled")
            
            // Generate the QR code in the specified format
            val saveResult = if (autoSaveEnabled) {
                ShareUtils.saveQRCodeInFormat(
                    context = context,
                    bitmap = bitmap,
                    qrCodeData = qrCodeData,
                    format = targetFormat
                )
            } else {
                // If auto-save is disabled, we still generate but don't save
                ShareUtils.SaveResult.Success("temp_${System.currentTimeMillis()}", "memory")
            }
            
            when (saveResult) {
                is ShareUtils.SaveResult.Success -> {
                    QRGenerationResult.Success(
                        qrCodeData = qrCodeData,
                        bitmap = bitmap,
                        format = targetFormat,
                        savedToDevice = autoSaveEnabled,
                        filePath = if (autoSaveEnabled) saveResult.path else null,
                        fileName = if (autoSaveEnabled) saveResult.fileName else null
                    )
                }
                is ShareUtils.SaveResult.Error -> {
                    QRGenerationResult.Error("Failed to save QR code: ${saveResult.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in QR generation with format", e)
            QRGenerationResult.Error("QR generation failed: ${e.message}")
        }
    }
    
    /**
     * Generate QR code bitmap with user's preferred size settings
     */
    suspend fun generateQRBitmap(
        context: Context,
        content: String,
        customization: QRCodeCustomization? = null
    ): Bitmap? {
        return try {
            val settingsRepository = SettingsRepository(context)
            val defaultSize = settingsRepository.defaultQRSize.first()

            // Convert size setting to pixels
            val sizeInPixels = settingsRepository.getQRSizePixels(defaultSize)

            // Determine the final size to use
            val finalSize = customization?.size ?: sizeInPixels

            // Create final customization with proper size
            val finalCustomization = if (customization != null) {
                QRCodeCustomization(
                    foregroundColor = customization.foregroundColor,
                    backgroundColor = customization.backgroundColor,
                    size = finalSize,
                    errorCorrectionLevel = customization.errorCorrectionLevel,
                    logoPath = customization.logoPath,
                    logoSize = customization.logoSize
                )
            } else {
                QRCodeCustomization(size = finalSize)
            }

            Log.d(TAG, "Generating QR with size: ${finalCustomization.size}px (user default: $defaultSize = ${sizeInPixels}px)")

            QRCodeGenerator.generateQRCode(
                content = content,
                width = finalCustomization.size!!,
                height = finalCustomization.size!!,
                foregroundColor = finalCustomization.foregroundColor,
                backgroundColor = finalCustomization.backgroundColor,
                errorCorrectionLevel = finalCustomization.errorCorrectionLevel
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error generating QR bitmap", e)
            null
        }
    }
    
    /**
     * Complete QR generation flow with format handling
     */
    suspend fun generateCompleteQRCode(
        context: Context,
        type: QRCodeType,
        content: String,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ): QRGenerationResult {
        return try {
            // Generate bitmap
            val bitmap = generateQRBitmap(context, content, customization)
                ?: return QRGenerationResult.Error("Failed to generate QR code bitmap")
            
            // Analyze content for display name with context for localization
            val analysisResult = QRCodeAnalyzer.analyzeQRCodeContent(content, context)
            
            // Create QR code data
            val qrCodeData = QRCodeData(
                content = content,
                type = type,
                format = type.name,
                displayName = analysisResult.displayName,
                createdAt = Date(),
                isGenerated = true,
                isFavorite = false
            )
            
            // Generate with format handling
            generateQRCodeWithFormat(
                context = context,
                qrCodeData = qrCodeData,
                bitmap = bitmap,
                customization = customization ?: QRCodeCustomization(size = null),
                overrideFormat = overrideFormat
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error in complete QR generation", e)
            QRGenerationResult.Error("Complete QR generation failed: ${e.message}")
        }
    }
    
    /**
     * Batch QR code generation with format consistency
     */
    suspend fun generateBatchQRCodes(
        context: Context,
        qrDataList: List<Pair<QRCodeType, String>>,
        customization: QRCodeCustomization? = null,
        overrideFormat: String? = null
    ): BatchQRGenerationResult {
        val results = mutableListOf<QRGenerationResult>()
        var successCount = 0
        var errorCount = 0
        
        try {
            for ((type, content) in qrDataList) {
                val result = generateCompleteQRCode(
                    context = context,
                    type = type,
                    content = content,
                    customization = customization,
                    overrideFormat = overrideFormat
                )
                
                results.add(result)
                
                when (result) {
                    is QRGenerationResult.Success -> successCount++
                    is QRGenerationResult.Error -> errorCount++
                }
            }
            
            return BatchQRGenerationResult(
                results = results,
                totalCount = qrDataList.size,
                successCount = successCount,
                errorCount = errorCount,
                format = overrideFormat ?: SettingsRepository(context).defaultQRFormat.first()
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error in batch QR generation", e)
            return BatchQRGenerationResult(
                results = results,
                totalCount = qrDataList.size,
                successCount = successCount,
                errorCount = errorCount + (qrDataList.size - results.size),
                format = "PNG",
                error = "Batch generation failed: ${e.message}"
            )
        }
    }
    
    /**
     * Get format-specific feedback message
     */
    fun getFormatFeedbackMessage(format: String, fileName: String?, filePath: String?): String {
        return when (format.uppercase()) {
            "PDF" -> "QR code saved as PDF${if (filePath != null) " in $filePath" else ""}"
            "JPEG" -> "QR code saved as JPEG${if (filePath != null) " in $filePath" else ""}"
            "PNG" -> "QR code saved as PNG${if (filePath != null) " in $filePath" else ""}"
            else -> "QR code saved${if (fileName != null) " as $fileName" else ""}"
        }
    }
}

/**
 * Result classes for QR generation operations
 */
sealed class QRGenerationResult {
    data class Success(
        val qrCodeData: QRCodeData,
        val bitmap: Bitmap,
        val format: String,
        val savedToDevice: Boolean,
        val filePath: String?,
        val fileName: String?
    ) : QRGenerationResult()
    
    data class Error(val message: String) : QRGenerationResult()
}

data class BatchQRGenerationResult(
    val results: List<QRGenerationResult>,
    val totalCount: Int,
    val successCount: Int,
    val errorCount: Int,
    val format: String,
    val error: String? = null
)
