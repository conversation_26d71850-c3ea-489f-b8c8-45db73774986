package com.example.qr.presentation.camera.analyzer

import android.util.Log
import androidx.annotation.OptIn
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.barcode.BarcodeScanning
import com.google.mlkit.vision.common.InputImage

/**
 * QR Code analyzer for CameraX ImageAnalysis
 */
class QRCodeAnalyzer(
    private val onQRCodeDetected: (String) -> Unit
) : ImageAnalysis.Analyzer {

    private val scanner = BarcodeScanning.getClient()
    private var isProcessing = false

    @OptIn(ExperimentalGetImage::class)
    override fun analyze(imageProxy: ImageProxy) {
        // Prevent multiple simultaneous processing
        if (isProcessing) {
            imageProxy.close()
            return
        }

        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            isProcessing = true
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)

            scanner.process(image)
                .addOnSuccessListener { barcodes ->
                    try {
                        if (barcodes.isNotEmpty()) {
                            barcodes.firstOrNull()?.rawValue?.let { value ->
                                Log.d("QRCodeAnalyzer", "QR Code detected: $value")
                                onQRCodeDetected(value)
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("QRCodeAnalyzer", "Error processing detected QR code", e)
                    }
                }
                .addOnFailureListener { exception ->
                    Log.e("QRCodeAnalyzer", "Barcode scanning failed", exception)
                }
                .addOnCompleteListener {
                    try {
                        imageProxy.close()
                    } catch (e: Exception) {
                        Log.e("QRCodeAnalyzer", "Error closing image proxy", e)
                    } finally {
                        isProcessing = false
                    }
                }
        } else {
            imageProxy.close()
            isProcessing = false
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            scanner.close()
            isProcessing = false
            Log.d("QRCodeAnalyzer", "Analyzer cleaned up")
        } catch (e: Exception) {
            Log.e("QRCodeAnalyzer", "Error cleaning up analyzer", e)
        }
    }
}
