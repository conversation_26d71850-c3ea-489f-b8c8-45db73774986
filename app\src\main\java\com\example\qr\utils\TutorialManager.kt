package com.example.qr.utils

import android.content.Context
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.example.qr.R
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first

/**
 * TutorialManager - Manages tutorial display based on user settings
 * Controls when and how tutorials are shown throughout the app
 */
object TutorialManager {
    
    // Tutorial completion tracking keys
    private const val PREF_SCANNER_TUTORIAL_SHOWN = "scanner_tutorial_shown"
    private const val PREF_GENERATOR_TUTORIAL_SHOWN = "generator_tutorial_shown"
    private const val PREF_HISTORY_TUTORIAL_SHOWN = "history_tutorial_shown"
    private const val PREF_SETTINGS_TUTORIAL_SHOWN = "settings_tutorial_shown"
    private const val PREF_HOME_TUTORIAL_SHOWN = "home_tutorial_shown"
    
    /**
     * Check if tutorials should be shown based on settings
     */
    @Composable
    fun shouldShowTutorials(): Boolean {
        val context = LocalContext.current
        val settingsRepository = remember { SettingsRepository(context) }
        val showTutorials by settingsRepository.showTutorials.collectAsState(initial = true)
        return showTutorials
    }
    
    /**
     * Check if a specific tutorial should be shown
     */
    @Composable
    fun shouldShowTutorial(tutorialKey: String): Boolean {
        val context = LocalContext.current
        val settingsRepository = remember { SettingsRepository(context) }
        val showTutorials by settingsRepository.showTutorials.collectAsState(initial = true)
        
        if (!showTutorials) return false
        
        // Check if this specific tutorial has been shown before
        val prefs = context.getSharedPreferences("tutorial_prefs", Context.MODE_PRIVATE)
        return !prefs.getBoolean(tutorialKey, false)
    }
    
    /**
     * Mark a tutorial as shown
     */
    fun markTutorialAsShown(context: Context, tutorialKey: String) {
        val prefs = context.getSharedPreferences("tutorial_prefs", Context.MODE_PRIVATE)
        prefs.edit().putBoolean(tutorialKey, true).apply()
    }
    
    /**
     * Reset all tutorial states (for settings reset)
     */
    fun resetAllTutorials(context: Context) {
        val prefs = context.getSharedPreferences("tutorial_prefs", Context.MODE_PRIVATE)
        prefs.edit().clear().apply()
    }
    
    /**
     * Suspend function to check if tutorials should be shown (for ViewModels)
     */
    suspend fun shouldShowTutorials(settingsRepository: SettingsRepository): Boolean {
        return settingsRepository.showTutorials.first()
    }
    
    /**
     * Suspend function to check specific tutorial (for ViewModels)
     */
    suspend fun shouldShowTutorial(
        context: Context,
        settingsRepository: SettingsRepository,
        tutorialKey: String
    ): Boolean {
        val showTutorials = shouldShowTutorials(settingsRepository)
        if (!showTutorials) return false
        
        val prefs = context.getSharedPreferences("tutorial_prefs", Context.MODE_PRIVATE)
        return !prefs.getBoolean(tutorialKey, false)
    }
    
    // Tutorial keys for different screens
    object TutorialKeys {
        const val HOME_SCREEN = PREF_HOME_TUTORIAL_SHOWN
        const val SCANNER_SCREEN = PREF_SCANNER_TUTORIAL_SHOWN
        const val GENERATOR_SCREEN = PREF_GENERATOR_TUTORIAL_SHOWN
        const val HISTORY_SCREEN = PREF_HISTORY_TUTORIAL_SHOWN
        const val SETTINGS_SCREEN = PREF_SETTINGS_TUTORIAL_SHOWN
        const val FIRST_SCAN = "first_scan_tutorial"
        const val FIRST_GENERATE = "first_generate_tutorial"
        const val CAMERA_CONTROLS = "camera_controls_tutorial"
        const val QR_TYPES = "qr_types_tutorial"
        const val SHARING_FEATURES = "sharing_features_tutorial"
    }
}

/**
 * Tutorial data class for defining tutorial content
 */
data class TutorialStep(
    val title: String,
    val description: String,
    val targetKey: String? = null, // For highlighting specific UI elements
    val action: String? = null // Action button text
)

/**
 * Tutorial configuration for different screens
 */
object TutorialContent {

    fun getHomeTutorials(context: android.content.Context): List<TutorialStep> = listOf(
        TutorialStep(
            title = context.getString(R.string.tutorial_welcome_title),
            description = context.getString(R.string.tutorial_welcome_description),
            action = context.getString(R.string.action_get_started)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_scanning_title),
            description = context.getString(R.string.tutorial_scanning_description),
            targetKey = "scan_button",
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_generation_title),
            description = context.getString(R.string.tutorial_generation_description),
            targetKey = "generate_button",
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_management_title),
            description = context.getString(R.string.tutorial_management_description),
            targetKey = "collection_button",
            action = context.getString(R.string.action_finish)
        )
    )
    
    fun getScannerTutorials(context: android.content.Context): List<TutorialStep> = listOf(
        TutorialStep(
            title = context.getString(R.string.tutorial_scanner_camera_title),
            description = context.getString(R.string.tutorial_scanner_camera_description),
            action = context.getString(R.string.action_ok)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_scanner_upload_title),
            description = context.getString(R.string.tutorial_scanner_upload_description),
            targetKey = "upload_button",
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_scanner_controls_title),
            description = context.getString(R.string.tutorial_scanner_controls_description),
            targetKey = "camera_controls",
            action = context.getString(R.string.action_finish)
        )
    )
    
    fun getGeneratorTutorials(context: android.content.Context): List<TutorialStep> = listOf(
        TutorialStep(
            title = context.getString(R.string.tutorial_generator_type_title),
            description = context.getString(R.string.tutorial_generator_type_description),
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_generator_content_title),
            description = context.getString(R.string.tutorial_generator_content_description),
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_generator_share_title),
            description = context.getString(R.string.tutorial_generator_share_description),
            action = context.getString(R.string.action_finish)
        )
    )

    fun getHistoryTutorials(context: android.content.Context): List<TutorialStep> = listOf(
        TutorialStep(
            title = context.getString(R.string.tutorial_history_collection_title),
            description = context.getString(R.string.tutorial_history_collection_description),
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_history_organization_title),
            description = context.getString(R.string.tutorial_history_organization_description),
            action = context.getString(R.string.action_continue)
        ),
        TutorialStep(
            title = context.getString(R.string.tutorial_history_actions_title),
            description = context.getString(R.string.tutorial_history_actions_description),
            action = context.getString(R.string.action_finish)
        )
    )
}
