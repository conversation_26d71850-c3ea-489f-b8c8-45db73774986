package com.example.qr.presentation.screens.history

import android.content.Context
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.repository.PersistentQRRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class SimpleHistoryViewModel(private val context: Context) : ViewModel() {

    private val repository = PersistentQRRepository(context)

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery.asStateFlow()

    private val _selectedTab = MutableStateFlow(0)
    val selectedTab: StateFlow<Int> = _selectedTab.asStateFlow()

    private val _uiState = MutableStateFlow(HistoryUiState())
    val uiState: StateFlow<HistoryUiState> = _uiState.asStateFlow()

    // Observe QR codes from repository
    val qrCodes: StateFlow<List<QRCodeData>> = repository.qrCodes.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    // Filtered QR codes based on tab and search
    val filteredQRCodes: StateFlow<List<QRCodeData>> = combine(
        qrCodes,
        _selectedTab,
        _searchQuery
    ) { codes, tab, query ->
        val tabFiltered = when (tab) {
            0 -> codes // All
            1 -> codes.filter { !it.isGenerated } // Scanned
            2 -> codes.filter { it.isGenerated } // Generated
            3 -> codes.filter { it.isFavorite } // Favorites
            else -> codes
        }
        
        if (query.isBlank()) {
            tabFiltered.sortedByDescending { it.createdAt }
        } else {
            tabFiltered.filter { qrCode ->
                qrCode.content.contains(query, ignoreCase = true) ||
                qrCode.displayName.contains(query, ignoreCase = true)
            }.sortedByDescending { it.createdAt }
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    init {
        // Initialize sample data if repository is empty
        viewModelScope.launch {
            repository.initializeSampleData()
        }
    }
    
    fun updateSearchQuery(query: String) {
        _searchQuery.value = query
    }
    
    fun selectTab(tabIndex: Int) {
        _selectedTab.value = tabIndex
    }
    
    fun toggleFavorite(qrCode: QRCodeData) {
        viewModelScope.launch {
            try {
                repository.toggleFavorite(qrCode.id)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to update favorite status: ${e.message}"
                )
            }
        }
    }

    fun deleteQRCode(qrCode: QRCodeData) {
        viewModelScope.launch {
            try {
                repository.deleteQRCode(qrCode)
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete QR code: ${e.message}"
                )
            }
        }
    }

    fun deleteAllQRCodes() {
        viewModelScope.launch {
            try {
                repository.clearAll()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete all QR codes: ${e.message}"
                )
            }
        }
    }

    fun deleteNonFavoriteQRCodes() {
        viewModelScope.launch {
            try {
                repository.deleteNonFavorites()
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete non-favorite QR codes: ${e.message}"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class HistoryUiState(
    val isLoading: Boolean = false,
    val error: String? = null
)
