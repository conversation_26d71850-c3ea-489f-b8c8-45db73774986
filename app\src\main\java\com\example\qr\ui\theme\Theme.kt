package com.example.qr.ui.theme


import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.platform.LocalContext

private val DarkColorScheme = darkColorScheme(
    // Primary brand colors
    primary = SparkGreen,
    onPrimary = PureWhite,
    primaryContainer = SparkGreen.copy(alpha = 0.2f),
    onPrimaryContainer = SparkGreen,

    // Secondary brand colors
    secondary = SparkPink,
    onSecondary = PureWhite,
    secondaryContainer = SparkPink.copy(alpha = 0.2f),
    onSecondaryContainer = SparkPink,

    // Tertiary colors
    tertiary = SparkRed,
    onTertiary = PureWhite,
    tertiaryContainer = SparkRed.copy(alpha = 0.2f),
    onTertiaryContainer = SparkRed,

    // Background and surface colors
    background = DarkBackground,
    onBackground = DarkOnSurface,
    surface = DarkSurface,
    onSurface = DarkOnSurface,
    surfaceVariant = DarkSurfaceVariant,
    onSurfaceVariant = DarkOnSurfaceVariant,

    // Outline and borders
    outline = DarkOutline,
    outlineVariant = DarkOutline.copy(alpha = 0.5f),

    // Error colors
    error = ErrorRed,
    onError = PureWhite,
    errorContainer = ErrorRed.copy(alpha = 0.2f),
    onErrorContainer = ErrorRed
)

private val LightColorScheme = lightColorScheme(
    // Primary brand colors
    primary = SparkGreen,
    onPrimary = PureWhite,
    primaryContainer = SparkGreen.copy(alpha = 0.1f),
    onPrimaryContainer = SparkGreen,

    // Secondary brand colors
    secondary = SparkPink,
    onSecondary = PureWhite,
    secondaryContainer = SparkPink.copy(alpha = 0.1f),
    onSecondaryContainer = SparkPink,

    // Tertiary colors
    tertiary = SparkRed,
    onTertiary = PureWhite,
    tertiaryContainer = SparkRed.copy(alpha = 0.1f),
    onTertiaryContainer = SparkRed,

    // Background and surface colors
    background = LightBackground,
    onBackground = LightOnSurface,
    surface = LightSurface,
    onSurface = LightOnSurface,
    surfaceVariant = LightSurfaceVariant,
    onSurfaceVariant = LightOnSurfaceVariant,

    // Outline and borders
    outline = LightOutline,
    outlineVariant = LightOutline.copy(alpha = 0.5f),

    // Error colors
    error = ErrorRed,
    onError = PureWhite,
    errorContainer = ErrorRed.copy(alpha = 0.1f),
    onErrorContainer = ErrorRed
)

@Composable
fun QRTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is disabled for consistent QR Spark branding
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        // Disable dynamic colors to maintain QR Spark brand consistency
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    // Preload fonts for better performance
    LaunchedEffect(Unit) {
        FontPreloader.preloadFonts()
    }

    val typography = rememberQRSparkTypography()

    MaterialTheme(
        colorScheme = colorScheme,
        typography = typography,
        content = content
    )
}

/**
 * QR Theme with settings-based theme selection
 */
@Composable
fun QRThemeWithSettings(
    themeMode: String = "System",
    content: @Composable () -> Unit
) {
    val systemInDarkTheme = isSystemInDarkTheme()

    val darkTheme = when (themeMode) {
        "Light" -> false
        "Dark" -> true
        "System" -> systemInDarkTheme
        else -> systemInDarkTheme
    }

    QRTheme(
        darkTheme = darkTheme,
        dynamicColor = false, // Maintain QR Spark brand consistency
        content = content
    )
}