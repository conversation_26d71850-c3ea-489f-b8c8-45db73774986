# QR Spark - Data Safety Declaration for Google Play

## Data Collection Summary
**Does your app collect or share any of the required user data types?**
- **Answer**: No (by default) / Yes (if user enables analytics/crash reporting)

## Detailed Data Safety Responses

### 1. Location
- **Collected**: No
- **Shared**: No
- **Reason**: App does not access or use location data

### 2. Personal Info
- **Collected**: No
- **Shared**: No
- **Reason**: App does not collect names, emails, or personal identifiers

### 3. Financial Info
- **Collected**: No
- **Shared**: No
- **Reason**: App is free with no in-app purchases or financial features

### 4. Health and Fitness
- **Collected**: No
- **Shared**: No
- **Reason**: App does not access health or fitness data

### 5. Messages
- **Collected**: No
- **Shared**: No
- **Reason**: App does not access SMS, email, or messaging data

### 6. Photos and Videos
- **Collected**: No
- **Shared**: No
- **Reason**: Camera is used only for QR scanning, no photos stored

### 7. Audio Files
- **Collected**: No
- **Shared**: No
- **Reason**: App does not access or use audio files

### 8. Files and Docs
- **Collected**: No
- **Shared**: No
- **Reason**: App only saves QR codes locally, no access to user files

### 9. Calendar
- **Collected**: No
- **Shared**: No
- **Reason**: App does not access calendar data

### 10. Contacts
- **Collected**: No
- **Shared**: No
- **Reason**: App does not access contact information

### 11. App Activity
- **Collected**: Yes (optional)
- **Shared**: No
- **Data Types**: App interactions, in-app search history, installed apps, other user-generated content
- **Collection Purpose**: Analytics (optional, user can disable)
- **Data Handling**: 
  - Encrypted in transit: Yes
  - Can be deleted: Yes
  - User can request deletion: Yes

### 12. Web Browsing
- **Collected**: No
- **Shared**: No
- **Reason**: App does not track web browsing

### 13. App Info and Performance
- **Collected**: Yes (optional)
- **Shared**: No
- **Data Types**: Crash logs, diagnostics, other app performance data
- **Collection Purpose**: App functionality, analytics
- **Data Handling**:
  - Encrypted in transit: Yes
  - Can be deleted: Yes
  - User can request deletion: Yes

### 14. Device or Other IDs
- **Collected**: No
- **Shared**: No
- **Reason**: App does not collect device identifiers

## Data Security Practices

### Encryption
- **Data encrypted in transit**: Yes (for optional analytics/crash reports)
- **Data encrypted at rest**: Yes (local device storage)

### Data Deletion
- **Users can request data deletion**: Yes
- **How**: Through app settings or by contacting support
- **What gets deleted**: All locally stored QR codes and app data

### Data Retention
- **Local data**: Stored until user deletes or uninstalls app
- **Analytics data**: Retained for maximum 26 months (if enabled)
- **Crash reports**: Retained for maximum 90 days (if enabled)

## User Controls

### Opt-in Requirements
- Analytics: User must explicitly enable in settings
- Crash reporting: User must explicitly enable in settings
- Default state: All data collection disabled

### User Rights
- View collected data: Through app settings
- Delete data: Through app settings or app uninstall
- Disable collection: Through app settings
- Export data: QR codes can be exported/shared

## Target Audience
- **Primary**: General audience (13+)
- **Content rating**: Everyone
- **Child-directed**: No

## Additional Notes
- App functions completely offline
- No third-party data sharing
- No advertising or tracking
- User has complete control over data collection
- Privacy-first design philosophy
