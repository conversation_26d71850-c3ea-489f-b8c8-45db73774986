package com.example.qr.utils

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Centralized safe area utilities for consistent handling across the QR Spark application
 */
object SafeAreaUtils {
    
    /**
     * Standard safe area modifier for full-screen content
     */
    @Composable
    fun Modifier.safeAreaFullScreen(): Modifier = this
        .fillMaxSize()
        .windowInsetsPadding(WindowInsets.displayCutout)
        .windowInsetsPadding(WindowInsets.systemBars)
    
    /**
     * Safe area modifier for content that needs to avoid system bars
     */
    @Composable
    fun Modifier.safeAreaContent(): Modifier = this
        .windowInsetsPadding(WindowInsets.safeDrawing)
        .windowInsetsPadding(WindowInsets.ime)
    
    /**
     * Safe area modifier for navigation elements
     */
    @Composable
    fun Modifier.safeAreaNavigation(): Modifier = this
        .windowInsetsPadding(WindowInsets.navigationBars)
    
    /**
     * Safe area modifier for status bar area
     */
    @Composable
    fun Modifier.safeAreaStatusBar(): Modifier = this
        .windowInsetsPadding(WindowInsets.statusBars)
    
    /**
     * Safe area modifier for floating elements (FABs, snackbars, etc.)
     */
    @Composable
    fun Modifier.safeAreaFloating(): Modifier = this
        .windowInsetsPadding(WindowInsets.navigationBars)
        .windowInsetsPadding(WindowInsets.ime)
        .windowInsetsPadding(WindowInsets.displayCutout)
    
    /**
     * Standard content padding for screens with responsive design
     */
    val standardHorizontalPadding = 16.dp
    val standardVerticalPadding = 8.dp
    val standardContentPadding = PaddingValues(
        horizontal = standardHorizontalPadding,
        vertical = standardVerticalPadding
    )
    
    /**
     * Responsive padding that adapts to screen size
     */
    @Composable
    fun responsiveContentPadding(): PaddingValues = PaddingValues(
        start = standardHorizontalPadding,
        end = standardHorizontalPadding,
        top = standardVerticalPadding,
        bottom = 24.dp // Extra bottom padding for better scrolling experience
    )
    
    /**
     * Safe area window insets for TopAppBar
     */
    @get:Composable
    val topAppBarWindowInsets: WindowInsets
        get() = WindowInsets.statusBars

    /**
     * Safe area window insets for Scaffold content
     */
    @get:Composable
    val scaffoldContentWindowInsets: WindowInsets
        get() = WindowInsets.safeDrawing
}

/**
 * Extension functions for common safe area patterns
 */

/**
 * Apply safe area handling for screen-level composables
 */
@Composable
fun Modifier.screenSafeArea(): Modifier = this
    .fillMaxSize()
    .windowInsetsPadding(WindowInsets.displayCutout)

/**
 * Apply safe area handling for scrollable content
 */
@Composable
fun Modifier.scrollableSafeArea(): Modifier = this
    .windowInsetsPadding(WindowInsets.navigationBars)
    .windowInsetsPadding(WindowInsets.ime)

/**
 * Apply safe area handling for dialog and bottom sheet content
 */
@Composable
fun Modifier.dialogSafeArea(): Modifier = this
    .windowInsetsPadding(WindowInsets.ime)
    .windowInsetsPadding(WindowInsets.displayCutout)
