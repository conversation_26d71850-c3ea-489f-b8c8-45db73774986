package com.example.qr.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.OpenInNew
import androidx.compose.material.icons.automirrored.filled.VolumeUp
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.platform.LocalContext
import com.example.qr.R
import com.example.qr.ui.theme.*
import com.example.qr.utils.AnimationManager
import com.example.qr.utils.LanguageManager
import java.util.UUID

/**
 * InfoDialog - Shows helpful information about settings with QR Spark design
 * Respects animation settings and provides consistent styling
 */
@Composable
fun InfoDialog(
    title: String,
    description: String,
    icon: ImageVector = Icons.Default.Info,
    iconColor: Color = InfoBlue,
    onDismiss: () -> Unit,
    actionText: String = "Got it",
    onAction: (() -> Unit)? = null
) {
    // Use standard AlertDialog for maximum stability - no custom animations
    AlertDialog(
        onDismissRequest = onDismiss,
        icon = {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = iconColor,
                modifier = Modifier.size(32.dp)
            )
        },
        title = {
            Text(
                text = title,
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold
            )
        },
        text = {
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
                lineHeight = 20.sp
            )
        },
        confirmButton = {
            Button(
                onClick = {
                    onAction?.invoke()
                    onDismiss()
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = iconColor,
                    contentColor = Color.White
                ),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = actionText,
                    fontWeight = FontWeight.Medium
                )
            }
        },
        dismissButton = {
            TextButton(
                onClick = onDismiss
            ) {
                Text(
                    text = "Cancel",
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        shape = RoundedCornerShape(20.dp),
        containerColor = MaterialTheme.colorScheme.surface,
        tonalElevation = 6.dp
    )
}

/**
 * Settings-specific info dialog with predefined content
 */
@Composable
fun SettingsInfoDialog(
    settingType: SettingInfoType,
    onDismiss: () -> Unit,
    onAction: (() -> Unit)? = null
) {
    // Simplified approach without complex language reset logic
    val info = getSettingInfo(settingType)

    InfoDialog(
        title = info.title,
        description = info.description,
        icon = info.icon,
        iconColor = info.iconColor,
        onDismiss = onDismiss,
        actionText = info.actionText,
        onAction = onAction
    )
}

/**
 * Setting info types for predefined dialogs
 */
enum class SettingInfoType {
    AUTO_SAVE_SCANNED,
    PLAY_SOUND_ON_SCAN,
    SHOW_TUTORIALS,
    ENABLE_ANIMATIONS,
    VIBRATE_ON_SCAN,
    AUTO_OPEN_LINKS,
    AUTO_SAVE_GENERATED
}

/**
 * Setting info data class
 */
data class SettingInfo(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val iconColor: Color,
    val actionText: String = "Got it"
)

/**
 * Get predefined setting information
 */
@Composable
fun getSettingInfo(type: SettingInfoType): SettingInfo {
    return when (type) {
        SettingInfoType.AUTO_SAVE_SCANNED -> SettingInfo(
            title = stringResource(R.string.info_auto_save_scanned_title),
            description = stringResource(R.string.info_auto_save_scanned_description),
            icon = Icons.Default.Save,
            iconColor = ScanColor
        )

        SettingInfoType.PLAY_SOUND_ON_SCAN -> SettingInfo(
            title = stringResource(R.string.info_sound_feedback_title),
            description = stringResource(R.string.info_sound_feedback_description),
            icon = Icons.AutoMirrored.Filled.VolumeUp,
            iconColor = InfoBlue,
            actionText = stringResource(R.string.action_test_sound)
        )

        SettingInfoType.SHOW_TUTORIALS -> SettingInfo(
            title = stringResource(R.string.info_tutorial_guidance_title),
            description = stringResource(R.string.info_tutorial_guidance_description),
            icon = Icons.Default.School,
            iconColor = InfoBlue
        )

        SettingInfoType.ENABLE_ANIMATIONS -> SettingInfo(
            title = stringResource(R.string.info_ui_animations_title),
            description = stringResource(R.string.info_ui_animations_description),
            icon = Icons.Default.PlayArrow,
            iconColor = SparkGreen
        )

        SettingInfoType.VIBRATE_ON_SCAN -> SettingInfo(
            title = stringResource(R.string.info_haptic_feedback_title),
            description = stringResource(R.string.info_haptic_feedback_description),
            icon = Icons.Default.Vibration,
            iconColor = SparkGreen
        )

        SettingInfoType.AUTO_OPEN_LINKS -> SettingInfo(
            title = stringResource(R.string.info_auto_open_links_title),
            description = stringResource(R.string.info_auto_open_links_description),
            icon = Icons.AutoMirrored.Filled.OpenInNew,
            iconColor = WarningOrange
        )

        SettingInfoType.AUTO_SAVE_GENERATED -> SettingInfo(
            title = stringResource(R.string.info_auto_save_generated_title),
            description = stringResource(R.string.info_auto_save_generated_description),
            icon = Icons.Default.Download,
            iconColor = SuccessGreen
        )
    }
}
