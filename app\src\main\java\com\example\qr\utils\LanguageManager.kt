package com.example.qr.utils

import android.app.Activity
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.util.Log
import com.example.qr.data.model.Language
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Locale

object LanguageManager {

    // Reactive language state for immediate translation
    private val _currentLanguage = MutableStateFlow(Language.getSystemLanguage())
    val currentLanguage: StateFlow<Language> = _currentLanguage.asStateFlow()

    // Current language state for immediate access
    var currentLanguageState: Language = Language.getSystemLanguage()
        private set

    /**
     * Initialize language from saved preferences
     */
    fun initializeLanguage(context: Context, languageCode: String) {
        val language = Language.getLanguageByCode(languageCode) ?: Language.getSystemLanguage()
        currentLanguageState = language
        _currentLanguage.value = language
        Log.d("LanguageManager", "Initialized language: ${language.nativeName} (${language.code})")
    }

    /**
     * Update language immediately without activity restart
     * This triggers recomposition of all UI elements
     */
    fun updateLanguageImmediately(context: Context, language: Language) {
        Log.d("LanguageManager", "Updating language immediately to: ${language.nativeName} (${language.code})")

        // Update global locale first
        Locale.setDefault(language.locale)

        // Update reactive state first - this triggers recomposition immediately
        currentLanguageState = language
        _currentLanguage.value = language

        // Save to preferences for persistence
        try {
            val sharedPrefs = context.getSharedPreferences("datastore", Context.MODE_PRIVATE)
            sharedPrefs.edit().putString("selected_language", language.code).apply()
            Log.d("LanguageManager", "Language preference saved: ${language.code}")
        } catch (e: Exception) {
            Log.w("LanguageManager", "Could not save language preference: ${e.message}")
        }

        Log.d("LanguageManager", "Language updated successfully - UI will recompose")
    }

    fun setLanguage(context: Context, language: Language): Context {
        val locale = language.locale
        Locale.setDefault(locale)

        val config = Configuration(context.resources.configuration)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            config.setLocale(locale)
        } else {
            @Suppress("DEPRECATION")
            config.locale = locale
        }

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createConfigurationContext(config)
        } else {
            @Suppress("DEPRECATION")
            context.resources.updateConfiguration(config, context.resources.displayMetrics)
            context
        }
    }

    fun updateActivityLanguage(activity: Activity, language: Language) {
        val locale = language.locale
        Locale.setDefault(locale)

        val config = Configuration(activity.resources.configuration)
        config.setLocale(locale)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            activity.createConfigurationContext(config)
        } else {
            @Suppress("DEPRECATION")
            activity.resources.updateConfiguration(config, activity.resources.displayMetrics)
        }

        // Recreate activity to apply language changes
        activity.recreate()
    }

    fun getCurrentLanguage(context: Context): Language {
        val currentLocale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.resources.configuration.locales[0]
        } else {
            @Suppress("DEPRECATION")
            context.resources.configuration.locale
        }

        return Language.getSupportedLanguages().find {
            it.locale.language == currentLocale.language
        } ?: Language.getSupportedLanguages().first()
    }

    fun isRTL(language: Language): Boolean {
        return language.code in listOf("ar", "he", "fa", "ur")
    }
}
