package com.example.qr.ui.components

import android.graphics.Bitmap
import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.animation.core.*
import androidx.compose.animation.animateColorAsState
import androidx.compose.ui.draw.scale
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.res.stringResource
import com.example.qr.R
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.repository.SettingsRepository
import com.example.qr.ui.theme.*
import com.example.qr.utils.ShareUtils
import com.example.qr.ui.theme.QRSparkTextStyles
import com.example.qr.utils.PermissionManager
import com.example.qr.utils.LanguageManager
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.UUID

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QRResultBottomSheet(
    qrCode: QRCodeData,
    bitmap: Bitmap?,
    autoSaved: Boolean,
    onDismiss: () -> Unit,
    onToggleFavorite: () -> Unit,
    onCustomize: (() -> Unit)? = null,
    onSaveToGallery: (() -> Unit)? = null,
    generatedFormat: String? = null,
    formatFeedback: String? = null
) {
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()
    val resetKey = remember { mutableStateOf(UUID.randomUUID()) }

    LaunchedEffect(currentLanguage) {
        resetKey.value = UUID.randomUUID() // Force reset on language change
    }

    key(resetKey.value) {
        QRResultBottomSheetContent(
            qrCode = qrCode,
            bitmap = bitmap,
            autoSaved = autoSaved,
            onDismiss = onDismiss,
            onToggleFavorite = onToggleFavorite,
            onCustomize = onCustomize,
            onSaveToGallery = onSaveToGallery,
            generatedFormat = generatedFormat,
            formatFeedback = formatFeedback
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun QRResultBottomSheetContent(
    qrCode: QRCodeData,
    bitmap: Bitmap?,
    autoSaved: Boolean,
    onDismiss: () -> Unit,
    onToggleFavorite: () -> Unit,
    onCustomize: (() -> Unit)? = null,
    onSaveToGallery: (() -> Unit)? = null,
    generatedFormat: String? = null,
    formatFeedback: String? = null
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val bottomSheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true
    )

    // Reactive string resources that update with language changes
    val messageNoQrImage = stringResource(R.string.message_no_qr_image)
    val messageStoragePermissionRequired = stringResource(R.string.message_storage_permission_required)
    val messageSavedToGallery = stringResource(R.string.message_saved_to_gallery)
    val messageSaveFailed = stringResource(R.string.message_save_failed)
    val messageShareFailed = stringResource(R.string.message_share_failed)
    val actionOk = stringResource(R.string.action_ok)
    val actionRetry = stringResource(R.string.action_retry)

    var isSaving by remember { mutableStateOf(false) }
    var saveSuccess by remember { mutableStateOf(false) }
    var saveError by remember { mutableStateOf(false) }
    var isSharing by remember { mutableStateOf(false) }
    var shareSuccess by remember { mutableStateOf(false) }
    var shareError by remember { mutableStateOf(false) }

    // Enhanced save to gallery function with format support
    fun saveToGalleryWithFeedback() {
        if (bitmap == null) {
            scope.launch {
                snackbarHostState.showSnackbar(
                    message = messageNoQrImage,
                    actionLabel = actionOk
                )
            }
            return
        }

        // Check permissions
        if (!PermissionManager.hasStoragePermission(context)) {
            scope.launch {
                snackbarHostState.showSnackbar(
                    message = messageStoragePermissionRequired,
                    actionLabel = actionOk
                )
            }
            return
        }

        scope.launch {
            isSaving = true
            saveSuccess = false
            saveError = false

            try {
                // Get user's preferred format from settings
                val settingsRepository = SettingsRepository(context)
                val defaultFormat = settingsRepository.defaultQRFormat.first()

                Log.d("QRResultBottomSheet", "Saving QR code in user's preferred format: $defaultFormat")

                // Use the format-aware save method
                when (val result = ShareUtils.saveQRCodeInFormat(
                    context = context,
                    bitmap = bitmap,
                    qrCodeData = qrCode,
                    format = defaultFormat
                )) {
                    is ShareUtils.SaveResult.Success -> {
                        saveSuccess = true

                        // Create format-specific success message
                        val successMessage = when (defaultFormat.uppercase()) {
                            "JPEG" -> "QR code saved as JPEG to Pictures/QR Spark"
                            "PDF" -> "QR code saved as PDF to Documents/QR Spark"
                            else -> messageSavedToGallery
                        }

                        snackbarHostState.showSnackbar(
                            message = successMessage,
                            duration = SnackbarDuration.Short
                        )
                        // Keep success state permanently - don't reset to show it's saved
                        // saveSuccess remains true to indicate the QR code is saved
                    }
                    is ShareUtils.SaveResult.Error -> {
                        saveError = true
                        snackbarHostState.showSnackbar(
                            message = "$messageSaveFailed: ${result.message}",
                            actionLabel = actionRetry
                        )

                        // Quick reset for error
                        kotlinx.coroutines.delay(1500)
                        saveError = false
                    }
                }
            } catch (e: Exception) {
                Log.e("QRResultBottomSheet", "Error saving QR code", e)
                saveError = true
                snackbarHostState.showSnackbar(
                    message = "$messageSaveFailed: ${e.message}",
                    actionLabel = actionRetry
                )
                kotlinx.coroutines.delay(1500)
                saveError = false
            } finally {
                isSaving = false
            }
        }
    }

    // Enhanced share function with format support
    fun shareQRCodeWithFormat(bitmap: Bitmap?, qrCode: QRCodeData) {
        if (bitmap == null) return

        scope.launch {
            isSharing = true
            shareSuccess = false
            shareError = false

            try {
                val result = ShareUtils.shareQRCodeWithFormat(
                    context = context,
                    bitmap = bitmap,
                    qrCodeData = qrCode
                )

                when (result) {
                    is ShareUtils.ShareResult.Success -> {
                        shareSuccess = true
                        snackbarHostState.showSnackbar(
                            message = result.message,
                            duration = SnackbarDuration.Short
                        )

                        // Keep success state visible for 3 seconds for consistency
                        kotlinx.coroutines.delay(3000)
                        shareSuccess = false
                    }
                    is ShareUtils.ShareResult.Error -> {
                        shareError = true
                        snackbarHostState.showSnackbar(
                            message = "$messageShareFailed: ${result.message}",
                            actionLabel = actionRetry
                        )

                        // Quick reset for error
                        kotlinx.coroutines.delay(1500)
                        shareError = false
                    }
                }
            } catch (e: Exception) {
                shareError = true
                snackbarHostState.showSnackbar(
                    message = "$messageShareFailed: ${e.message}",
                    actionLabel = actionRetry
                )
                kotlinx.coroutines.delay(1500)
                shareError = false
            } finally {
                isSharing = false
            }
        }
    }

    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = bottomSheetState,
        containerColor = MaterialTheme.colorScheme.surface,
        contentColor = MaterialTheme.colorScheme.onSurface,
        shape = RoundedCornerShape(topStart = 28.dp, topEnd = 28.dp),
        dragHandle = {
            Surface(
                modifier = Modifier.padding(vertical = 12.dp),
                color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.4f),
                shape = RoundedCornerShape(16.dp)
            ) {
                Box(
                    modifier = Modifier.size(width = 32.dp, height = 4.dp)
                )
            }
        }
    ) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp)
                    .padding(bottom = 32.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
            // Success Header
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(bottom = 16.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .background(
                            color = SuccessGreen.copy(alpha = 0.1f),
                            shape = RoundedCornerShape(24.dp)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = SuccessGreen,
                        modifier = Modifier.size(28.dp)
                    )
                }

                Spacer(modifier = Modifier.width(16.dp))

                Column {
                    Text(
                        text = stringResource(R.string.qr_generated_title),
                        style = QRSparkTextStyles.cardTitle,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Text(
                        text = qrCode.displayName,
                        style = QRSparkTextStyles.cardSubtitle,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    // QR Content Preview
                    if (qrCode.content.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = if (qrCode.content.length > 50)
                                "${qrCode.content.take(50)}..."
                            else qrCode.content,
                            style = QRSparkTextStyles.qrContent,
                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f)
                        )
                    }
                }
            }

            // Auto-save status with format feedback (also consider favorites as saved)
            if (autoSaved || formatFeedback != null || qrCode.isFavorite) {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = SuccessGreen.copy(alpha = 0.1f)
                    ),
                    shape = RoundedCornerShape(12.dp),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    Column(
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 12.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.CloudDone,
                                contentDescription = null,
                                tint = SuccessGreen,
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(6.dp))
                            Text(
                                text = when {
                                    autoSaved -> stringResource(R.string.status_auto_saved)
                                    qrCode.isFavorite -> stringResource(R.string.status_favorited)
                                    formatFeedback != null -> stringResource(R.string.qr_code_generated)
                                    else -> stringResource(R.string.qr_code_generated)
                                },
                                style = QRSparkTextStyles.statusSuccess,
                                color = SuccessGreen
                            )

                            // Format badge
                            if (generatedFormat != null) {
                                Spacer(modifier = Modifier.width(8.dp))
                                Surface(
                                    color = SuccessGreen.copy(alpha = 0.2f),
                                    shape = RoundedCornerShape(6.dp)
                                ) {
                                    Text(
                                        text = generatedFormat.uppercase(),
                                        style = QRSparkTextStyles.cardSubtitle,
                                        color = SuccessGreen,
                                        fontWeight = FontWeight.Bold,
                                        modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                                    )
                                }
                            }
                        }

                        // Format feedback message
                        if (formatFeedback != null) {
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = formatFeedback,
                                style = QRSparkTextStyles.cardSubtitle,
                                color = SuccessGreen.copy(alpha = 0.8f),
                                fontSize = 11.sp,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Info,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(6.dp))
                    Text(
                        text = stringResource(R.string.status_not_saved),
                        style = QRSparkTextStyles.statusError,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }

            // QR Code Image
            if (bitmap != null) {
                Card(
                    modifier = Modifier.size(200.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White
                    ),
                    shape = RoundedCornerShape(20.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                ) {
                    Image(
                        bitmap = bitmap.asImageBitmap(),
                        contentDescription = "Generated QR Code",
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Action Buttons Grid
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // First Row: Save to Gallery & Share
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Modern Animated Save to Gallery Button
                    AnimatedSaveButton(
                        onClick = { saveToGalleryWithFeedback() },
                        isLoading = isSaving,
                        isSuccess = saveSuccess,
                        isError = saveError,
                        enabled = bitmap != null,
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp)
                    )

                    // Enhanced Share Button with Format Support
                    EnhancedShareButton(
                        onClick = {
                            shareQRCodeWithFormat(bitmap, qrCode)
                        },
                        enabled = bitmap != null && !isSharing,
                        isLoading = isSharing,
                        isSuccess = shareSuccess,
                        isError = shareError,
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp)
                    )
                }

                // Second Row: Add to Favorites & Customize
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Add to Favorites Button
                    OutlinedButton(
                        onClick = onToggleFavorite,
                        modifier = Modifier
                            .weight(1f)
                            .height(56.dp),
                        shape = RoundedCornerShape(16.dp),
                        border = androidx.compose.foundation.BorderStroke(
                            1.5.dp,
                            if (qrCode.isFavorite) SparkPink else MaterialTheme.colorScheme.outline
                        ),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = if (qrCode.isFavorite) SparkPink else MaterialTheme.colorScheme.onSurface
                        )
                    ) {
                        Icon(
                            imageVector = if (qrCode.isFavorite) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            if (qrCode.isFavorite) stringResource(R.string.action_favorited) else stringResource(R.string.action_add_to_favorites),
                            style = QRSparkTextStyles.buttonMedium
                        )
                    }

                    // Customize Button (if available)
                    if (onCustomize != null) {
                        OutlinedButton(
                            onClick = onCustomize,
                            modifier = Modifier
                                .weight(1f)
                                .height(56.dp),
                            shape = RoundedCornerShape(16.dp),
                            border = androidx.compose.foundation.BorderStroke(
                                1.5.dp,
                                SparkGreen
                            ),
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = SparkGreen
                            )
                        ) {
                            Icon(
                                imageVector = Icons.Default.Edit,
                                contentDescription = null,
                                modifier = Modifier.size(20.dp)
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                stringResource(R.string.qr_generated_customize),
                                style = QRSparkTextStyles.buttonMedium
                            )
                        }
                    } else {
                        // Placeholder to maintain layout
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

                // Close Button
                TextButton(
                    onClick = onDismiss,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Text(
                        stringResource(R.string.action_close),
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontWeight = FontWeight.Medium,
                        fontSize = 16.sp
                    )
                }
            }

            // Simple Snackbar
            SnackbarHost(
                hostState = snackbarHostState,
                modifier = Modifier.align(Alignment.BottomCenter)
            )
        }
    }
}

@Composable
private fun AnimatedSaveButton(
    onClick: () -> Unit,
    isLoading: Boolean,
    isSuccess: Boolean,
    isError: Boolean,
    enabled: Boolean,
    modifier: Modifier = Modifier
) {
    // Enhanced color states with better success visibility
    val borderColor = when {
        isSuccess -> Color.White.copy(alpha = 0.3f) // Subtle white border on green
        isError -> MaterialTheme.colorScheme.error
        isLoading -> GenerateColor.copy(alpha = 0.6f)
        !enabled -> MaterialTheme.colorScheme.outline.copy(alpha = 0.5f)
        else -> GenerateColor
    }

    val contentColor = when {
        isSuccess -> Color.White // White text on green background
        isError -> MaterialTheme.colorScheme.error
        isLoading -> GenerateColor.copy(alpha = 0.6f)
        !enabled -> MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
        else -> GenerateColor
    }

    // Enhanced background color for success state with faster animation
    val backgroundColor by animateColorAsState(
        targetValue = when {
            isSuccess -> SuccessGreen // Full green background when saved
            else -> Color.Transparent
        },
        animationSpec = tween(durationMillis = 150), // Faster animation
        label = "background_color"
    )

    // Faster scale animation for success state
    val scale by animateFloatAsState(
        targetValue = if (isSuccess) 1.02f else 1f, // More subtle scale
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioNoBouncy, // No bounce for faster feel
            stiffness = Spring.StiffnessMedium // Faster animation
        ),
        label = "scale"
    )

    if (isSuccess) {
        // Use filled button when saved
        Button(
            onClick = onClick,
            enabled = false, // Disable clicking when saved
            modifier = modifier.scale(scale),
            shape = RoundedCornerShape(16.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = backgroundColor,
                contentColor = contentColor,
                disabledContainerColor = backgroundColor,
                disabledContentColor = contentColor
            )
        ) {
            // Content for filled button (success state only)
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "Saved successfully",
                tint = Color.White, // White icon on green background
                modifier = Modifier.size(22.dp) // Slightly smaller for better proportion
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = stringResource(R.string.action_saved),
                style = QRSparkTextStyles.buttonMedium.copy(
                    fontWeight = FontWeight.Bold // Bold text for emphasis
                ),
                color = Color.White // White text on green background
            )
        }
    } else {
        // Use outlined button for normal states
        OutlinedButton(
            onClick = onClick,
            enabled = enabled && !isLoading,
            modifier = modifier.scale(scale),
            shape = RoundedCornerShape(16.dp),
            border = androidx.compose.foundation.BorderStroke(
                width = 1.5.dp,
                color = borderColor
            ),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = contentColor,
                disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.5f)
            )
        ) {
            // Content for outlined button (normal states)
            when {
                isLoading -> {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = contentColor
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(stringResource(R.string.action_saving), style = QRSparkTextStyles.buttonMedium)
                }
                isError -> {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(stringResource(R.string.action_failed), style = QRSparkTextStyles.buttonMedium)
                }
                else -> {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = null,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(stringResource(R.string.qr_generated_save_gallery), style = QRSparkTextStyles.buttonMedium)
                }
            }
        }
    }
}

@Composable
private fun EnhancedShareButton(
    onClick: () -> Unit,
    enabled: Boolean,
    modifier: Modifier = Modifier,
    isLoading: Boolean = false,
    isSuccess: Boolean = false,
    isError: Boolean = false
) {

    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = when {
                isSuccess -> SuccessGreen
                isError -> MaterialTheme.colorScheme.error
                else -> SparkPink
            },
            disabledContainerColor = SparkPink.copy(alpha = 0.6f)
        )
    ) {
        when {
            isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(16.dp),
                    strokeWidth = 2.dp,
                    color = Color.White
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_sharing),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
            isSuccess -> {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Shared successfully",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_shared),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
            isError -> {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = "Share failed",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_failed),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
            else -> {
                Icon(
                    imageVector = Icons.Default.Share,
                    contentDescription = "Share QR Code",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    stringResource(R.string.action_share),
                    style = QRSparkTextStyles.buttonMedium,
                    color = Color.White
                )
            }
        }
    }
}
