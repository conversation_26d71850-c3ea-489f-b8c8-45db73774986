package com.example.qr.utils

import android.content.Context
import android.media.AudioManager
import android.media.ToneGenerator
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.os.VibratorManager
import android.util.Log
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class FeedbackManager @Inject constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository,
    private val soundManager: SoundManager
) {
    private val vibrator = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        val vibratorManager = context.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as VibratorManager
        vibratorManager.defaultVibrator
    } else {
        @Suppress("DEPRECATION")
        context.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
    }
    private var toneGenerator: ToneGenerator? = null

    init {
        try {
            toneGenerator = ToneGenerator(AudioManager.STREAM_NOTIFICATION, 100)
        } catch (e: Exception) {
            Log.e("FeedbackManager", "Error initializing ToneGenerator", e)
        }
    }

    /**
     * Provide haptic feedback if enabled in settings
     */
    suspend fun provideHapticFeedback(type: HapticType = HapticType.SUCCESS) {
        try {
            val vibrateEnabled = settingsRepository.vibrateOnScan.first()
            if (!vibrateEnabled) return

            vibrator?.let { vib ->
                val pattern = when (type) {
                    HapticType.SUCCESS -> longArrayOf(0, 100)
                    HapticType.ERROR -> longArrayOf(0, 50, 50, 50)
                    HapticType.WARNING -> longArrayOf(0, 75, 25, 75)
                    HapticType.LIGHT -> longArrayOf(0, 50)
                }

                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    val effect = VibrationEffect.createWaveform(pattern, -1)
                    vib.vibrate(effect)
                } else {
                    @Suppress("DEPRECATION")
                    vib.vibrate(pattern, -1)
                }

                Log.d("FeedbackManager", "Haptic feedback provided: $type")
            }
        } catch (e: Exception) {
            Log.e("FeedbackManager", "Error providing haptic feedback", e)
        }
    }

    /**
     * Provide audio feedback if enabled in settings
     */
    suspend fun provideAudioFeedback(type: AudioType = AudioType.SUCCESS) {
        try {
            when (type) {
                AudioType.SUCCESS -> soundManager.playScanSuccessSound()
                AudioType.ERROR -> soundManager.playScanErrorSound()
                AudioType.WARNING -> soundManager.playPleasantBeep()
            }
            Log.d("FeedbackManager", "Audio feedback provided: $type")
        } catch (e: Exception) {
            Log.e("FeedbackManager", "Error providing audio feedback", e)
        }
    }

    /**
     * Provide combined feedback (haptic + audio) for QR scan success
     */
    suspend fun provideScanSuccessFeedback() {
        provideHapticFeedback(HapticType.SUCCESS)
        provideAudioFeedback(AudioType.SUCCESS)
    }

    /**
     * Provide combined feedback for QR scan error
     */
    suspend fun provideScanErrorFeedback() {
        provideHapticFeedback(HapticType.ERROR)
        provideAudioFeedback(AudioType.ERROR)
    }

    /**
     * Provide light feedback for UI interactions
     */
    suspend fun provideUIFeedback() {
        provideHapticFeedback(HapticType.LIGHT)
    }

    /**
     * Test vibration (used in settings)
     */
    fun testVibration() {
        try {
            vibrator?.let { vib ->
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                    vib.vibrate(VibrationEffect.createOneShot(100, VibrationEffect.DEFAULT_AMPLITUDE))
                } else {
                    @Suppress("DEPRECATION")
                    vib.vibrate(100)
                }
            }
        } catch (e: Exception) {
            Log.e("FeedbackManager", "Error testing vibration", e)
        }
    }

    /**
     * Test sound (used in settings)
     */
    suspend fun testSound() {
        try {
            soundManager.testSound()
        } catch (e: Exception) {
            Log.e("FeedbackManager", "Error testing sound", e)
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            toneGenerator?.release()
            toneGenerator = null
            soundManager.cleanup()
        } catch (e: Exception) {
            Log.e("FeedbackManager", "Error cleaning up FeedbackManager", e)
        }
    }
}

enum class HapticType {
    SUCCESS,
    ERROR,
    WARNING,
    LIGHT
}

enum class AudioType {
    SUCCESS,
    ERROR,
    WARNING
}

// Extension function for easy access in ViewModels
suspend fun FeedbackManager.provideFeedbackIfEnabled(
    hapticType: HapticType = HapticType.SUCCESS,
    audioType: AudioType = AudioType.SUCCESS
) {
    provideHapticFeedback(hapticType)
    provideAudioFeedback(audioType)
}
