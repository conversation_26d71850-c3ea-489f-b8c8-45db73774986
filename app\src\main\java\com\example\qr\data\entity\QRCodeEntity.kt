package com.example.qr.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.example.qr.data.model.QRCodeData
import com.example.qr.data.model.QRCodeType
import java.util.Date

@Entity(tableName = "qr_codes")
@TypeConverters(QRCodeConverters::class)
data class QRCodeEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val content: String,
    val type: QRCodeType,
    val format: String,
    val displayName: String,
    val createdAt: Date,
    val isGenerated: Boolean,
    val isFavorite: Boolean = false,
    val customization: String? = null,
    val filePath: String? = null
)

// Type converters for Room database
class QRCodeConverters {
    
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun fromQRCodeType(type: QRCodeType): String {
        return type.name
    }

    @TypeConverter
    fun toQRCodeType(typeName: String): QRCodeType {
        return try {
            QRCodeType.valueOf(typeName)
        } catch (e: IllegalArgumentException) {
            QRCodeType.OTHER
        }
    }
}

// Extension functions to convert between Entity and Data models
fun QRCodeEntity.toQRCodeData(): QRCodeData {
    return QRCodeData(
        id = this.id,
        content = this.content,
        type = this.type,
        format = this.format,
        displayName = this.displayName,
        createdAt = this.createdAt,
        isGenerated = this.isGenerated,
        isFavorite = this.isFavorite,
        customization = this.customization,
        filePath = this.filePath
    )
}

fun QRCodeData.toQRCodeEntity(): QRCodeEntity {
    return QRCodeEntity(
        id = this.id,
        content = this.content,
        type = this.type,
        format = this.format,
        displayName = this.displayName,
        createdAt = this.createdAt,
        isGenerated = this.isGenerated,
        isFavorite = this.isFavorite,
        customization = this.customization,
        filePath = this.filePath
    )
}
