package com.example.qr.utils

import androidx.compose.animation.core.*
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first

/**
 * AnimationManager - Centralized animation control based on user settings
 * Provides animation specs that respect the "Enable Animations" setting
 */
object AnimationManager {
    
    /**
     * Get animation duration based on settings
     * Returns 0 if animations are disabled, normal duration if enabled
     */
    @Composable
    fun getAnimationDuration(normalDuration: Int): Int {
        val context = LocalContext.current
        val settingsRepository = remember { SettingsRepository(context) }
        val enableAnimations by settingsRepository.enableAnimations.collectAsState(initial = true)
        
        return if (enableAnimations) normalDuration else 0
    }
    
    /**
     * Get tween animation spec that respects animation settings
     */
    @Composable
    fun <T> getTweenSpec(
        durationMillis: Int,
        delayMillis: Int = 0,
        easing: Easing = FastOutSlowInEasing
    ): TweenSpec<T> {
        val duration = getAnimationDuration(durationMillis)
        val delay = getAnimationDuration(delayMillis)
        
        return tween(
            durationMillis = duration,
            delayMillis = delay,
            easing = easing
        )
    }
    
    /**
     * Get spring animation spec that respects animation settings
     */
    @Composable
    fun <T> getSpringSpec(
        dampingRatio: Float = Spring.DampingRatioNoBouncy,
        stiffness: Float = Spring.StiffnessMedium
    ): SpringSpec<T> {
        val context = LocalContext.current
        val settingsRepository = remember { SettingsRepository(context) }
        val enableAnimations by settingsRepository.enableAnimations.collectAsState(initial = true)
        
        return if (enableAnimations) {
            spring(dampingRatio = dampingRatio, stiffness = stiffness)
        } else {
            spring(dampingRatio = Spring.DampingRatioNoBouncy, stiffness = Float.MAX_VALUE)
        }
    }
    
    /**
     * Get infinite repeatable animation spec that respects animation settings
     */
    @Composable
    fun <T> getInfiniteRepeatableSpec(
        animation: DurationBasedAnimationSpec<T>,
        repeatMode: RepeatMode = RepeatMode.Restart
    ): InfiniteRepeatableSpec<T> {
        val context = LocalContext.current
        val settingsRepository = remember { SettingsRepository(context) }
        val enableAnimations by settingsRepository.enableAnimations.collectAsState(initial = true)
        
        return if (enableAnimations) {
            infiniteRepeatable(animation = animation, repeatMode = repeatMode)
        } else {
            // Return a spec that doesn't animate
            infiniteRepeatable(
                animation = tween(durationMillis = 0),
                repeatMode = RepeatMode.Restart
            )
        }
    }
    
    /**
     * Animated float state that respects animation settings
     */
    @Composable
    fun animateFloatAsState(
        targetValue: Float,
        animationSpec: AnimationSpec<Float> = getTweenSpec<Float>(300),
        label: String = "",
        finishedListener: ((Float) -> Unit)? = null
    ): State<Float> {
        return androidx.compose.animation.core.animateFloatAsState(
            targetValue = targetValue,
            animationSpec = animationSpec,
            label = label,
            finishedListener = finishedListener
        )
    }
    
    /**
     * Animated Dp state that respects animation settings
     */
    @Composable
    fun animateDpAsState(
        targetValue: androidx.compose.ui.unit.Dp,
        animationSpec: AnimationSpec<androidx.compose.ui.unit.Dp> = getTweenSpec<androidx.compose.ui.unit.Dp>(300),
        label: String = "",
        finishedListener: ((androidx.compose.ui.unit.Dp) -> Unit)? = null
    ): State<androidx.compose.ui.unit.Dp> {
        return androidx.compose.animation.core.animateDpAsState(
            targetValue = targetValue,
            animationSpec = animationSpec,
            label = label,
            finishedListener = finishedListener
        )
    }
    
    /**
     * Animated Color state that respects animation settings
     */
    @Composable
    fun animateColorAsState(
        targetValue: androidx.compose.ui.graphics.Color,
        animationSpec: AnimationSpec<androidx.compose.ui.graphics.Color> = getTweenSpec<androidx.compose.ui.graphics.Color>(300),
        label: String = "",
        finishedListener: ((androidx.compose.ui.graphics.Color) -> Unit)? = null
    ): State<androidx.compose.ui.graphics.Color> {
        return androidx.compose.animation.animateColorAsState(
            targetValue = targetValue,
            animationSpec = animationSpec,
            label = label,
            finishedListener = finishedListener
        )
    }
    
    /**
     * Check if animations are enabled (suspend function for ViewModels)
     */
    suspend fun areAnimationsEnabled(settingsRepository: SettingsRepository): Boolean {
        return settingsRepository.enableAnimations.first()
    }
    
    /**
     * Get animation duration for ViewModels (suspend function)
     */
    suspend fun getAnimationDuration(
        settingsRepository: SettingsRepository,
        normalDuration: Int
    ): Int {
        val enabled = areAnimationsEnabled(settingsRepository)
        return if (enabled) normalDuration else 0
    }
}
