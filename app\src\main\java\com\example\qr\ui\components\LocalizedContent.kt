package com.example.qr.ui.components

import android.content.Context
import android.content.res.Configuration
import android.util.Log
import androidx.annotation.StringRes
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.example.qr.data.model.Language
import com.example.qr.utils.LanguageManager
import java.util.Locale

/**
 * A composable that provides localized context and automatically recomposes
 * when the language changes, enabling immediate language switching without activity restart.
 */
@Composable
fun LocalizedContent(
    content: @Composable (Context) -> Unit
) {
    val baseContext = LocalContext.current

    // Watch for language changes - this triggers recomposition when language changes
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    // Force recomposition key to ensure UI updates
    var recompositionKey by remember { mutableStateOf(0) }

    // Update recomposition key when language changes
    LaunchedEffect(currentLanguage) {
        recompositionKey++
        Log.d("LocalizedContent", "Language changed to: ${currentLanguage.nativeName}, recomposition key: $recompositionKey")
    }

    // Create localized context using modern approach while preserving Activity context
    val localizedContext = remember(currentLanguage) {
        try {
            val locale = currentLanguage.locale
            Locale.setDefault(locale)

            val config = Configuration(baseContext.resources.configuration)
            config.setLocale(locale)

            // For Activity contexts, preserve the Activity wrapper
            if (baseContext is androidx.activity.ComponentActivity) {
                // Create a wrapper that preserves Activity context while using localized resources
                object : android.content.ContextWrapper(baseContext) {
                    private val localizedResources = baseContext.createConfigurationContext(config).resources

                    override fun getResources(): android.content.res.Resources {
                        return localizedResources
                    }
                }
            } else {
                // Use createConfigurationContext for non-Activity contexts
                val newContext = baseContext.createConfigurationContext(config)
                Log.d("LocalizedContent", "Created localized context for: ${currentLanguage.nativeName}")
                newContext
            }
        } catch (e: Exception) {
            Log.e("LocalizedContent", "Error creating localized context: ${e.message}")
            baseContext // Fallback to original context
        }
    }

    // Provide the localized context to the content
    CompositionLocalProvider(
        LocalContext provides localizedContext
    ) {
        // Use key to force recomposition of entire content tree
        key(recompositionKey) {
            content(localizedContext)
        }
    }
}

/**
 * Hook to get the current language state for immediate access
 */
@Composable
fun rememberCurrentLanguage(): Language {
    return LanguageManager.currentLanguageState
}

/**
 * Hook to trigger immediate language change
 */
@Composable
fun rememberLanguageChanger(): (Language) -> Unit {
    val context = LocalContext.current

    return remember {
        { language: Language ->
            LanguageManager.updateLanguageImmediately(context, language)
        }
    }
}

/**
 * A composable that provides localized strings and automatically updates when language changes
 * This replaces the standard stringResource() to make it reactive to language changes
 */
@Composable
fun LocalizedString(
    @androidx.annotation.StringRes stringRes: Int,
    vararg formatArgs: Any
): String {
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()
    val context = LocalContext.current

    return remember(currentLanguage, stringRes, formatArgs.contentHashCode()) {
        Log.d("LocalizedString", "Getting string for resource $stringRes in language ${currentLanguage.code}")
        if (formatArgs.isEmpty()) {
            context.getString(stringRes)
        } else {
            context.getString(stringRes, *formatArgs)
        }
    }
}

/**
 * Reactive version of stringResource that updates when language changes
 * Use this instead of the standard stringResource() for immediate language switching
 */
@Composable
fun reactiveStringResource(
    @androidx.annotation.StringRes id: Int,
    vararg formatArgs: Any
): String {
    return LocalizedString(id, *formatArgs)
}

/**
 * A composable that wraps content and ensures it recomposes when language changes
 * This should be used to wrap screens that need immediate language updates
 */
@Composable
fun ReactiveLanguageContent(
    content: @Composable () -> Unit
) {
    // Watch for language changes - this triggers recomposition when language changes
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    // Force recomposition key to ensure UI updates
    var recompositionKey by remember { mutableStateOf(0) }

    // Update recomposition key when language changes
    LaunchedEffect(currentLanguage) {
        recompositionKey++
        Log.d("ReactiveLanguageContent", "Language changed to: ${currentLanguage.nativeName}, recomposition key: $recompositionKey")
    }

    // Use key to force recomposition of entire content tree
    key(recompositionKey) {
        content()
    }
}

/**
 * A composable that provides localized strings that update immediately when language changes
 * This ensures strings are always in the current language
 */
@Composable
fun LocalizedString(@StringRes resId: Int): String {
    val context = LocalContext.current
    val currentLanguage by LanguageManager.currentLanguage.collectAsState()

    return remember(resId, currentLanguage) {
        context.resources.getString(resId)
    }
}
