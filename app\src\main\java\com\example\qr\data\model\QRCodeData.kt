package com.example.qr.data.model

import java.util.Date

data class QRCodeData(
    val id: Long = 0,
    val content: String,
    val type: QRCodeType,
    val format: String, // The raw format detected (URL, TEXT, WIFI, etc.)
    val displayName: String, // User-friendly name
    val createdAt: Date,
    val isGenerated: Boolean, // true if generated, false if scanned
    val isFavorite: Boolean = false,
    val customization: String? = null, // JSON string for generator customizations
    val filePath: String? = null // Path to saved QR code image
)

enum class QRCodeType {
    TEXT,
    URL,
    WIFI,
    EMAIL,
    PHONE,
    SMS,
    CONTACT,
    LOCATION,
    CALENDAR,
    OTHER
}
