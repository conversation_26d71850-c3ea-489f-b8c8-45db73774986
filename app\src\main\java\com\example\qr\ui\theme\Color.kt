package com.example.qr.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// QR Spark Brand Colors - Improved Harmony
val SparkGreen = Color(0xFF10B981)   // Emerald Green (more refined)
val SparkPink = Color(0xFFEC4899)    // Pink (kept for contrast)
val SparkBlue = Color(0xFF3B82F6)    // Blue (new primary)
val SparkRed = Color(0xFFEF4444)     // Red (kept for compatibility)

// Action Colors - Functional Color Coding
val ScanColor = Color(0xFF10B981)      // Green for scan (success/go)
val GenerateColor = Color(0xFF3B82F6)  // Blue for generate (primary action)
val CollectionColor = Color(0xFF8B5CF6) // Purple for collection (storage)

// Semantic Colors - Improved Harmony
val SuccessGreen = Color(0xFF10B981)
val WarningOrange = Color(0xFFF59E0B)
val ErrorRed = Color(0xFFEF4444)
val InfoBlue = Color(0xFF3B82F6)

// Brand Gradient - More Harmonious
val BrandGradientStart = Color(0xFF3B82F6)  // Blue
val BrandGradientMiddle = Color(0xFF8B5CF6) // Purple
val BrandGradientEnd = Color(0xFFEC4899)    // Pink

// Neutral Colors
val PureWhite = Color(0xFFFFFFFF)
val PureBlack = Color(0xFF000000)

// Dark Mode Colors
val DarkBackground = Color(0xFF1A1A1A)      // Deep gray background
val DarkSurface = Color(0xFF2D2D2D)         // Light gray cards
val DarkSurfaceVariant = Color(0xFF3A3A3A)  // Elevated surfaces
val DarkOnSurface = Color(0xFFE5E5E5)       // High contrast text
val DarkOnSurfaceVariant = Color(0xFFB8B8B8) // Medium contrast text
val DarkOutline = Color(0xFF4A4A4A)         // Borders and dividers

// Light Mode Colors (for consistency)
val LightBackground = Color(0xFFFAFAFA)     // Light background
val LightSurface = Color(0xFFFFFFFF)        // White cards
val LightSurfaceVariant = Color(0xFFF5F5F5) // Elevated surfaces
val LightOnSurface = Color(0xFF1A1A1A)      // High contrast text
val LightOnSurfaceVariant = Color(0xFF6B7280) // Medium contrast text
val LightOutline = Color(0xFFE5E7EB)        // Borders and dividers