# QR Spark Privacy Policy

**Last Updated**: [Date]

## Overview
QR Spark is a QR code scanning and generation app that prioritizes your privacy. This policy explains how we handle your information.

## Information We Collect

### Information We DON'T Collect by Default
- Personal information
- Location data
- Contact information
- Device identifiers
- Usage analytics

### Information We MAY Collect (Only if You Enable It)
- **Crash Reports**: If you enable crash reporting in settings, we collect anonymous crash data to improve app stability
- **Usage Analytics**: If you enable analytics in settings, we collect anonymous usage statistics to improve the app

### Local Data Storage
- QR codes you scan or generate are stored locally on your device
- App settings and preferences are stored locally
- No data is transmitted to external servers

## How We Use Information
- Crash reports: To identify and fix app issues
- Analytics: To understand app usage patterns and improve features
- Local data: To provide app functionality and remember your preferences

## Data Sharing
- We do not sell, trade, or share your personal information
- Crash reports and analytics are anonymous and cannot identify you
- Local QR code data never leaves your device

## Your Choices
- All data collection is opt-in through app settings
- You can disable analytics and crash reporting at any time
- You can delete local QR code data through the app
- You can uninstall the app to remove all data

## Data Security
- All data is stored securely on your device
- We use industry-standard security practices
- No sensitive data is transmitted over the internet

## Children's Privacy
QR Spark does not knowingly collect information from children under 13.

## Changes to This Policy
We may update this policy occasionally. Changes will be posted in the app.

## Contact Us
For questions about this privacy policy, contact: <EMAIL>

---
*QR Spark - Your Ultimate QR Companion*
