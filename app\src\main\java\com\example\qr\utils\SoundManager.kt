package com.example.qr.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.SoundPool
import android.media.ToneGenerator
import android.util.Log
import com.example.qr.data.repository.SettingsRepository
import kotlinx.coroutines.flow.first
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Enhanced sound manager for QR Spark app
 * Handles both system tones and custom sound files for scan feedback
 */
@Singleton
class SoundManager @Inject constructor(
    private val context: Context,
    private val settingsRepository: SettingsRepository
) {
    private var soundPool: SoundPool? = null
    private var toneGenerator: ToneGenerator? = null
    private var mediaPlayer: MediaPlayer? = null
    private val audioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    
    // Sound IDs for loaded sounds
    private var scanSuccessSoundId: Int = -1
    private var scanErrorSoundId: Int = -1
    
    companion object {
        private const val TAG = "SoundManager"
        private const val MAX_STREAMS = 3
        private const val SOUND_PRIORITY = 1
        private const val TONE_DURATION_MS = 200
        private const val TONE_VOLUME = 70 // Reduced for more pleasant sound
        private const val PREMIUM_TONE_VOLUME = 65 // Even gentler for premium sounds
    }

    init {
        initializeSoundSystem()
    }

    /**
     * Initialize the sound system with SoundPool and ToneGenerator fallback
     */
    private fun initializeSoundSystem() {
        try {
            // Initialize SoundPool for custom sounds
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_NOTIFICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
                .build()

            soundPool = SoundPool.Builder()
                .setMaxStreams(MAX_STREAMS)
                .setAudioAttributes(audioAttributes)
                .build()

            // Initialize ToneGenerator with premium volume for better sound quality
            toneGenerator = ToneGenerator(AudioManager.STREAM_NOTIFICATION, PREMIUM_TONE_VOLUME)
            
            Log.d(TAG, "Sound system initialized successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing sound system", e)
        }
    }

    /**
     * Load custom sound files (if available)
     * For now, we'll use system tones, but this can be extended with actual sound files
     */
    private fun loadCustomSounds() {
        try {
            soundPool?.let { pool ->
                // These would load actual sound files from res/raw if available
                // scanSuccessSoundId = pool.load(context, R.raw.scan_success, SOUND_PRIORITY)
                // scanErrorSoundId = pool.load(context, R.raw.scan_error, SOUND_PRIORITY)
                
                Log.d(TAG, "Custom sounds would be loaded here")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading custom sounds", e)
        }
    }

    /**
     * Play scan success sound if enabled in settings
     */
    suspend fun playScanSuccessSound() {
        try {
            val soundEnabled = settingsRepository.playSoundOnScan.first()
            if (!soundEnabled) {
                Log.d(TAG, "Sound disabled in settings")
                return
            }

            if (!isAudioEnabled()) {
                Log.d(TAG, "Audio is muted or disabled")
                return
            }

            // Try to play custom sound first, fallback to enhanced system tone
            if (scanSuccessSoundId != -1 && soundPool != null) {
                playCustomSound(scanSuccessSoundId)
            } else {
                toneGenerator?.let { generator ->
                    playEnhancedSuccessSound(generator)
                }
            }

            Log.d(TAG, "Scan success sound played")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing scan success sound", e)
        }
    }

    /**
     * Play scan error sound if enabled in settings
     */
    suspend fun playScanErrorSound() {
        try {
            val soundEnabled = settingsRepository.playSoundOnScan.first()
            if (!soundEnabled) return

            if (!isAudioEnabled()) return

            // Try to play custom sound first, fallback to enhanced error tone
            if (scanErrorSoundId != -1 && soundPool != null) {
                playCustomSound(scanErrorSoundId)
            } else {
                toneGenerator?.let { generator ->
                    playEnhancedErrorSound(generator)
                }
            }

            Log.d(TAG, "Scan error sound played")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing scan error sound", e)
        }
    }

    /**
     * Play a pleasant beep sound for successful scans
     * This creates a brief, pleasant tone programmatically with enhanced quality
     */
    suspend fun playPleasantBeep() {
        try {
            val soundEnabled = settingsRepository.playSoundOnScan.first()
            if (!soundEnabled) return

            if (!isAudioEnabled()) return

            // Create an enhanced pleasant sound pattern
            toneGenerator?.let { generator ->
                // Play a sophisticated success sound pattern
                playEnhancedSuccessSound(generator)
            }

            Log.d(TAG, "Enhanced pleasant beep sound played")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing pleasant beep", e)
        }
    }

    /**
     * Play a single pleasant tone for success
     * Simple, clean, and pleasant single beep
     */
    private fun playEnhancedSuccessSound(generator: ToneGenerator) {
        try {
            // Single pleasant tone - DTMF 8 provides a nice, clear, pleasant sound
            generator.startTone(ToneGenerator.TONE_DTMF_8, 200) // Pleasant single tone

            Log.d(TAG, "Pleasant single tone played")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing enhanced success sound", e)
            // Fallback to simple beep
            generator.startTone(ToneGenerator.TONE_PROP_BEEP, 200)
        }
    }

    /**
     * Play a single gentle error tone
     * Simple, informative single tone for errors
     */
    private fun playEnhancedErrorSound(generator: ToneGenerator) {
        try {
            // Single gentle error tone - DTMF 4 provides a neutral, not harsh sound
            generator.startTone(ToneGenerator.TONE_DTMF_4, 200) // Gentle single error tone

            Log.d(TAG, "Gentle single error tone played")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing enhanced error sound", e)
            // Fallback to simple error tone
            generator.startTone(ToneGenerator.TONE_PROP_NACK, 150)
        }
    }

    /**
     * Test sound playback (used in settings)
     */
    suspend fun testSound() {
        try {
            // Always play test sound regardless of settings
            if (!isAudioEnabled()) {
                Log.d(TAG, "Audio is muted, cannot test sound")
                return
            }

            // Play the same single pleasant tone for testing
            toneGenerator?.let { generator ->
                playEnhancedSuccessSound(generator)
            }
            Log.d(TAG, "Test sound played")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing test sound", e)
        }
    }

    /**
     * Play custom sound using SoundPool
     */
    private fun playCustomSound(soundId: Int) {
        try {
            soundPool?.let { pool ->
                val volume = getCurrentVolume()
                pool.play(soundId, volume, volume, SOUND_PRIORITY, 0, 1.0f)
                Log.d(TAG, "Custom sound played with volume: $volume")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error playing custom sound", e)
        }
    }

    /**
     * Play system tone using ToneGenerator
     */
    private fun playSystemTone(tone: Int) {
        try {
            toneGenerator?.startTone(tone, TONE_DURATION_MS)
            Log.d(TAG, "System tone played: $tone")
        } catch (e: Exception) {
            Log.e(TAG, "Error playing system tone", e)
        }
    }

    /**
     * Check if audio is enabled (not muted, volume > 0)
     */
    private fun isAudioEnabled(): Boolean {
        return try {
            val ringerMode = audioManager.ringerMode
            val notificationVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
            
            val isEnabled = ringerMode != AudioManager.RINGER_MODE_SILENT && 
                           notificationVolume > 0
            
            Log.d(TAG, "Audio enabled: $isEnabled (ringer: $ringerMode, volume: $notificationVolume)")
            isEnabled
        } catch (e: Exception) {
            Log.e(TAG, "Error checking audio state", e)
            true // Default to enabled if we can't check
        }
    }

    /**
     * Get current notification volume as a float between 0.0 and 1.0
     */
    private fun getCurrentVolume(): Float {
        return try {
            val currentVolume = audioManager.getStreamVolume(AudioManager.STREAM_NOTIFICATION)
            val maxVolume = audioManager.getStreamMaxVolume(AudioManager.STREAM_NOTIFICATION)
            if (maxVolume > 0) currentVolume.toFloat() / maxVolume.toFloat() else 0.5f
        } catch (e: Exception) {
            Log.e(TAG, "Error getting current volume", e)
            0.5f // Default volume
        }
    }

    /**
     * Clean up resources
     */
    fun cleanup() {
        try {
            soundPool?.release()
            soundPool = null
            
            toneGenerator?.release()
            toneGenerator = null
            
            mediaPlayer?.release()
            mediaPlayer = null
            
            Log.d(TAG, "Sound manager cleaned up")
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up sound manager", e)
        }
    }
}
