package com.example.qr

import android.app.Application
import android.content.Context
import android.content.res.Configuration
import com.example.qr.data.model.Language
import com.example.qr.utils.LanguageManager

class QRApplication : Application() {

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(base?.let { context ->
            // Apply saved language at application level
            try {
                val sharedPrefs = context.getSharedPreferences("datastore", Context.MODE_PRIVATE)
                val languageCode = sharedPrefs.getString("selected_language", "en") ?: "en"
                val language = Language.getLanguageByCode(languageCode) ?: Language.getSystemLanguage()
                LanguageManager.setLanguage(context, language)
            } catch (e: Exception) {
                // Fallback to default context if language application fails
                context
            }
        } ?: base)
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        // Reapply language when configuration changes
        try {
            val sharedPrefs = getSharedPreferences("datastore", Context.MODE_PRIVATE)
            val languageCode = sharedPrefs.getString("selected_language", "en") ?: "en"
            val language = Language.getLanguageByCode(languageCode) ?: Language.getSystemLanguage()
            LanguageManager.setLanguage(this, language)
        } catch (e: Exception) {
            // Handle error silently
        }
    }
}
